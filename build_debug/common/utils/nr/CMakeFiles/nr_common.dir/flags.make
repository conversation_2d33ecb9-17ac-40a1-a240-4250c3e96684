# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# compile C with /usr/bin/cc
C_DEFINES = -DAVX2 -DCMAKE_BUILD_TYPE=\"Debug\" -DHAVE_NETINET_IN_H -DMAX_NUM_CCs=1 -DNAS_BUILT_IN_UE -DNB_ANTENNAS_RX=4 -DNB_ANTENNAS_TX=4 -DNUMBER_OF_UE_MAX_NB_IoT=16 -DRRC_DEFAULT_RAB_IS_AM -DSTDC_HEADERS="1 -DHAVE_SYS_TYPES_H=1 -DHAVE_SYS_STAT_H=1 -DHAVE_STDLIB_H=1 -DHAVE_STRING_H=1 -DHAVE_MEMORY_H=1 -DHAVE_STRINGS_H=1 -DHAVE_INTTYPES_H=1 -DHAVE_STDINT_H=1 -DHAVE_UNISTD_H=1 -DHAVE_FCNTL_H=1 -DHAVE_ARPA_INET_H=1 -DHAVE_SYS_TIME_H=1 -DHAVE_SYS_SOCKET_H=1 -DHAVE_STRERROR=1 -DHAVE_SOCKET=1 -DHAVE_MEMSET=1 -DHAVE_GETTIMEOFDAY=1 -DHAVE_STDLIB_H=1 -DHAVE_MALLOC=1 -DHAVE_LIBSCTP" -DT_TRACER

C_INCLUDES = -I/home/<USER>/oaiseu/openair3/S1AP -I/home/<USER>/oaiseu/openair3/NGAP -I/home/<USER>/oaiseu/openair2/X2AP -I/home/<USER>/oaiseu/radio/COMMON -I/home/<USER>/oaiseu/build_debug -I/home/<USER>/oaiseu/executables -I/home/<USER>/oaiseu/openair2/COMMON -I/home/<USER>/oaiseu/openair2/UTIL -I/home/<USER>/oaiseu/openair2/UTIL/LOG -I/home/<USER>/oaiseu/openair3/COMMON -I/home/<USER>/oaiseu/openair3/UTILS -I/home/<USER>/oaiseu/nfapi/open-nFAPI/nfapi/public_inc -I/home/<USER>/oaiseu/nfapi/open-nFAPI/common/public_inc -I/home/<USER>/oaiseu/nfapi/open-nFAPI/pnf/public_inc -I/home/<USER>/oaiseu/nfapi/open-nFAPI/nfapi/inc -I/home/<USER>/oaiseu/nfapi/open-nFAPI/sim_common/inc -I/home/<USER>/oaiseu/nfapi/open-nFAPI/pnf_sim/inc -I/home/<USER>/oaiseu/openair1 -I/home/<USER>/oaiseu/openair2 -I/home/<USER>/oaiseu/openair3/NAS/TOOLS -I/home/<USER>/oaiseu/openair2/ENB_APP -I/home/<USER>/oaiseu/openair2/GNB_APP -I/home/<USER>/oaiseu/openair2/MCE_APP -I/home/<USER>/oaiseu/openair2/LAYER2/RLC -I/home/<USER>/oaiseu/openair2/LAYER2/PDCP_v10.1.0 -I/home/<USER>/oaiseu/openair2/RRC/LTE/MESSAGES -I/home/<USER>/oaiseu/openair2/RRC/LTE -I/home/<USER>/oaiseu/common/utils -I/home/<USER>/oaiseu/common/utils/collection -I/home/<USER>/oaiseu/common/utils/ocp_itti -I/home/<USER>/oaiseu/openair3/NAS/COMMON -I/home/<USER>/oaiseu/openair3/NAS/COMMON/API/NETWORK -I/home/<USER>/oaiseu/openair3/NAS/COMMON/EMM/MSG -I/home/<USER>/oaiseu/openair3/NAS/COMMON/ESM/MSG -I/home/<USER>/oaiseu/openair3/NAS/UE/ESM -I/home/<USER>/oaiseu/openair3/NAS/UE/EMM -I/home/<USER>/oaiseu/openair3/NAS/UE/API/USER -I/home/<USER>/oaiseu/openair3/NAS/COMMON/IES -I/home/<USER>/oaiseu/openair3/NAS/COMMON/UTIL -I/home/<USER>/oaiseu/openair3/SECU -I/home/<USER>/oaiseu/openair3/SCTP -I/home/<USER>/oaiseu/openair2/M2AP -I/home/<USER>/oaiseu/openair2/F1AP -I/home/<USER>/oaiseu/openair3/ocp-gtpu -I/home/<USER>/oaiseu/openair3/M3AP -I/home/<USER>/oaiseu/openair3/MME_APP -I/home/<USER>/oaiseu/openair2/UTIL/OSA -I/home/<USER>/oaiseu/openair2/UTIL/MEM -I/home/<USER>/oaiseu/openair2/UTIL/LISTS -I/home/<USER>/oaiseu/openair2/UTIL/FIFO -I/home/<USER>/oaiseu/openair2/UTIL/MATH -I/home/<USER>/oaiseu/openair2/UTIL/TIMER -I/home/<USER>/oaiseu/openair2/UTIL/OTG -I/home/<USER>/oaiseu/openair2/UTIL/OPT -I/home/<USER>/oaiseu -I/home/<USER>/oaiseu/common/utils/T -I/home/<USER>/oaiseu/build_debug/common/utils/T -I/home/<USER>/oaiseu/common/utils/hashtable -I/home/<USER>/oaiseu/nfapi/open-nFAPI/pnf/inc -I/home/<USER>/oaiseu/nfapi/open-nFAPI/vnf/public_inc -I/home/<USER>/oaiseu/nfapi/open-nFAPI/vnf/inc -I/home/<USER>/oaiseu/nfapi/oai_integration -I/home/<USER>/oaiseu/openair2/NR_UE_PHY_INTERFACE -I/home/<USER>/oaiseu/openair2/LAYER2 -I/home/<USER>/oaiseu/openair1/SCHED_NR_UE -I/home/<USER>/oaiseu/openair3/ocp-gtp -I/home/<USER>/oaiseu/openair3/NAS/NR_UE -I/home/<USER>/oaiseu/openair3/NAS/UE -I/home/<USER>/oaiseu/openair3/NAS/UE/API/USIM -I/home/<USER>/oaiseu/openair3/NAS/UE/EMM/MSG -I/home/<USER>/oaiseu/openair3/NAS/UE/EMM/SAP -I/home/<USER>/oaiseu/openair3/NAS/UE/ESM/SAP -I/home/<USER>/oaiseu/common/utils/nr/. -I/home/<USER>/oaiseu/common/utils/LOG/. -I/home/<USER>/oaiseu/common/utils/threadPool/. -I/home/<USER>/oaiseu/common/utils/. -I/home/<USER>/oaiseu/common/utils/barrier/. -I/home/<USER>/oaiseu/common/utils/actor/. -I/home/<USER>/oaiseu/common/utils/actor/../threadPool -I/home/<USER>/oaiseu/common/.

C_FLAGS =  -DSIMDE_X86_AVX_NATIVE -DSIMDE_X86_AVX_NATIVE -DSIMDE_X86_F16C_NATIVE -DSIMDE_X86_FMA_NATIVE -DSIMDE_X86_GFNI_NATIVE -DSIMDE_X86_MMX_NATIVE -DSIMDE_X86_PCLMUL_NATIVE -DSIMDE_X86_SSE2_NATIVE -DSIMDE_X86_SSE3_NATIVE -DSIMDE_X86_SSE_NATIVE -DSIMDE_X86_XOP_HAVE_COM_ -DSIMDE_X86_XOP_NATIVE -mno-avx512f -march=native -DSIMDE_X86_AVX2_NATIVE -DSIMDE_X86_VPCLMULQDQ_NATIVE -march=native -pipe -fPIC -Wall -fno-strict-aliasing -rdynamic -Wno-packed-bitfield-compat -std=gnu11 -funroll-loops  -ggdb2 -DMALLOC_CHECK_=3 -fno-delete-null-pointer-checks -O0   -DASN_DISABLE_OER_SUPPORT -DHAVE_CONFIG_H -DHAVE_CONFIG_H_

