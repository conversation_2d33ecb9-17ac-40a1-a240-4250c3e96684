# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/oaiseu

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/oaiseu/build_debug

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/oaiseu/build_debug/CMakeFiles /home/<USER>/oaiseu/build_debug/common/utils/time_manager//CMakeFiles/progress.marks
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 common/utils/time_manager/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/oaiseu/build_debug/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 common/utils/time_manager/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 common/utils/time_manager/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 common/utils/time_manager/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
common/utils/time_manager/CMakeFiles/time_management.dir/rule:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 common/utils/time_manager/CMakeFiles/time_management.dir/rule
.PHONY : common/utils/time_manager/CMakeFiles/time_management.dir/rule

# Convenience name for target.
time_management: common/utils/time_manager/CMakeFiles/time_management.dir/rule
.PHONY : time_management

# fast build rule for target.
time_management/fast:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/time_manager/CMakeFiles/time_management.dir/build.make common/utils/time_manager/CMakeFiles/time_management.dir/build
.PHONY : time_management/fast

# Convenience name for target.
common/utils/time_manager/CMakeFiles/time_management_core.dir/rule:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 common/utils/time_manager/CMakeFiles/time_management_core.dir/rule
.PHONY : common/utils/time_manager/CMakeFiles/time_management_core.dir/rule

# Convenience name for target.
time_management_core: common/utils/time_manager/CMakeFiles/time_management_core.dir/rule
.PHONY : time_management_core

# fast build rule for target.
time_management_core/fast:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/time_manager/CMakeFiles/time_management_core.dir/build.make common/utils/time_manager/CMakeFiles/time_management_core.dir/build
.PHONY : time_management_core/fast

time_client.o: time_client.c.o
.PHONY : time_client.o

# target to build an object file
time_client.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/time_manager/CMakeFiles/time_management_core.dir/build.make common/utils/time_manager/CMakeFiles/time_management_core.dir/time_client.c.o
.PHONY : time_client.c.o

time_client.i: time_client.c.i
.PHONY : time_client.i

# target to preprocess a source file
time_client.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/time_manager/CMakeFiles/time_management_core.dir/build.make common/utils/time_manager/CMakeFiles/time_management_core.dir/time_client.c.i
.PHONY : time_client.c.i

time_client.s: time_client.c.s
.PHONY : time_client.s

# target to generate assembly for a file
time_client.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/time_manager/CMakeFiles/time_management_core.dir/build.make common/utils/time_manager/CMakeFiles/time_management_core.dir/time_client.c.s
.PHONY : time_client.c.s

time_manager.o: time_manager.c.o
.PHONY : time_manager.o

# target to build an object file
time_manager.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/time_manager/CMakeFiles/time_management.dir/build.make common/utils/time_manager/CMakeFiles/time_management.dir/time_manager.c.o
.PHONY : time_manager.c.o

time_manager.i: time_manager.c.i
.PHONY : time_manager.i

# target to preprocess a source file
time_manager.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/time_manager/CMakeFiles/time_management.dir/build.make common/utils/time_manager/CMakeFiles/time_management.dir/time_manager.c.i
.PHONY : time_manager.c.i

time_manager.s: time_manager.c.s
.PHONY : time_manager.s

# target to generate assembly for a file
time_manager.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/time_manager/CMakeFiles/time_management.dir/build.make common/utils/time_manager/CMakeFiles/time_management.dir/time_manager.c.s
.PHONY : time_manager.c.s

time_server.o: time_server.c.o
.PHONY : time_server.o

# target to build an object file
time_server.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/time_manager/CMakeFiles/time_management_core.dir/build.make common/utils/time_manager/CMakeFiles/time_management_core.dir/time_server.c.o
.PHONY : time_server.c.o

time_server.i: time_server.c.i
.PHONY : time_server.i

# target to preprocess a source file
time_server.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/time_manager/CMakeFiles/time_management_core.dir/build.make common/utils/time_manager/CMakeFiles/time_management_core.dir/time_server.c.i
.PHONY : time_server.c.i

time_server.s: time_server.c.s
.PHONY : time_server.s

# target to generate assembly for a file
time_server.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/time_manager/CMakeFiles/time_management_core.dir/build.make common/utils/time_manager/CMakeFiles/time_management_core.dir/time_server.c.s
.PHONY : time_server.c.s

time_source.o: time_source.c.o
.PHONY : time_source.o

# target to build an object file
time_source.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/time_manager/CMakeFiles/time_management_core.dir/build.make common/utils/time_manager/CMakeFiles/time_management_core.dir/time_source.c.o
.PHONY : time_source.c.o

time_source.i: time_source.c.i
.PHONY : time_source.i

# target to preprocess a source file
time_source.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/time_manager/CMakeFiles/time_management_core.dir/build.make common/utils/time_manager/CMakeFiles/time_management_core.dir/time_source.c.i
.PHONY : time_source.c.i

time_source.s: time_source.c.s
.PHONY : time_source.s

# target to generate assembly for a file
time_source.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/time_manager/CMakeFiles/time_management_core.dir/build.make common/utils/time_manager/CMakeFiles/time_management_core.dir/time_source.c.s
.PHONY : time_source.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... time_management"
	@echo "... time_management_core"
	@echo "... time_client.o"
	@echo "... time_client.i"
	@echo "... time_client.s"
	@echo "... time_manager.o"
	@echo "... time_manager.i"
	@echo "... time_manager.s"
	@echo "... time_server.o"
	@echo "... time_server.i"
	@echo "... time_server.s"
	@echo "... time_source.o"
	@echo "... time_source.i"
	@echo "... time_source.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

