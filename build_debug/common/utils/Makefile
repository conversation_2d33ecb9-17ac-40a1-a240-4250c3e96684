# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/oaiseu

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/oaiseu/build_debug

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/oaiseu/build_debug/CMakeFiles /home/<USER>/oaiseu/build_debug/common/utils//CMakeFiles/progress.marks
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 common/utils/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/oaiseu/build_debug/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 common/utils/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 common/utils/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 common/utils/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
common/utils/CMakeFiles/utils.dir/rule:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 common/utils/CMakeFiles/utils.dir/rule
.PHONY : common/utils/CMakeFiles/utils.dir/rule

# Convenience name for target.
utils: common/utils/CMakeFiles/utils.dir/rule
.PHONY : utils

# fast build rule for target.
utils/fast:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/CMakeFiles/utils.dir/build.make common/utils/CMakeFiles/utils.dir/build
.PHONY : utils/fast

system.o: system.c.o
.PHONY : system.o

# target to build an object file
system.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/CMakeFiles/utils.dir/build.make common/utils/CMakeFiles/utils.dir/system.c.o
.PHONY : system.c.o

system.i: system.c.i
.PHONY : system.i

# target to preprocess a source file
system.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/CMakeFiles/utils.dir/build.make common/utils/CMakeFiles/utils.dir/system.c.i
.PHONY : system.c.i

system.s: system.c.s
.PHONY : system.s

# target to generate assembly for a file
system.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/CMakeFiles/utils.dir/build.make common/utils/CMakeFiles/utils.dir/system.c.s
.PHONY : system.c.s

time_meas.o: time_meas.c.o
.PHONY : time_meas.o

# target to build an object file
time_meas.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/CMakeFiles/utils.dir/build.make common/utils/CMakeFiles/utils.dir/time_meas.c.o
.PHONY : time_meas.c.o

time_meas.i: time_meas.c.i
.PHONY : time_meas.i

# target to preprocess a source file
time_meas.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/CMakeFiles/utils.dir/build.make common/utils/CMakeFiles/utils.dir/time_meas.c.i
.PHONY : time_meas.c.i

time_meas.s: time_meas.c.s
.PHONY : time_meas.s

# target to generate assembly for a file
time_meas.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/CMakeFiles/utils.dir/build.make common/utils/CMakeFiles/utils.dir/time_meas.c.s
.PHONY : time_meas.c.s

time_stat.o: time_stat.c.o
.PHONY : time_stat.o

# target to build an object file
time_stat.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/CMakeFiles/utils.dir/build.make common/utils/CMakeFiles/utils.dir/time_stat.c.o
.PHONY : time_stat.c.o

time_stat.i: time_stat.c.i
.PHONY : time_stat.i

# target to preprocess a source file
time_stat.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/CMakeFiles/utils.dir/build.make common/utils/CMakeFiles/utils.dir/time_stat.c.i
.PHONY : time_stat.c.i

time_stat.s: time_stat.c.s
.PHONY : time_stat.s

# target to generate assembly for a file
time_stat.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/CMakeFiles/utils.dir/build.make common/utils/CMakeFiles/utils.dir/time_stat.c.s
.PHONY : time_stat.c.s

tun_if.o: tun_if.c.o
.PHONY : tun_if.o

# target to build an object file
tun_if.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/CMakeFiles/utils.dir/build.make common/utils/CMakeFiles/utils.dir/tun_if.c.o
.PHONY : tun_if.c.o

tun_if.i: tun_if.c.i
.PHONY : tun_if.i

# target to preprocess a source file
tun_if.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/CMakeFiles/utils.dir/build.make common/utils/CMakeFiles/utils.dir/tun_if.c.i
.PHONY : tun_if.c.i

tun_if.s: tun_if.c.s
.PHONY : tun_if.s

# target to generate assembly for a file
tun_if.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/CMakeFiles/utils.dir/build.make common/utils/CMakeFiles/utils.dir/tun_if.c.s
.PHONY : tun_if.c.s

utils.o: utils.c.o
.PHONY : utils.o

# target to build an object file
utils.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/CMakeFiles/utils.dir/build.make common/utils/CMakeFiles/utils.dir/utils.c.o
.PHONY : utils.c.o

utils.i: utils.c.i
.PHONY : utils.i

# target to preprocess a source file
utils.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/CMakeFiles/utils.dir/build.make common/utils/CMakeFiles/utils.dir/utils.c.i
.PHONY : utils.c.i

utils.s: utils.c.s
.PHONY : utils.s

# target to generate assembly for a file
utils.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/CMakeFiles/utils.dir/build.make common/utils/CMakeFiles/utils.dir/utils.c.s
.PHONY : utils.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... utils"
	@echo "... system.o"
	@echo "... system.i"
	@echo "... system.s"
	@echo "... time_meas.o"
	@echo "... time_meas.i"
	@echo "... time_meas.s"
	@echo "... time_stat.o"
	@echo "... time_stat.i"
	@echo "... time_stat.s"
	@echo "... tun_if.o"
	@echo "... tun_if.i"
	@echo "... tun_if.s"
	@echo "... utils.o"
	@echo "... utils.i"
	@echo "... utils.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

