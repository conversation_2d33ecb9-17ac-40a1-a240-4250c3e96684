# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/oaiseu

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/oaiseu/build_debug

# Include any dependencies generated for this target.
include common/utils/threadPool/CMakeFiles/thread-pool.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include common/utils/threadPool/CMakeFiles/thread-pool.dir/compiler_depend.make

# Include the progress variables for this target.
include common/utils/threadPool/CMakeFiles/thread-pool.dir/progress.make

# Include the compile flags for this target's objects.
include common/utils/threadPool/CMakeFiles/thread-pool.dir/flags.make

common/utils/threadPool/CMakeFiles/thread-pool.dir/thread-pool.c.o: common/utils/threadPool/CMakeFiles/thread-pool.dir/flags.make
common/utils/threadPool/CMakeFiles/thread-pool.dir/thread-pool.c.o: ../common/utils/threadPool/thread-pool.c
common/utils/threadPool/CMakeFiles/thread-pool.dir/thread-pool.c.o: common/utils/threadPool/CMakeFiles/thread-pool.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object common/utils/threadPool/CMakeFiles/thread-pool.dir/thread-pool.c.o"
	cd /home/<USER>/oaiseu/build_debug/common/utils/threadPool && /usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT common/utils/threadPool/CMakeFiles/thread-pool.dir/thread-pool.c.o -MF CMakeFiles/thread-pool.dir/thread-pool.c.o.d -o CMakeFiles/thread-pool.dir/thread-pool.c.o -c /home/<USER>/oaiseu/common/utils/threadPool/thread-pool.c

common/utils/threadPool/CMakeFiles/thread-pool.dir/thread-pool.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/thread-pool.dir/thread-pool.c.i"
	cd /home/<USER>/oaiseu/build_debug/common/utils/threadPool && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/oaiseu/common/utils/threadPool/thread-pool.c > CMakeFiles/thread-pool.dir/thread-pool.c.i

common/utils/threadPool/CMakeFiles/thread-pool.dir/thread-pool.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/thread-pool.dir/thread-pool.c.s"
	cd /home/<USER>/oaiseu/build_debug/common/utils/threadPool && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/oaiseu/common/utils/threadPool/thread-pool.c -o CMakeFiles/thread-pool.dir/thread-pool.c.s

common/utils/threadPool/CMakeFiles/thread-pool.dir/task_ans.c.o: common/utils/threadPool/CMakeFiles/thread-pool.dir/flags.make
common/utils/threadPool/CMakeFiles/thread-pool.dir/task_ans.c.o: ../common/utils/threadPool/task_ans.c
common/utils/threadPool/CMakeFiles/thread-pool.dir/task_ans.c.o: common/utils/threadPool/CMakeFiles/thread-pool.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object common/utils/threadPool/CMakeFiles/thread-pool.dir/task_ans.c.o"
	cd /home/<USER>/oaiseu/build_debug/common/utils/threadPool && /usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT common/utils/threadPool/CMakeFiles/thread-pool.dir/task_ans.c.o -MF CMakeFiles/thread-pool.dir/task_ans.c.o.d -o CMakeFiles/thread-pool.dir/task_ans.c.o -c /home/<USER>/oaiseu/common/utils/threadPool/task_ans.c

common/utils/threadPool/CMakeFiles/thread-pool.dir/task_ans.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/thread-pool.dir/task_ans.c.i"
	cd /home/<USER>/oaiseu/build_debug/common/utils/threadPool && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/oaiseu/common/utils/threadPool/task_ans.c > CMakeFiles/thread-pool.dir/task_ans.c.i

common/utils/threadPool/CMakeFiles/thread-pool.dir/task_ans.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/thread-pool.dir/task_ans.c.s"
	cd /home/<USER>/oaiseu/build_debug/common/utils/threadPool && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/oaiseu/common/utils/threadPool/task_ans.c -o CMakeFiles/thread-pool.dir/task_ans.c.s

# Object files for target thread-pool
thread__pool_OBJECTS = \
"CMakeFiles/thread-pool.dir/thread-pool.c.o" \
"CMakeFiles/thread-pool.dir/task_ans.c.o"

# External object files for target thread-pool
thread__pool_EXTERNAL_OBJECTS =

common/utils/threadPool/libthread-pool.a: common/utils/threadPool/CMakeFiles/thread-pool.dir/thread-pool.c.o
common/utils/threadPool/libthread-pool.a: common/utils/threadPool/CMakeFiles/thread-pool.dir/task_ans.c.o
common/utils/threadPool/libthread-pool.a: common/utils/threadPool/CMakeFiles/thread-pool.dir/build.make
common/utils/threadPool/libthread-pool.a: common/utils/threadPool/CMakeFiles/thread-pool.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking C static library libthread-pool.a"
	cd /home/<USER>/oaiseu/build_debug/common/utils/threadPool && $(CMAKE_COMMAND) -P CMakeFiles/thread-pool.dir/cmake_clean_target.cmake
	cd /home/<USER>/oaiseu/build_debug/common/utils/threadPool && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/thread-pool.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
common/utils/threadPool/CMakeFiles/thread-pool.dir/build: common/utils/threadPool/libthread-pool.a
.PHONY : common/utils/threadPool/CMakeFiles/thread-pool.dir/build

common/utils/threadPool/CMakeFiles/thread-pool.dir/clean:
	cd /home/<USER>/oaiseu/build_debug/common/utils/threadPool && $(CMAKE_COMMAND) -P CMakeFiles/thread-pool.dir/cmake_clean.cmake
.PHONY : common/utils/threadPool/CMakeFiles/thread-pool.dir/clean

common/utils/threadPool/CMakeFiles/thread-pool.dir/depend:
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/oaiseu /home/<USER>/oaiseu/common/utils/threadPool /home/<USER>/oaiseu/build_debug /home/<USER>/oaiseu/build_debug/common/utils/threadPool /home/<USER>/oaiseu/build_debug/common/utils/threadPool/CMakeFiles/thread-pool.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : common/utils/threadPool/CMakeFiles/thread-pool.dir/depend

