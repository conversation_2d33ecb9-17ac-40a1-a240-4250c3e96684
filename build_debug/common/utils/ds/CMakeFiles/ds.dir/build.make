# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/oaiseu

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/oaiseu/build_debug

# Include any dependencies generated for this target.
include common/utils/ds/CMakeFiles/ds.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include common/utils/ds/CMakeFiles/ds.dir/compiler_depend.make

# Include the progress variables for this target.
include common/utils/ds/CMakeFiles/ds.dir/progress.make

# Include the compile flags for this target's objects.
include common/utils/ds/CMakeFiles/ds.dir/flags.make

common/utils/ds/CMakeFiles/ds.dir/byte_array.c.o: common/utils/ds/CMakeFiles/ds.dir/flags.make
common/utils/ds/CMakeFiles/ds.dir/byte_array.c.o: ../common/utils/ds/byte_array.c
common/utils/ds/CMakeFiles/ds.dir/byte_array.c.o: common/utils/ds/CMakeFiles/ds.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object common/utils/ds/CMakeFiles/ds.dir/byte_array.c.o"
	cd /home/<USER>/oaiseu/build_debug/common/utils/ds && /usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT common/utils/ds/CMakeFiles/ds.dir/byte_array.c.o -MF CMakeFiles/ds.dir/byte_array.c.o.d -o CMakeFiles/ds.dir/byte_array.c.o -c /home/<USER>/oaiseu/common/utils/ds/byte_array.c

common/utils/ds/CMakeFiles/ds.dir/byte_array.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/ds.dir/byte_array.c.i"
	cd /home/<USER>/oaiseu/build_debug/common/utils/ds && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/oaiseu/common/utils/ds/byte_array.c > CMakeFiles/ds.dir/byte_array.c.i

common/utils/ds/CMakeFiles/ds.dir/byte_array.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/ds.dir/byte_array.c.s"
	cd /home/<USER>/oaiseu/build_debug/common/utils/ds && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/oaiseu/common/utils/ds/byte_array.c -o CMakeFiles/ds.dir/byte_array.c.s

common/utils/ds/CMakeFiles/ds.dir/seq_arr.c.o: common/utils/ds/CMakeFiles/ds.dir/flags.make
common/utils/ds/CMakeFiles/ds.dir/seq_arr.c.o: ../common/utils/ds/seq_arr.c
common/utils/ds/CMakeFiles/ds.dir/seq_arr.c.o: common/utils/ds/CMakeFiles/ds.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object common/utils/ds/CMakeFiles/ds.dir/seq_arr.c.o"
	cd /home/<USER>/oaiseu/build_debug/common/utils/ds && /usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT common/utils/ds/CMakeFiles/ds.dir/seq_arr.c.o -MF CMakeFiles/ds.dir/seq_arr.c.o.d -o CMakeFiles/ds.dir/seq_arr.c.o -c /home/<USER>/oaiseu/common/utils/ds/seq_arr.c

common/utils/ds/CMakeFiles/ds.dir/seq_arr.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/ds.dir/seq_arr.c.i"
	cd /home/<USER>/oaiseu/build_debug/common/utils/ds && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/oaiseu/common/utils/ds/seq_arr.c > CMakeFiles/ds.dir/seq_arr.c.i

common/utils/ds/CMakeFiles/ds.dir/seq_arr.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/ds.dir/seq_arr.c.s"
	cd /home/<USER>/oaiseu/build_debug/common/utils/ds && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/oaiseu/common/utils/ds/seq_arr.c -o CMakeFiles/ds.dir/seq_arr.c.s

ds: common/utils/ds/CMakeFiles/ds.dir/byte_array.c.o
ds: common/utils/ds/CMakeFiles/ds.dir/seq_arr.c.o
ds: common/utils/ds/CMakeFiles/ds.dir/build.make
.PHONY : ds

# Rule to build all files generated by this target.
common/utils/ds/CMakeFiles/ds.dir/build: ds
.PHONY : common/utils/ds/CMakeFiles/ds.dir/build

common/utils/ds/CMakeFiles/ds.dir/clean:
	cd /home/<USER>/oaiseu/build_debug/common/utils/ds && $(CMAKE_COMMAND) -P CMakeFiles/ds.dir/cmake_clean.cmake
.PHONY : common/utils/ds/CMakeFiles/ds.dir/clean

common/utils/ds/CMakeFiles/ds.dir/depend:
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/oaiseu /home/<USER>/oaiseu/common/utils/ds /home/<USER>/oaiseu/build_debug /home/<USER>/oaiseu/build_debug/common/utils/ds /home/<USER>/oaiseu/build_debug/common/utils/ds/CMakeFiles/ds.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : common/utils/ds/CMakeFiles/ds.dir/depend

