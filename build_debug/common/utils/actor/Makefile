# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/oaiseu

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/oaiseu/build_debug

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/oaiseu/build_debug/CMakeFiles /home/<USER>/oaiseu/build_debug/common/utils/actor//CMakeFiles/progress.marks
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 common/utils/actor/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/oaiseu/build_debug/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 common/utils/actor/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 common/utils/actor/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 common/utils/actor/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
common/utils/actor/CMakeFiles/actor.dir/rule:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 common/utils/actor/CMakeFiles/actor.dir/rule
.PHONY : common/utils/actor/CMakeFiles/actor.dir/rule

# Convenience name for target.
actor: common/utils/actor/CMakeFiles/actor.dir/rule
.PHONY : actor

# fast build rule for target.
actor/fast:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/actor/CMakeFiles/actor.dir/build.make common/utils/actor/CMakeFiles/actor.dir/build
.PHONY : actor/fast

actor.o: actor.c.o
.PHONY : actor.o

# target to build an object file
actor.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/actor/CMakeFiles/actor.dir/build.make common/utils/actor/CMakeFiles/actor.dir/actor.c.o
.PHONY : actor.c.o

actor.i: actor.c.i
.PHONY : actor.i

# target to preprocess a source file
actor.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/actor/CMakeFiles/actor.dir/build.make common/utils/actor/CMakeFiles/actor.dir/actor.c.i
.PHONY : actor.c.i

actor.s: actor.c.s
.PHONY : actor.s

# target to generate assembly for a file
actor.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/actor/CMakeFiles/actor.dir/build.make common/utils/actor/CMakeFiles/actor.dir/actor.c.s
.PHONY : actor.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... actor"
	@echo "... actor.o"
	@echo "... actor.i"
	@echo "... actor.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

