# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/oaiseu

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/oaiseu/build_debug

# Include any dependencies generated for this target.
include common/utils/T/CMakeFiles/_check_vcd.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include common/utils/T/CMakeFiles/_check_vcd.dir/compiler_depend.make

# Include the progress variables for this target.
include common/utils/T/CMakeFiles/_check_vcd.dir/progress.make

# Include the compile flags for this target's objects.
include common/utils/T/CMakeFiles/_check_vcd.dir/flags.make

common/utils/T/T_IDs.h: common/utils/T/genids
common/utils/T/T_IDs.h: ../common/utils/T/T_messages.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Generating T_IDs.h from T_messages.txt"
	cd /home/<USER>/oaiseu/build_debug/common/utils/T && ./genids /home/<USER>/oaiseu/common/utils/T/T_messages.txt T_IDs.h

common/utils/T/T_messages.txt.h: ../common/utils/T/T_messages.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Generating T_messages.txt.h from T_messages.txt"
	cd /home/<USER>/oaiseu/common/utils/T && xxd -i T_messages.txt /home/<USER>/oaiseu/build_debug/common/utils/T/T_messages.txt.h

common/utils/T/CMakeFiles/_check_vcd.dir/check_vcd.c.o: common/utils/T/CMakeFiles/_check_vcd.dir/flags.make
common/utils/T/CMakeFiles/_check_vcd.dir/check_vcd.c.o: ../common/utils/T/check_vcd.c
common/utils/T/CMakeFiles/_check_vcd.dir/check_vcd.c.o: common/utils/T/CMakeFiles/_check_vcd.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object common/utils/T/CMakeFiles/_check_vcd.dir/check_vcd.c.o"
	cd /home/<USER>/oaiseu/build_debug/common/utils/T && /usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT common/utils/T/CMakeFiles/_check_vcd.dir/check_vcd.c.o -MF CMakeFiles/_check_vcd.dir/check_vcd.c.o.d -o CMakeFiles/_check_vcd.dir/check_vcd.c.o -c /home/<USER>/oaiseu/common/utils/T/check_vcd.c

common/utils/T/CMakeFiles/_check_vcd.dir/check_vcd.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/_check_vcd.dir/check_vcd.c.i"
	cd /home/<USER>/oaiseu/build_debug/common/utils/T && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/oaiseu/common/utils/T/check_vcd.c > CMakeFiles/_check_vcd.dir/check_vcd.c.i

common/utils/T/CMakeFiles/_check_vcd.dir/check_vcd.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/_check_vcd.dir/check_vcd.c.s"
	cd /home/<USER>/oaiseu/build_debug/common/utils/T && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/oaiseu/common/utils/T/check_vcd.c -o CMakeFiles/_check_vcd.dir/check_vcd.c.s

common/utils/T/CMakeFiles/_check_vcd.dir/tracer/database.c.o: common/utils/T/CMakeFiles/_check_vcd.dir/flags.make
common/utils/T/CMakeFiles/_check_vcd.dir/tracer/database.c.o: ../common/utils/T/tracer/database.c
common/utils/T/CMakeFiles/_check_vcd.dir/tracer/database.c.o: common/utils/T/CMakeFiles/_check_vcd.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object common/utils/T/CMakeFiles/_check_vcd.dir/tracer/database.c.o"
	cd /home/<USER>/oaiseu/build_debug/common/utils/T && /usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT common/utils/T/CMakeFiles/_check_vcd.dir/tracer/database.c.o -MF CMakeFiles/_check_vcd.dir/tracer/database.c.o.d -o CMakeFiles/_check_vcd.dir/tracer/database.c.o -c /home/<USER>/oaiseu/common/utils/T/tracer/database.c

common/utils/T/CMakeFiles/_check_vcd.dir/tracer/database.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/_check_vcd.dir/tracer/database.c.i"
	cd /home/<USER>/oaiseu/build_debug/common/utils/T && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/oaiseu/common/utils/T/tracer/database.c > CMakeFiles/_check_vcd.dir/tracer/database.c.i

common/utils/T/CMakeFiles/_check_vcd.dir/tracer/database.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/_check_vcd.dir/tracer/database.c.s"
	cd /home/<USER>/oaiseu/build_debug/common/utils/T && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/oaiseu/common/utils/T/tracer/database.c -o CMakeFiles/_check_vcd.dir/tracer/database.c.s

common/utils/T/CMakeFiles/_check_vcd.dir/tracer/utils.c.o: common/utils/T/CMakeFiles/_check_vcd.dir/flags.make
common/utils/T/CMakeFiles/_check_vcd.dir/tracer/utils.c.o: ../common/utils/T/tracer/utils.c
common/utils/T/CMakeFiles/_check_vcd.dir/tracer/utils.c.o: common/utils/T/CMakeFiles/_check_vcd.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object common/utils/T/CMakeFiles/_check_vcd.dir/tracer/utils.c.o"
	cd /home/<USER>/oaiseu/build_debug/common/utils/T && /usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT common/utils/T/CMakeFiles/_check_vcd.dir/tracer/utils.c.o -MF CMakeFiles/_check_vcd.dir/tracer/utils.c.o.d -o CMakeFiles/_check_vcd.dir/tracer/utils.c.o -c /home/<USER>/oaiseu/common/utils/T/tracer/utils.c

common/utils/T/CMakeFiles/_check_vcd.dir/tracer/utils.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/_check_vcd.dir/tracer/utils.c.i"
	cd /home/<USER>/oaiseu/build_debug/common/utils/T && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/oaiseu/common/utils/T/tracer/utils.c > CMakeFiles/_check_vcd.dir/tracer/utils.c.i

common/utils/T/CMakeFiles/_check_vcd.dir/tracer/utils.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/_check_vcd.dir/tracer/utils.c.s"
	cd /home/<USER>/oaiseu/build_debug/common/utils/T && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/oaiseu/common/utils/T/tracer/utils.c -o CMakeFiles/_check_vcd.dir/tracer/utils.c.s

# Object files for target _check_vcd
_check_vcd_OBJECTS = \
"CMakeFiles/_check_vcd.dir/check_vcd.c.o" \
"CMakeFiles/_check_vcd.dir/tracer/database.c.o" \
"CMakeFiles/_check_vcd.dir/tracer/utils.c.o"

# External object files for target _check_vcd
_check_vcd_EXTERNAL_OBJECTS =

common/utils/T/_check_vcd: common/utils/T/CMakeFiles/_check_vcd.dir/check_vcd.c.o
common/utils/T/_check_vcd: common/utils/T/CMakeFiles/_check_vcd.dir/tracer/database.c.o
common/utils/T/_check_vcd: common/utils/T/CMakeFiles/_check_vcd.dir/tracer/utils.c.o
common/utils/T/_check_vcd: common/utils/T/CMakeFiles/_check_vcd.dir/build.make
common/utils/T/_check_vcd: common/utils/T/CMakeFiles/_check_vcd.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Linking C executable _check_vcd"
	cd /home/<USER>/oaiseu/build_debug/common/utils/T && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/_check_vcd.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
common/utils/T/CMakeFiles/_check_vcd.dir/build: common/utils/T/_check_vcd
.PHONY : common/utils/T/CMakeFiles/_check_vcd.dir/build

common/utils/T/CMakeFiles/_check_vcd.dir/clean:
	cd /home/<USER>/oaiseu/build_debug/common/utils/T && $(CMAKE_COMMAND) -P CMakeFiles/_check_vcd.dir/cmake_clean.cmake
.PHONY : common/utils/T/CMakeFiles/_check_vcd.dir/clean

common/utils/T/CMakeFiles/_check_vcd.dir/depend: common/utils/T/T_IDs.h
common/utils/T/CMakeFiles/_check_vcd.dir/depend: common/utils/T/T_messages.txt.h
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/oaiseu /home/<USER>/oaiseu/common/utils/T /home/<USER>/oaiseu/build_debug /home/<USER>/oaiseu/build_debug/common/utils/T /home/<USER>/oaiseu/build_debug/common/utils/T/CMakeFiles/_check_vcd.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : common/utils/T/CMakeFiles/_check_vcd.dir/depend

