# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/oaiseu

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/oaiseu/build_debug

# Include any dependencies generated for this target.
include common/utils/T/CMakeFiles/T.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include common/utils/T/CMakeFiles/T.dir/compiler_depend.make

# Include the progress variables for this target.
include common/utils/T/CMakeFiles/T.dir/progress.make

# Include the compile flags for this target's objects.
include common/utils/T/CMakeFiles/T.dir/flags.make

common/utils/T/T_IDs.h: common/utils/T/genids
common/utils/T/T_IDs.h: ../common/utils/T/T_messages.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Generating T_IDs.h from T_messages.txt"
	cd /home/<USER>/oaiseu/build_debug/common/utils/T && ./genids /home/<USER>/oaiseu/common/utils/T/T_messages.txt T_IDs.h

common/utils/T/T_messages.txt.h: ../common/utils/T/T_messages.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Generating T_messages.txt.h from T_messages.txt"
	cd /home/<USER>/oaiseu/common/utils/T && xxd -i T_messages.txt /home/<USER>/oaiseu/build_debug/common/utils/T/T_messages.txt.h

common/utils/T/CMakeFiles/T.dir/T.c.o: common/utils/T/CMakeFiles/T.dir/flags.make
common/utils/T/CMakeFiles/T.dir/T.c.o: ../common/utils/T/T.c
common/utils/T/CMakeFiles/T.dir/T.c.o: common/utils/T/CMakeFiles/T.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object common/utils/T/CMakeFiles/T.dir/T.c.o"
	cd /home/<USER>/oaiseu/build_debug/common/utils/T && /usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT common/utils/T/CMakeFiles/T.dir/T.c.o -MF CMakeFiles/T.dir/T.c.o.d -o CMakeFiles/T.dir/T.c.o -c /home/<USER>/oaiseu/common/utils/T/T.c

common/utils/T/CMakeFiles/T.dir/T.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/T.dir/T.c.i"
	cd /home/<USER>/oaiseu/build_debug/common/utils/T && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/oaiseu/common/utils/T/T.c > CMakeFiles/T.dir/T.c.i

common/utils/T/CMakeFiles/T.dir/T.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/T.dir/T.c.s"
	cd /home/<USER>/oaiseu/build_debug/common/utils/T && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/oaiseu/common/utils/T/T.c -o CMakeFiles/T.dir/T.c.s

common/utils/T/CMakeFiles/T.dir/local_tracer.c.o: common/utils/T/CMakeFiles/T.dir/flags.make
common/utils/T/CMakeFiles/T.dir/local_tracer.c.o: ../common/utils/T/local_tracer.c
common/utils/T/CMakeFiles/T.dir/local_tracer.c.o: common/utils/T/CMakeFiles/T.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object common/utils/T/CMakeFiles/T.dir/local_tracer.c.o"
	cd /home/<USER>/oaiseu/build_debug/common/utils/T && /usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT common/utils/T/CMakeFiles/T.dir/local_tracer.c.o -MF CMakeFiles/T.dir/local_tracer.c.o.d -o CMakeFiles/T.dir/local_tracer.c.o -c /home/<USER>/oaiseu/common/utils/T/local_tracer.c

common/utils/T/CMakeFiles/T.dir/local_tracer.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/T.dir/local_tracer.c.i"
	cd /home/<USER>/oaiseu/build_debug/common/utils/T && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/oaiseu/common/utils/T/local_tracer.c > CMakeFiles/T.dir/local_tracer.c.i

common/utils/T/CMakeFiles/T.dir/local_tracer.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/T.dir/local_tracer.c.s"
	cd /home/<USER>/oaiseu/build_debug/common/utils/T && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/oaiseu/common/utils/T/local_tracer.c -o CMakeFiles/T.dir/local_tracer.c.s

# Object files for target T
T_OBJECTS = \
"CMakeFiles/T.dir/T.c.o" \
"CMakeFiles/T.dir/local_tracer.c.o"

# External object files for target T
T_EXTERNAL_OBJECTS =

common/utils/T/libT.a: common/utils/T/CMakeFiles/T.dir/T.c.o
common/utils/T/libT.a: common/utils/T/CMakeFiles/T.dir/local_tracer.c.o
common/utils/T/libT.a: common/utils/T/CMakeFiles/T.dir/build.make
common/utils/T/libT.a: common/utils/T/CMakeFiles/T.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Linking C static library libT.a"
	cd /home/<USER>/oaiseu/build_debug/common/utils/T && $(CMAKE_COMMAND) -P CMakeFiles/T.dir/cmake_clean_target.cmake
	cd /home/<USER>/oaiseu/build_debug/common/utils/T && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/T.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
common/utils/T/CMakeFiles/T.dir/build: common/utils/T/libT.a
.PHONY : common/utils/T/CMakeFiles/T.dir/build

common/utils/T/CMakeFiles/T.dir/clean:
	cd /home/<USER>/oaiseu/build_debug/common/utils/T && $(CMAKE_COMMAND) -P CMakeFiles/T.dir/cmake_clean.cmake
.PHONY : common/utils/T/CMakeFiles/T.dir/clean

common/utils/T/CMakeFiles/T.dir/depend: common/utils/T/T_IDs.h
common/utils/T/CMakeFiles/T.dir/depend: common/utils/T/T_messages.txt.h
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/oaiseu /home/<USER>/oaiseu/common/utils/T /home/<USER>/oaiseu/build_debug /home/<USER>/oaiseu/build_debug/common/utils/T /home/<USER>/oaiseu/build_debug/common/utils/T/CMakeFiles/T.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : common/utils/T/CMakeFiles/T.dir/depend

