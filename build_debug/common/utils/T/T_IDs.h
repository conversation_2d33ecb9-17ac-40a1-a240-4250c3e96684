/* generated file, do not edit by hand */

#define T_ENB_MASTER_TICK T_ID(0)
#define T_USRP_RX_ANT0 T_ID(1)
#define T_USRP_TX_ANT0 T_ID(2)
#define T_ENB_PHY_UL_TICK T_ID(3)
#define T_ENB_PHY_DL_TICK T_ID(4)
#define T_ENB_PHY_DLSCH_UE_DCI T_ID(5)
#define T_ENB_PHY_DLSCH_UE_ACK T_ID(6)
#define T_ENB_PHY_DLSCH_UE_NACK T_ID(7)
#define T_ENB_PHY_ULSCH_UE_DCI T_ID(8)
#define T_ENB_PHY_ULSCH_UE_NO_DCI_RETRANSMISSION T_ID(9)
#define T_ENB_PHY_ULSCH_UE_ACK T_ID(10)
#define T_ENB_PHY_ULSCH_UE_NACK T_ID(11)
#define T_ENB_PHY_INPUT_SIGNAL T_ID(12)
#define T_ENB_PHY_OUTPUT_SIGNAL T_ID(13)
#define T_ENB_PHY_UL_CHANNEL_ESTIMATE T_ID(14)
#define T_ENB_PHY_PUSCH_IQ T_ID(15)
#define T_ENB_PHY_PUCCH_1AB_IQ T_ID(16)
#define T_ENB_PHY_PUCCH_1_ENERGY T_ID(17)
#define T_ENB_PHY_PHICH T_ID(18)
#define T_ENB_PHY_MSG3_ALLOCATION T_ID(19)
#define T_ENB_PHY_INITIATE_RA_PROCEDURE T_ID(20)
#define T_UE_PHY_INITIATE_RA_PROCEDURE T_ID(21)
#define T_GNB_PHY_DL_TICK T_ID(22)
#define T_ENB_PHY_MIB T_ID(23)
#define T_GNB_PHY_MIB T_ID(24)
#define T_NRUE_PHY_MIB T_ID(25)
#define T_GNB_PHY_PUCCH_PUSCH_IQ T_ID(26)
#define T_GNB_PHY_UL_FREQ_CHANNEL_ESTIMATE T_ID(27)
#define T_GNB_PHY_UL_TIME_CHANNEL_ESTIMATE T_ID(28)
#define T_GNB_PHY_PRACH_INPUT_SIGNAL T_ID(29)
#define T_GNB_PHY_DL_OUTPUT_SIGNAL T_ID(30)
#define T_ENB_MAC_UE_DL_SDU T_ID(31)
#define T_ENB_MAC_UE_UL_SCHEDULE T_ID(32)
#define T_ENB_MAC_UE_UL_SCHEDULE_RETRANSMISSION T_ID(33)
#define T_ENB_MAC_UE_UL_PDU T_ID(34)
#define T_ENB_MAC_UE_UL_PDU_WITH_DATA T_ID(35)
#define T_ENB_MAC_UE_UL_SDU T_ID(36)
#define T_ENB_MAC_UE_UL_SDU_WITH_DATA T_ID(37)
#define T_ENB_MAC_UE_UL_CE T_ID(38)
#define T_ENB_MAC_UE_DL_PDU_WITH_DATA T_ID(39)
#define T_ENB_MAC_SCHEDULING_REQUEST T_ID(40)
#define T_ENB_MAC_UE_DL_RAR_PDU_WITH_DATA T_ID(41)
#define T_GNB_MAC_DL_PDU_WITH_DATA T_ID(42)
#define T_GNB_MAC_UL_PDU_WITH_DATA T_ID(43)
#define T_GNB_MAC_DL_RAR_PDU_WITH_DATA T_ID(44)
#define T_GNB_MAC_RETRANSMISSION_DL_PDU_WITH_DATA T_ID(45)
#define T_NRUE_MAC_DL_PDU_WITH_DATA T_ID(46)
#define T_NRUE_MAC_UL_PDU_WITH_DATA T_ID(47)
#define T_NRUE_MAC_DL_RAR_PDU_WITH_DATA T_ID(48)
#define T_ENB_RLC_DL T_ID(49)
#define T_ENB_RLC_UL T_ID(50)
#define T_ENB_RLC_MAC_DL T_ID(51)
#define T_ENB_RLC_MAC_UL T_ID(52)
#define T_ENB_PDCP_UL T_ID(53)
#define T_ENB_PDCP_DL T_ID(54)
#define T_ENB_RRC_CONNECTION_SETUP_COMPLETE T_ID(55)
#define T_ENB_RRC_SECURITY_MODE_COMMAND T_ID(56)
#define T_ENB_RRC_SECURITY_MODE_COMPLETE T_ID(57)
#define T_ENB_RRC_SECURITY_MODE_FAILURE T_ID(58)
#define T_ENB_RRC_UE_CAPABILITY_ENQUIRY T_ID(59)
#define T_ENB_RRC_UE_CAPABILITY_INFORMATION T_ID(60)
#define T_ENB_RRC_CONNECTION_REQUEST T_ID(61)
#define T_ENB_RRC_CONNECTION_REJECT T_ID(62)
#define T_ENB_RRC_CONNECTION_REESTABLISHMENT_REQUEST T_ID(63)
#define T_ENB_RRC_CONNECTION_REESTABLISHMENT T_ID(64)
#define T_ENB_RRC_CONNECTION_REESTABLISHMENT_COMPLETE T_ID(65)
#define T_ENB_RRC_CONNECTION_REESTABLISHMENT_REJECT T_ID(66)
#define T_ENB_RRC_CONNECTION_RELEASE T_ID(67)
#define T_ENB_RRC_CONNECTION_RECONFIGURATION T_ID(68)
#define T_ENB_RRC_MEASUREMENT_REPORT T_ID(69)
#define T_ENB_RRC_HANDOVER_PREPARATION_INFORMATION T_ID(70)
#define T_ENB_RRC_CONNECTION_RECONFIGURATION_COMPLETE T_ID(71)
#define T_ENB_RRC_CONNECTION_SETUP T_ID(72)
#define T_ENB_RRC_UL_CCCH_DATA_IN T_ID(73)
#define T_ENB_RRC_UL_DCCH_DATA_IN T_ID(74)
#define T_ENB_RRC_UL_HANDOVER_PREPARATION_TRANSFER T_ID(75)
#define T_ENB_RRC_UL_INFORMATION_TRANSFER T_ID(76)
#define T_ENB_RRC_COUNTER_CHECK_RESPONSE T_ID(77)
#define T_ENB_RRC_UE_INFORMATION_RESPONSE_R9 T_ID(78)
#define T_ENB_RRC_PROXIMITY_INDICATION_R9 T_ID(79)
#define T_ENB_RRC_RECONFIGURATION_COMPLETE_R10 T_ID(80)
#define T_ENB_RRC_MBMS_COUNTING_RESPONSE_R10 T_ID(81)
#define T_ENB_RRC_INTER_FREQ_RSTD_MEASUREMENT_INDICATION T_ID(82)
#define T_ENB_RRC_UNKNOW_MESSAGE T_ID(83)
#define T_LEGACY_MAC_INFO T_ID(84)
#define T_LEGACY_MAC_ERROR T_ID(85)
#define T_LEGACY_MAC_WARNING T_ID(86)
#define T_LEGACY_MAC_DEBUG T_ID(87)
#define T_LEGACY_MAC_TRACE T_ID(88)
#define T_LEGACY_NR_MAC_INFO T_ID(89)
#define T_LEGACY_NR_MAC_ERROR T_ID(90)
#define T_LEGACY_NR_MAC_WARNING T_ID(91)
#define T_LEGACY_NR_MAC_DEBUG T_ID(92)
#define T_LEGACY_NR_MAC_TRACE T_ID(93)
#define T_LEGACY_NR_PHY_DCI_INFO T_ID(94)
#define T_LEGACY_NR_PHY_DCI_ERROR T_ID(95)
#define T_LEGACY_NR_PHY_DCI_WARNING T_ID(96)
#define T_LEGACY_NR_PHY_DCI_DEBUG T_ID(97)
#define T_LEGACY_NR_PHY_DCI_TRACE T_ID(98)
#define T_LEGACY_NR_MAC_DCI_INFO T_ID(99)
#define T_LEGACY_NR_MAC_DCI_ERROR T_ID(100)
#define T_LEGACY_NR_MAC_DCI_WARNING T_ID(101)
#define T_LEGACY_NR_MAC_DCI_DEBUG T_ID(102)
#define T_LEGACY_NR_MAC_DCI_TRACE T_ID(103)
#define T_LEGACY_PHY_INFO T_ID(104)
#define T_LEGACY_PHY_ERROR T_ID(105)
#define T_LEGACY_PHY_WARNING T_ID(106)
#define T_LEGACY_PHY_DEBUG T_ID(107)
#define T_LEGACY_PHY_TRACE T_ID(108)
#define T_LEGACY_NR_PHY_INFO T_ID(109)
#define T_LEGACY_NR_PHY_ERROR T_ID(110)
#define T_LEGACY_NR_PHY_WARNING T_ID(111)
#define T_LEGACY_NR_PHY_DEBUG T_ID(112)
#define T_LEGACY_NR_PHY_TRACE T_ID(113)
#define T_LEGACY_S1AP_INFO T_ID(114)
#define T_LEGACY_S1AP_ERROR T_ID(115)
#define T_LEGACY_S1AP_WARNING T_ID(116)
#define T_LEGACY_S1AP_DEBUG T_ID(117)
#define T_LEGACY_S1AP_TRACE T_ID(118)
#define T_LEGACY_NGAP_INFO T_ID(119)
#define T_LEGACY_NGAP_ERROR T_ID(120)
#define T_LEGACY_NGAP_WARNING T_ID(121)
#define T_LEGACY_NGAP_DEBUG T_ID(122)
#define T_LEGACY_NGAP_TRACE T_ID(123)
#define T_LEGACY_X2AP_INFO T_ID(124)
#define T_LEGACY_X2AP_ERROR T_ID(125)
#define T_LEGACY_X2AP_WARNING T_ID(126)
#define T_LEGACY_X2AP_DEBUG T_ID(127)
#define T_LEGACY_X2AP_TRACE T_ID(128)
#define T_LEGACY_M2AP_INFO T_ID(129)
#define T_LEGACY_M2AP_ERROR T_ID(130)
#define T_LEGACY_M2AP_WARNING T_ID(131)
#define T_LEGACY_M2AP_DEBUG T_ID(132)
#define T_LEGACY_M2AP_TRACE T_ID(133)
#define T_LEGACY_M3AP_INFO T_ID(134)
#define T_LEGACY_M3AP_ERROR T_ID(135)
#define T_LEGACY_M3AP_WARNING T_ID(136)
#define T_LEGACY_M3AP_DEBUG T_ID(137)
#define T_LEGACY_M3AP_TRACE T_ID(138)
#define T_LEGACY_RRC_INFO T_ID(139)
#define T_LEGACY_RRC_ERROR T_ID(140)
#define T_LEGACY_RRC_WARNING T_ID(141)
#define T_LEGACY_RRC_DEBUG T_ID(142)
#define T_LEGACY_RRC_TRACE T_ID(143)
#define T_LEGACY_NR_RRC_INFO T_ID(144)
#define T_LEGACY_NR_RRC_ERROR T_ID(145)
#define T_LEGACY_NR_RRC_WARNING T_ID(146)
#define T_LEGACY_NR_RRC_DEBUG T_ID(147)
#define T_LEGACY_NR_RRC_TRACE T_ID(148)
#define T_LEGACY_RLC_INFO T_ID(149)
#define T_LEGACY_RLC_ERROR T_ID(150)
#define T_LEGACY_RLC_WARNING T_ID(151)
#define T_LEGACY_RLC_DEBUG T_ID(152)
#define T_LEGACY_RLC_TRACE T_ID(153)
#define T_LEGACY_PDCP_INFO T_ID(154)
#define T_LEGACY_PDCP_ERROR T_ID(155)
#define T_LEGACY_PDCP_WARNING T_ID(156)
#define T_LEGACY_PDCP_DEBUG T_ID(157)
#define T_LEGACY_PDCP_TRACE T_ID(158)
#define T_LEGACY_ENB_APP_INFO T_ID(159)
#define T_LEGACY_ENB_APP_ERROR T_ID(160)
#define T_LEGACY_ENB_APP_WARNING T_ID(161)
#define T_LEGACY_ENB_APP_DEBUG T_ID(162)
#define T_LEGACY_ENB_APP_TRACE T_ID(163)
#define T_LEGACY_GNB_APP_INFO T_ID(164)
#define T_LEGACY_GNB_APP_ERROR T_ID(165)
#define T_LEGACY_GNB_APP_WARNING T_ID(166)
#define T_LEGACY_GNB_APP_DEBUG T_ID(167)
#define T_LEGACY_GNB_APP_TRACE T_ID(168)
#define T_LEGACY_MCE_APP_INFO T_ID(169)
#define T_LEGACY_MCE_APP_ERROR T_ID(170)
#define T_LEGACY_MCE_APP_WARNING T_ID(171)
#define T_LEGACY_MCE_APP_DEBUG T_ID(172)
#define T_LEGACY_MCE_APP_TRACE T_ID(173)
#define T_LEGACY_MME_APP_INFO T_ID(174)
#define T_LEGACY_MME_APP_ERROR T_ID(175)
#define T_LEGACY_MME_APP_WARNING T_ID(176)
#define T_LEGACY_MME_APP_DEBUG T_ID(177)
#define T_LEGACY_MME_APP_TRACE T_ID(178)
#define T_LEGACY_SCTP_INFO T_ID(179)
#define T_LEGACY_SCTP_ERROR T_ID(180)
#define T_LEGACY_SCTP_WARNING T_ID(181)
#define T_LEGACY_SCTP_DEBUG T_ID(182)
#define T_LEGACY_SCTP_TRACE T_ID(183)
#define T_LEGACY_UDP__INFO T_ID(184)
#define T_LEGACY_UDP__ERROR T_ID(185)
#define T_LEGACY_UDP__WARNING T_ID(186)
#define T_LEGACY_UDP__DEBUG T_ID(187)
#define T_LEGACY_UDP__TRACE T_ID(188)
#define T_LEGACY_NAS_INFO T_ID(189)
#define T_LEGACY_NAS_ERROR T_ID(190)
#define T_LEGACY_NAS_WARNING T_ID(191)
#define T_LEGACY_NAS_DEBUG T_ID(192)
#define T_LEGACY_NAS_TRACE T_ID(193)
#define T_LEGACY_HW_INFO T_ID(194)
#define T_LEGACY_HW_ERROR T_ID(195)
#define T_LEGACY_HW_WARNING T_ID(196)
#define T_LEGACY_HW_DEBUG T_ID(197)
#define T_LEGACY_HW_TRACE T_ID(198)
#define T_LEGACY_EMU_INFO T_ID(199)
#define T_LEGACY_EMU_ERROR T_ID(200)
#define T_LEGACY_EMU_WARNING T_ID(201)
#define T_LEGACY_EMU_DEBUG T_ID(202)
#define T_LEGACY_EMU_TRACE T_ID(203)
#define T_LEGACY_OTG_INFO T_ID(204)
#define T_LEGACY_OTG_ERROR T_ID(205)
#define T_LEGACY_OTG_WARNING T_ID(206)
#define T_LEGACY_OTG_DEBUG T_ID(207)
#define T_LEGACY_OTG_TRACE T_ID(208)
#define T_LEGACY_OCM_INFO T_ID(209)
#define T_LEGACY_OCM_ERROR T_ID(210)
#define T_LEGACY_OCM_WARNING T_ID(211)
#define T_LEGACY_OCM_DEBUG T_ID(212)
#define T_LEGACY_OCM_TRACE T_ID(213)
#define T_LEGACY_OIP_INFO T_ID(214)
#define T_LEGACY_OIP_ERROR T_ID(215)
#define T_LEGACY_OIP_WARNING T_ID(216)
#define T_LEGACY_OIP_DEBUG T_ID(217)
#define T_LEGACY_OIP_TRACE T_ID(218)
#define T_LEGACY_OMG_INFO T_ID(219)
#define T_LEGACY_OMG_ERROR T_ID(220)
#define T_LEGACY_OMG_WARNING T_ID(221)
#define T_LEGACY_OMG_DEBUG T_ID(222)
#define T_LEGACY_OMG_TRACE T_ID(223)
#define T_LEGACY_OPT_INFO T_ID(224)
#define T_LEGACY_OPT_ERROR T_ID(225)
#define T_LEGACY_OPT_WARNING T_ID(226)
#define T_LEGACY_OPT_DEBUG T_ID(227)
#define T_LEGACY_OPT_TRACE T_ID(228)
#define T_LEGACY_GTPU_INFO T_ID(229)
#define T_LEGACY_GTPU_ERROR T_ID(230)
#define T_LEGACY_GTPU_WARNING T_ID(231)
#define T_LEGACY_GTPU_DEBUG T_ID(232)
#define T_LEGACY_GTPU_TRACE T_ID(233)
#define T_LEGACY_SDAP_INFO T_ID(234)
#define T_LEGACY_SDAP_ERROR T_ID(235)
#define T_LEGACY_SDAP_WARNING T_ID(236)
#define T_LEGACY_SDAP_DEBUG T_ID(237)
#define T_LEGACY_SDAP_TRACE T_ID(238)
#define T_LEGACY_TMR_INFO T_ID(239)
#define T_LEGACY_TMR_ERROR T_ID(240)
#define T_LEGACY_TMR_WARNING T_ID(241)
#define T_LEGACY_TMR_DEBUG T_ID(242)
#define T_LEGACY_TMR_TRACE T_ID(243)
#define T_LEGACY_OSA_INFO T_ID(244)
#define T_LEGACY_OSA_ERROR T_ID(245)
#define T_LEGACY_OSA_WARNING T_ID(246)
#define T_LEGACY_OSA_DEBUG T_ID(247)
#define T_LEGACY_OSA_TRACE T_ID(248)
#define T_LEGACY_ASN1_INFO T_ID(249)
#define T_LEGACY_ASN1_ERROR T_ID(250)
#define T_LEGACY_ASN1_WARNING T_ID(251)
#define T_LEGACY_ASN1_DEBUG T_ID(252)
#define T_LEGACY_ASN1_TRACE T_ID(253)
#define T_LEGACY_SIM_INFO T_ID(254)
#define T_LEGACY_SIM_ERROR T_ID(255)
#define T_LEGACY_SIM_WARNING T_ID(256)
#define T_LEGACY_SIM_DEBUG T_ID(257)
#define T_LEGACY_SIM_TRACE T_ID(258)
#define T_LEGACY_NFAPI_VNF_INFO T_ID(259)
#define T_LEGACY_NFAPI_VNF_ERROR T_ID(260)
#define T_LEGACY_NFAPI_VNF_WARNING T_ID(261)
#define T_LEGACY_NFAPI_VNF_DEBUG T_ID(262)
#define T_LEGACY_NFAPI_VNF_TRACE T_ID(263)
#define T_LEGACY_NFAPI_PNF_INFO T_ID(264)
#define T_LEGACY_NFAPI_PNF_ERROR T_ID(265)
#define T_LEGACY_NFAPI_PNF_WARNING T_ID(266)
#define T_LEGACY_NFAPI_PNF_DEBUG T_ID(267)
#define T_LEGACY_NFAPI_PNF_TRACE T_ID(268)
#define T_LEGACY_ITTI_INFO T_ID(269)
#define T_LEGACY_ITTI_ERROR T_ID(270)
#define T_LEGACY_ITTI_WARNING T_ID(271)
#define T_LEGACY_ITTI_DEBUG T_ID(272)
#define T_LEGACY_ITTI_TRACE T_ID(273)
#define T_LEGACY_UTIL_INFO T_ID(274)
#define T_LEGACY_UTIL_ERROR T_ID(275)
#define T_LEGACY_UTIL_WARNING T_ID(276)
#define T_LEGACY_UTIL_DEBUG T_ID(277)
#define T_LEGACY_UTIL_TRACE T_ID(278)
#define T_LEGACY_component_INFO T_ID(279)
#define T_LEGACY_component_ERROR T_ID(280)
#define T_LEGACY_component_WARNING T_ID(281)
#define T_LEGACY_component_DEBUG T_ID(282)
#define T_LEGACY_component_TRACE T_ID(283)
#define T_LEGACY_componentP_INFO T_ID(284)
#define T_LEGACY_componentP_ERROR T_ID(285)
#define T_LEGACY_componentP_WARNING T_ID(286)
#define T_LEGACY_componentP_DEBUG T_ID(287)
#define T_LEGACY_componentP_TRACE T_ID(288)
#define T_LEGACY_F1U_DEBUG T_ID(289)
#define T_LEGACY_F1U_INFO T_ID(290)
#define T_LEGACY_F1U_ERROR T_ID(291)
#define T_LEGACY_F1AP_TRACE T_ID(292)
#define T_LEGACY_F1AP_DEBUG T_ID(293)
#define T_LEGACY_F1AP_INFO T_ID(294)
#define T_LEGACY_F1AP_WARNING T_ID(295)
#define T_LEGACY_F1AP_ERROR T_ID(296)
#define T_LEGACY_E1AP_TRACE T_ID(297)
#define T_LEGACY_E1AP_DEBUG T_ID(298)
#define T_LEGACY_E1AP_INFO T_ID(299)
#define T_LEGACY_E1AP_WARNING T_ID(300)
#define T_LEGACY_E1AP_ERROR T_ID(301)
#define T_UE_MASTER_TICK T_ID(302)
#define T_UE_PHY_UL_TICK T_ID(303)
#define T_UE_PHY_DL_TICK T_ID(304)
#define T_UE_PHY_DLSCH_UE_DCI T_ID(305)
#define T_UE_PHY_DLSCH_UE_ACK T_ID(306)
#define T_UE_PHY_DLSCH_UE_NACK T_ID(307)
#define T_UE_PHY_ULSCH_UE_DCI T_ID(308)
#define T_UE_PHY_ULSCH_UE_ACK T_ID(309)
#define T_UE_PHY_ULSCH_UE_NACK T_ID(310)
#define T_UE_PHY_INPUT_SIGNAL T_ID(311)
#define T_UE_PHY_DL_CHANNEL_ESTIMATE T_ID(312)
#define T_UE_PHY_DL_CHANNEL_ESTIMATE_FREQ T_ID(313)
#define T_UE_PHY_PDCCH_IQ T_ID(314)
#define T_UE_PHY_PDCCH_ENERGY T_ID(315)
#define T_UE_PHY_PDSCH_IQ T_ID(316)
#define T_UE_PHY_PDSCH_ENERGY T_ID(317)
#define T_UE_PHY_PUSCH_TX_POWER T_ID(318)
#define T_UE_PHY_PUCCH_TX_POWER T_ID(319)
#define T_UE_PHY_MEAS T_ID(320)
#define T_first T_ID(321)
#define T_buf_test T_ID(322)
#define T_VCD_VARIABLE_FRAME_NUMBER_TX0_ENB T_ID(323)
#define T_VCD_VARIABLE_MASK_RU T_ID(324)
#define T_VCD_VARIABLE_MASK_TX_RU T_ID(325)
#define T_VCD_VARIABLE_FRAME_NUMBER_TX1_ENB T_ID(326)
#define T_VCD_VARIABLE_FRAME_NUMBER_RX0_ENB T_ID(327)
#define T_VCD_VARIABLE_FRAME_NUMBER_RX1_ENB T_ID(328)
#define T_VCD_VARIABLE_SUBFRAME_NUMBER_TX0_ENB T_ID(329)
#define T_VCD_VARIABLE_SUBFRAME_NUMBER_TX1_ENB T_ID(330)
#define T_VCD_VARIABLE_SUBFRAME_NUMBER_RX0_ENB T_ID(331)
#define T_VCD_VARIABLE_SUBFRAME_NUMBER_RX1_ENB T_ID(332)
#define T_VCD_VARIABLE_FRAME_NUMBER_TX0_RU T_ID(333)
#define T_VCD_VARIABLE_FRAME_NUMBER_TX1_RU T_ID(334)
#define T_VCD_VARIABLE_FRAME_NUMBER_RX0_RU T_ID(335)
#define T_VCD_VARIABLE_FRAME_NUMBER_RX1_RU T_ID(336)
#define T_VCD_VARIABLE_TTI_NUMBER_TX0_RU T_ID(337)
#define T_VCD_VARIABLE_TTI_NUMBER_TX1_RU T_ID(338)
#define T_VCD_VARIABLE_TTI_NUMBER_RX0_RU T_ID(339)
#define T_VCD_VARIABLE_TTI_NUMBER_RX1_RU T_ID(340)
#define T_VCD_VARIABLE_SUBFRAME_NUMBER_IF4P5_NORTH_OUT T_ID(341)
#define T_VCD_VARIABLE_FRAME_NUMBER_IF4P5_NORTH_OUT T_ID(342)
#define T_VCD_VARIABLE_SUBFRAME_NUMBER_IF4P5_NORTH_ASYNCH_IN T_ID(343)
#define T_VCD_VARIABLE_FRAME_NUMBER_IF4P5_NORTH_ASYNCH_IN T_ID(344)
#define T_VCD_VARIABLE_SUBFRAME_NUMBER_IF4P5_SOUTH_OUT_RU T_ID(345)
#define T_VCD_VARIABLE_SUBFRAME_NUMBER_IF4P5_SOUTH_OUT_RU1 T_ID(346)
#define T_VCD_VARIABLE_SUBFRAME_NUMBER_IF4P5_SOUTH_OUT_RU2 T_ID(347)
#define T_VCD_VARIABLE_FRAME_NUMBER_IF4P5_SOUTH_OUT_RU T_ID(348)
#define T_VCD_VARIABLE_FRAME_NUMBER_IF4P5_SOUTH_OUT_RU1 T_ID(349)
#define T_VCD_VARIABLE_FRAME_NUMBER_IF4P5_SOUTH_OUT_RU2 T_ID(350)
#define T_VCD_VARIABLE_SUBFRAME_NUMBER_IF4P5_SOUTH_IN_RU T_ID(351)
#define T_VCD_VARIABLE_SUBFRAME_NUMBER_IF4P5_SOUTH_IN_RU1 T_ID(352)
#define T_VCD_VARIABLE_SUBFRAME_NUMBER_IF4P5_SOUTH_IN_RU2 T_ID(353)
#define T_VCD_VARIABLE_FRAME_NUMBER_IF4P5_SOUTH_IN_RU T_ID(354)
#define T_VCD_VARIABLE_FRAME_NUMBER_IF4P5_SOUTH_IN_RU1 T_ID(355)
#define T_VCD_VARIABLE_FRAME_NUMBER_IF4P5_SOUTH_IN_RU2 T_ID(356)
#define T_VCD_VARIABLE_SUBFRAME_NUMBER_WAKEUP_L1S_RU T_ID(357)
#define T_VCD_VARIABLE_SUBFRAME_NUMBER_WAKEUP_L1S_RU1 T_ID(358)
#define T_VCD_VARIABLE_SUBFRAME_NUMBER_WAKEUP_L1S_RU2 T_ID(359)
#define T_VCD_VARIABLE_FRAME_NUMBER_WAKEUP_L1S_RU T_ID(360)
#define T_VCD_VARIABLE_FRAME_NUMBER_WAKEUP_L1S_RU1 T_ID(361)
#define T_VCD_VARIABLE_FRAME_NUMBER_WAKEUP_L1S_RU2 T_ID(362)
#define T_VCD_VARIABLE_SUBFRAME_NUMBER_WAKEUP_RXTX_RX_RU T_ID(363)
#define T_VCD_VARIABLE_SUBFRAME_NUMBER_WAKEUP_RXTX_RX_RU1 T_ID(364)
#define T_VCD_VARIABLE_SUBFRAME_NUMBER_WAKEUP_RXTX_RX_RU2 T_ID(365)
#define T_VCD_VARIABLE_FRAME_NUMBER_WAKEUP_RXTX_RX_RU T_ID(366)
#define T_VCD_VARIABLE_FRAME_NUMBER_WAKEUP_RXTX_RX_RU1 T_ID(367)
#define T_VCD_VARIABLE_FRAME_NUMBER_WAKEUP_RXTX_RX_RU2 T_ID(368)
#define T_VCD_VARIABLE_SUBFRAME_NUMBER_WAKEUP_RXTX_TX_RU T_ID(369)
#define T_VCD_VARIABLE_SUBFRAME_NUMBER_WAKEUP_RXTX_TX_RU1 T_ID(370)
#define T_VCD_VARIABLE_SUBFRAME_NUMBER_WAKEUP_RXTX_TX_RU2 T_ID(371)
#define T_VCD_VARIABLE_FRAME_NUMBER_WAKEUP_RXTX_TX_RU T_ID(372)
#define T_VCD_VARIABLE_FRAME_NUMBER_WAKEUP_RXTX_TX_RU1 T_ID(373)
#define T_VCD_VARIABLE_FRAME_NUMBER_WAKEUP_RXTX_TX_RU2 T_ID(374)
#define T_VCD_VARIABLE_IC_ENB T_ID(375)
#define T_VCD_VARIABLE_IC_ENB1 T_ID(376)
#define T_VCD_VARIABLE_IC_ENB2 T_ID(377)
#define T_VCD_VARIABLE_L1_PROC_IC T_ID(378)
#define T_VCD_VARIABLE_L1_PROC_TX_IC T_ID(379)
#define T_VCD_VARIABLE_RUNTIME_TX_ENB T_ID(380)
#define T_VCD_VARIABLE_RUNTIME_RX_ENB T_ID(381)
#define T_VCD_VARIABLE_FRAME_NUMBER_TX0_UE T_ID(382)
#define T_VCD_VARIABLE_FRAME_NUMBER_TX1_UE T_ID(383)
#define T_VCD_VARIABLE_FRAME_NUMBER_RX0_UE T_ID(384)
#define T_VCD_VARIABLE_FRAME_NUMBER_RX1_UE T_ID(385)
#define T_VCD_VARIABLE_SUBFRAME_NUMBER_TX0_UE T_ID(386)
#define T_VCD_VARIABLE_SUBFRAME_NUMBER_TX1_UE T_ID(387)
#define T_VCD_VARIABLE_SUBFRAME_NUMBER_RX0_UE T_ID(388)
#define T_VCD_VARIABLE_SUBFRAME_NUMBER_RX1_UE T_ID(389)
#define T_VCD_VARIABLE_UE_RX_OFFSET T_ID(390)
#define T_VCD_VARIABLE_DIFF T_ID(391)
#define T_VCD_VARIABLE_HW_SUBFRAME T_ID(392)
#define T_VCD_VARIABLE_HW_FRAME T_ID(393)
#define T_VCD_VARIABLE_HW_SUBFRAME_RX T_ID(394)
#define T_VCD_VARIABLE_HW_FRAME_RX T_ID(395)
#define T_VCD_VARIABLE_TXCNT T_ID(396)
#define T_VCD_VARIABLE_RXCNT T_ID(397)
#define T_VCD_VARIABLE_TRX_TS T_ID(398)
#define T_VCD_VARIABLE_TRX_TST T_ID(399)
#define T_VCD_VARIABLE_TRX_TS_UE T_ID(400)
#define T_VCD_VARIABLE_TRX_TST_UE T_ID(401)
#define T_VCD_VARIABLE_TRX_WRITE_FLAGS T_ID(402)
#define T_VCD_VARIABLE_TX_TS T_ID(403)
#define T_VCD_VARIABLE_RX_TS T_ID(404)
#define T_VCD_VARIABLE_RX_HWCNT T_ID(405)
#define T_VCD_VARIABLE_RX_LHWCNT T_ID(406)
#define T_VCD_VARIABLE_TX_HWCNT T_ID(407)
#define T_VCD_VARIABLE_TX_LHWCNT T_ID(408)
#define T_VCD_VARIABLE_RX_PCK T_ID(409)
#define T_VCD_VARIABLE_TX_PCK T_ID(410)
#define T_VCD_VARIABLE_RX_SEQ_NUM T_ID(411)
#define T_VCD_VARIABLE_RX_SEQ_NUM_PRV T_ID(412)
#define T_VCD_VARIABLE_TX_SEQ_NUM T_ID(413)
#define T_VCD_VARIABLE_CNT T_ID(414)
#define T_VCD_VARIABLE_DUMMY_DUMP T_ID(415)
#define T_VCD_VARIABLE_ITTI_SEND_MSG T_ID(416)
#define T_VCD_VARIABLE_ITTI_POLL_MSG T_ID(417)
#define T_VCD_VARIABLE_ITTI_RECV_MSG T_ID(418)
#define T_VCD_VARIABLE_ITTI_ALLOC_MSG T_ID(419)
#define T_VCD_VARIABLE_MP_ALLOC T_ID(420)
#define T_VCD_VARIABLE_MP_FREE T_ID(421)
#define T_VCD_VARIABLE_UE_INST_CNT_RX T_ID(422)
#define T_VCD_VARIABLE_UE_INST_CNT_TX T_ID(423)
#define T_VCD_VARIABLE_DCI_INFO T_ID(424)
#define T_VCD_VARIABLE_UE0_BSR T_ID(425)
#define T_VCD_VARIABLE_UE0_BO T_ID(426)
#define T_VCD_VARIABLE_UE0_SCHEDULED T_ID(427)
#define T_VCD_VARIABLE_UE0_TIMING_ADVANCE T_ID(428)
#define T_VCD_VARIABLE_UE0_SR_ENERGY T_ID(429)
#define T_VCD_VARIABLE_UE0_SR_THRES T_ID(430)
#define T_VCD_VARIABLE_UE0_RSSI0 T_ID(431)
#define T_VCD_VARIABLE_UE0_RSSI1 T_ID(432)
#define T_VCD_VARIABLE_UE0_RSSI2 T_ID(433)
#define T_VCD_VARIABLE_UE0_RSSI3 T_ID(434)
#define T_VCD_VARIABLE_UE0_RSSI4 T_ID(435)
#define T_VCD_VARIABLE_UE0_RSSI5 T_ID(436)
#define T_VCD_VARIABLE_UE0_RSSI6 T_ID(437)
#define T_VCD_VARIABLE_UE0_RSSI7 T_ID(438)
#define T_VCD_VARIABLE_UE0_RES0 T_ID(439)
#define T_VCD_VARIABLE_UE0_RES1 T_ID(440)
#define T_VCD_VARIABLE_UE0_RES2 T_ID(441)
#define T_VCD_VARIABLE_UE0_RES3 T_ID(442)
#define T_VCD_VARIABLE_UE0_RES4 T_ID(443)
#define T_VCD_VARIABLE_UE0_RES5 T_ID(444)
#define T_VCD_VARIABLE_UE0_RES6 T_ID(445)
#define T_VCD_VARIABLE_UE0_RES7 T_ID(446)
#define T_VCD_VARIABLE_UE0_MCS0 T_ID(447)
#define T_VCD_VARIABLE_UE0_MCS1 T_ID(448)
#define T_VCD_VARIABLE_UE0_MCS2 T_ID(449)
#define T_VCD_VARIABLE_UE0_MCS3 T_ID(450)
#define T_VCD_VARIABLE_UE0_MCS4 T_ID(451)
#define T_VCD_VARIABLE_UE0_MCS5 T_ID(452)
#define T_VCD_VARIABLE_UE0_MCS6 T_ID(453)
#define T_VCD_VARIABLE_UE0_MCS7 T_ID(454)
#define T_VCD_VARIABLE_UE0_RB0 T_ID(455)
#define T_VCD_VARIABLE_UE0_RB1 T_ID(456)
#define T_VCD_VARIABLE_UE0_RB2 T_ID(457)
#define T_VCD_VARIABLE_UE0_RB3 T_ID(458)
#define T_VCD_VARIABLE_UE0_RB4 T_ID(459)
#define T_VCD_VARIABLE_UE0_RB5 T_ID(460)
#define T_VCD_VARIABLE_UE0_RB6 T_ID(461)
#define T_VCD_VARIABLE_UE0_RB7 T_ID(462)
#define T_VCD_VARIABLE_UE0_ROUND0 T_ID(463)
#define T_VCD_VARIABLE_UE0_ROUND1 T_ID(464)
#define T_VCD_VARIABLE_UE0_ROUND2 T_ID(465)
#define T_VCD_VARIABLE_UE0_ROUND3 T_ID(466)
#define T_VCD_VARIABLE_UE0_ROUND4 T_ID(467)
#define T_VCD_VARIABLE_UE0_ROUND5 T_ID(468)
#define T_VCD_VARIABLE_UE0_ROUND6 T_ID(469)
#define T_VCD_VARIABLE_UE0_ROUND7 T_ID(470)
#define T_VCD_VARIABLE_UE0_SFN0 T_ID(471)
#define T_VCD_VARIABLE_UE0_SFN1 T_ID(472)
#define T_VCD_VARIABLE_UE0_SFN2 T_ID(473)
#define T_VCD_VARIABLE_UE0_SFN3 T_ID(474)
#define T_VCD_VARIABLE_UE0_SFN4 T_ID(475)
#define T_VCD_VARIABLE_UE0_SFN5 T_ID(476)
#define T_VCD_VARIABLE_UE0_SFN6 T_ID(477)
#define T_VCD_VARIABLE_UE0_SFN7 T_ID(478)
#define T_VCD_VARIABLE_SEND_IF4_SYMBOL T_ID(479)
#define T_VCD_VARIABLE_RECV_IF4_SYMBOL T_ID(480)
#define T_VCD_VARIABLE_SEND_IF5_PKT_ID T_ID(481)
#define T_VCD_VARIABLE_RECV_IF5_PKT_ID T_ID(482)
#define T_VCD_VARIABLE_UE_PDCP_FLUSH_SIZE T_ID(483)
#define T_VCD_VARIABLE_UE_PDCP_FLUSH_ERR T_ID(484)
#define T_VCD_VARIABLE_UE0_TRX_READ_NS T_ID(485)
#define T_VCD_VARIABLE_UE0_TRX_WRITE_NS T_ID(486)
#define T_VCD_VARIABLE_UE0_TRX_READ_NS_MISSING T_ID(487)
#define T_VCD_VARIABLE_UE0_TRX_WRITE_NS_MISSING T_ID(488)
#define T_VCD_VARIABLE_CPUID_ENB_THREAD_RXTX T_ID(489)
#define T_VCD_VARIABLE_CPUID_RU_THREAD T_ID(490)
#define T_VCD_VARIABLE_CPUID_RU_THREAD_TX T_ID(491)
#define T_VCD_VARIABLE_ON_DURATION_TIMER T_ID(492)
#define T_VCD_VARIABLE_DRX_INACTIVITY T_ID(493)
#define T_VCD_VARIABLE_DRX_SHORT_CYCLE T_ID(494)
#define T_VCD_VARIABLE_SHORT_DRX_CYCLE_NUMBER T_ID(495)
#define T_VCD_VARIABLE_DRX_LONG_CYCLE T_ID(496)
#define T_VCD_VARIABLE_DRX_RETRANSMISSION_HARQ0 T_ID(497)
#define T_VCD_VARIABLE_DRX_ACTIVE_TIME T_ID(498)
#define T_VCD_VARIABLE_DRX_ACTIVE_TIME_CONDITION T_ID(499)
#define T_VCD_VARIABLE_FRAME_NUMBER_TX0_GNB T_ID(500)
#define T_VCD_VARIABLE_FRAME_NUMBER_TX1_GNB T_ID(501)
#define T_VCD_VARIABLE_FRAME_NUMBER_RX0_GNB T_ID(502)
#define T_VCD_VARIABLE_FRAME_NUMBER_RX1_GNB T_ID(503)
#define T_VCD_VARIABLE_SLOT_NUMBER_TX0_GNB T_ID(504)
#define T_VCD_VARIABLE_SLOT_NUMBER_TX1_GNB T_ID(505)
#define T_VCD_VARIABLE_SLOT_NUMBER_RX0_GNB T_ID(506)
#define T_VCD_VARIABLE_SLOT_NUMBER_RX1_GNB T_ID(507)
#define T_VCD_VARIABLE_RU_TX_OFDM_MASK T_ID(508)
#define T_VCD_VARIABLE_USRP_SEND_RETURN T_ID(509)
#define T_VCD_FUNCTION_RT_SLEEP T_ID(510)
#define T_VCD_FUNCTION_TRX_READ T_ID(511)
#define T_VCD_FUNCTION_TRX_WRITE T_ID(512)
#define T_VCD_FUNCTION_TRX_READ_UE T_ID(513)
#define T_VCD_FUNCTION_TRX_WRITE_UE T_ID(514)
#define T_VCD_FUNCTION_TRX_READ_IF0 T_ID(515)
#define T_VCD_FUNCTION_TRX_READ_IF1 T_ID(516)
#define T_VCD_FUNCTION_TRX_WRITE_IF0 T_ID(517)
#define T_VCD_FUNCTION_TRX_WRITE_IF1 T_ID(518)
#define T_VCD_FUNCTION_eNB_PROC_RXTX0 T_ID(519)
#define T_VCD_FUNCTION_eNB_PROC_RXTX1 T_ID(520)
#define T_VCD_FUNCTION_UE_THREAD_SYNCH T_ID(521)
#define T_VCD_FUNCTION_UE_THREAD_RXTX0 T_ID(522)
#define T_VCD_FUNCTION_UE_THREAD_RXTX1 T_ID(523)
#define T_VCD_FUNCTION_TRX_READ_SF9 T_ID(524)
#define T_VCD_FUNCTION_TRX_WRITE_SF9 T_ID(525)
#define T_VCD_FUNCTION_UE_SIGNAL_COND_RXTX0 T_ID(526)
#define T_VCD_FUNCTION_UE_SIGNAL_COND_RXTX1 T_ID(527)
#define T_VCD_FUNCTION_UE_WAIT_COND_RXTX0 T_ID(528)
#define T_VCD_FUNCTION_UE_WAIT_COND_RXTX1 T_ID(529)
#define T_VCD_FUNCTION_UE_LOCK_MUTEX_RXTX_FOR_COND_WAIT0 T_ID(530)
#define T_VCD_FUNCTION_UE_LOCK_MUTEX_RXTX_FOR_COND_WAIT1 T_ID(531)
#define T_VCD_FUNCTION_UE_LOCK_MUTEX_RXTX_FOR_CNT_DECREMENT0 T_ID(532)
#define T_VCD_FUNCTION_UE_LOCK_MUTEX_RXTX_FOR_CNT_DECREMENT1 T_ID(533)
#define T_VCD_FUNCTION_UE_LOCK_MUTEX_RXTX_FOR_CNT_INCREMENT0 T_ID(534)
#define T_VCD_FUNCTION_UE_LOCK_MUTEX_RXTX_FOR_CNT_INCREMENT1 T_ID(535)
#define T_VCD_FUNCTION_LOCK_MUTEX_RU T_ID(536)
#define T_VCD_FUNCTION_LOCK_MUTEX_RU1 T_ID(537)
#define T_VCD_FUNCTION_LOCK_MUTEX_RU2 T_ID(538)
#define T_VCD_FUNCTION_TRX_WRITE_THREAD T_ID(539)
#define T_VCD_FUNCTION_SIM_DO_DL_SIGNAL T_ID(540)
#define T_VCD_FUNCTION_SIM_DO_UL_SIGNAL T_ID(541)
#define T_VCD_FUNCTION_SIM_UE_TRX_READ T_ID(542)
#define T_VCD_FUNCTION_eNB_TX T_ID(543)
#define T_VCD_FUNCTION_eNB_RX T_ID(544)
#define T_VCD_FUNCTION_eNB_TRX T_ID(545)
#define T_VCD_FUNCTION_eNB_TM T_ID(546)
#define T_VCD_FUNCTION_eNB_RX_SLEEP T_ID(547)
#define T_VCD_FUNCTION_eNB_TX_SLEEP T_ID(548)
#define T_VCD_FUNCTION_eNB_PROC_SLEEP T_ID(549)
#define T_VCD_FUNCTION_TRX_READ_RF T_ID(550)
#define T_VCD_FUNCTION_TRX_WRITE_RF T_ID(551)
#define T_VCD_FUNCTION_UE_SYNCH T_ID(552)
#define T_VCD_FUNCTION_UE_SLOT_FEP T_ID(553)
#define T_VCD_FUNCTION_UE_SLOT_FEP_PDCCH T_ID(554)
#define T_VCD_FUNCTION_UE_SLOT_FEP_PBCH T_ID(555)
#define T_VCD_FUNCTION_UE_SLOT_FEP_PDSCH T_ID(556)
#define T_VCD_FUNCTION_UE_SLOT_FEP_MBSFN T_ID(557)
#define T_VCD_FUNCTION_UE_SLOT_FEP_MBSFN_KHZ_1DOT25 T_ID(558)
#define T_VCD_FUNCTION_UE_RRC_MEASUREMENTS T_ID(559)
#define T_VCD_FUNCTION_UE_GAIN_CONTROL T_ID(560)
#define T_VCD_FUNCTION_UE_ADJUST_SYNCH T_ID(561)
#define T_VCD_FUNCTION_UE_MEASUREMENT_PROCEDURES T_ID(562)
#define T_VCD_FUNCTION_UE_PDCCH_PROCEDURES T_ID(563)
#define T_VCD_FUNCTION_UE_PBCH_PROCEDURES T_ID(564)
#define T_VCD_FUNCTION_PHY_PROCEDURES_ENB_TX T_ID(565)
#define T_VCD_FUNCTION_PHY_PROCEDURES_ENB_TX1 T_ID(566)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPRX T_ID(567)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPRX1 T_ID(568)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPRX2 T_ID(569)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPRX3 T_ID(570)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPRX4 T_ID(571)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPRX5 T_ID(572)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPRX6 T_ID(573)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPRX7 T_ID(574)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPRX8 T_ID(575)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPRX9 T_ID(576)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPTX_OFDM T_ID(577)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPTX_OFDM1 T_ID(578)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPTX_OFDM2 T_ID(579)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPTX_OFDM3 T_ID(580)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPTX_OFDM4 T_ID(581)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPTX_OFDM5 T_ID(582)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPTX_OFDM6 T_ID(583)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPTX_OFDM7 T_ID(584)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPTX_OFDM8 T_ID(585)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPTX_OFDM9 T_ID(586)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPTX_OFDM10 T_ID(587)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPTX_OFDM11 T_ID(588)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPTX_OFDM12 T_ID(589)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPTX_OFDM13 T_ID(590)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPTX_OFDM14 T_ID(591)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPTX_OFDM15 T_ID(592)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPTX_OFDM16 T_ID(593)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPTX_PREC T_ID(594)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPTX_PREC1 T_ID(595)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPTX_PREC2 T_ID(596)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPTX_PREC3 T_ID(597)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPTX_PREC4 T_ID(598)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPTX_PREC5 T_ID(599)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPTX_PREC6 T_ID(600)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPTX_PREC7 T_ID(601)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPTX_PREC8 T_ID(602)
#define T_VCD_FUNCTION_PHY_PROCEDURES_RU_FEPTX_PREC9 T_ID(603)
#define T_VCD_FUNCTION_PHY_PROCEDURES_ENB_RX_UESPEC T_ID(604)
#define T_VCD_FUNCTION_PHY_PROCEDURES_ENB_RX_UESPEC1 T_ID(605)
#define T_VCD_FUNCTION_PHY_PROCEDURES_UE_TX T_ID(606)
#define T_VCD_FUNCTION_PHY_PROCEDURES_UE_RX T_ID(607)
#define T_VCD_FUNCTION_PHY_PROCEDURES_UE_TX_ULSCH_UESPEC T_ID(608)
#define T_VCD_FUNCTION_PHY_PROCEDURES_NR_UE_TX_ULSCH_UESPEC T_ID(609)
#define T_VCD_FUNCTION_PHY_PROCEDURES_UE_TX_PUCCH T_ID(610)
#define T_VCD_FUNCTION_PHY_PROCEDURES_UE_TX_ULSCH_COMMON T_ID(611)
#define T_VCD_FUNCTION_PHY_PROCEDURES_UE_TX_PRACH T_ID(612)
#define T_VCD_FUNCTION_PHY_PROCEDURES_UE_TX_ULSCH_RAR T_ID(613)
#define T_VCD_FUNCTION_PHY_PROCEDURES_ENB_LTE T_ID(614)
#define T_VCD_FUNCTION_PHY_PROCEDURES_UE_LTE T_ID(615)
#define T_VCD_FUNCTION_PDSCH_THREAD T_ID(616)
#define T_VCD_FUNCTION_DLSCH_THREAD0 T_ID(617)
#define T_VCD_FUNCTION_DLSCH_THREAD1 T_ID(618)
#define T_VCD_FUNCTION_DLSCH_THREAD2 T_ID(619)
#define T_VCD_FUNCTION_DLSCH_THREAD3 T_ID(620)
#define T_VCD_FUNCTION_DLSCH_THREAD4 T_ID(621)
#define T_VCD_FUNCTION_DLSCH_THREAD5 T_ID(622)
#define T_VCD_FUNCTION_DLSCH_THREAD6 T_ID(623)
#define T_VCD_FUNCTION_DLSCH_THREAD7 T_ID(624)
#define T_VCD_FUNCTION_DLSCH_DECODING0 T_ID(625)
#define T_VCD_FUNCTION_DLSCH_DECODING1 T_ID(626)
#define T_VCD_FUNCTION_DLSCH_DECODING2 T_ID(627)
#define T_VCD_FUNCTION_DLSCH_DECODING3 T_ID(628)
#define T_VCD_FUNCTION_DLSCH_DECODING4 T_ID(629)
#define T_VCD_FUNCTION_DLSCH_DECODING5 T_ID(630)
#define T_VCD_FUNCTION_DLSCH_DECODING6 T_ID(631)
#define T_VCD_FUNCTION_DLSCH_DECODING7 T_ID(632)
#define T_VCD_FUNCTION_DLSCH_SEGMENTATION T_ID(633)
#define T_VCD_FUNCTION_DLSCH_DEINTERLEAVING T_ID(634)
#define T_VCD_FUNCTION_DLSCH_RATE_MATCHING T_ID(635)
#define T_VCD_FUNCTION_DLSCH_LDPC T_ID(636)
#define T_VCD_FUNCTION_DLSCH_COMBINE_SEG T_ID(637)
#define T_VCD_FUNCTION_DLSCH_PMCH_DECODING T_ID(638)
#define T_VCD_FUNCTION_RX_PDCCH T_ID(639)
#define T_VCD_FUNCTION_DCI_DECODING T_ID(640)
#define T_VCD_FUNCTION_RX_PHICH T_ID(641)
#define T_VCD_FUNCTION_RX_PMCH T_ID(642)
#define T_VCD_FUNCTION_RX_PMCH_KHZ_1DOT25 T_ID(643)
#define T_VCD_FUNCTION_PDSCH_PROC T_ID(644)
#define T_VCD_FUNCTION_PDSCH_PROC_C T_ID(645)
#define T_VCD_FUNCTION_PDSCH_PROC_SI T_ID(646)
#define T_VCD_FUNCTION_PDSCH_PROC_P T_ID(647)
#define T_VCD_FUNCTION_PDSCH_PROC_RA T_ID(648)
#define T_VCD_FUNCTION_PHY_UE_CONFIG_SIB2 T_ID(649)
#define T_VCD_FUNCTION_PHY_CONFIG_SIB1_ENB T_ID(650)
#define T_VCD_FUNCTION_PHY_CONFIG_SIB2_ENB T_ID(651)
#define T_VCD_FUNCTION_PHY_CONFIG_DEDICATED_ENB T_ID(652)
#define T_VCD_FUNCTION_PHY_UE_COMPUTE_PRACH T_ID(653)
#define T_VCD_FUNCTION_PHY_ENB_ULSCH_MSG3 T_ID(654)
#define T_VCD_FUNCTION_PHY_ENB_ULSCH_DECODING0 T_ID(655)
#define T_VCD_FUNCTION_PHY_ENB_ULSCH_DECODING1 T_ID(656)
#define T_VCD_FUNCTION_PHY_ENB_ULSCH_DECODING2 T_ID(657)
#define T_VCD_FUNCTION_PHY_ENB_ULSCH_DECODING3 T_ID(658)
#define T_VCD_FUNCTION_PHY_ENB_ULSCH_DECODING4 T_ID(659)
#define T_VCD_FUNCTION_PHY_ENB_ULSCH_DECODING5 T_ID(660)
#define T_VCD_FUNCTION_PHY_ENB_ULSCH_DECODING6 T_ID(661)
#define T_VCD_FUNCTION_PHY_ENB_ULSCH_DECODING7 T_ID(662)
#define T_VCD_FUNCTION_PHY_ENB_SFGEN T_ID(663)
#define T_VCD_FUNCTION_PHY_ENB_PRACH_RX T_ID(664)
#define T_VCD_FUNCTION_PHY_RU_PRACH_RX T_ID(665)
#define T_VCD_FUNCTION_PHY_ENB_PDCCH_TX T_ID(666)
#define T_VCD_FUNCTION_PHY_ENB_COMMON_TX T_ID(667)
#define T_VCD_FUNCTION_PHY_ENB_RS_TX T_ID(668)
#define T_VCD_FUNCTION_UE_GENERATE_PRACH T_ID(669)
#define T_VCD_FUNCTION_UE_ULSCH_MODULATION T_ID(670)
#define T_VCD_FUNCTION_UE_ULSCH_ENCODING T_ID(671)
#define T_VCD_FUNCTION_UE_ULSCH_ENCODING_FILL_CQI T_ID(672)
#define T_VCD_FUNCTION_UE_ULSCH_SCRAMBLING T_ID(673)
#define T_VCD_FUNCTION_ENB_DLSCH_MODULATION T_ID(674)
#define T_VCD_FUNCTION_ENB_DLSCH_ENCODING T_ID(675)
#define T_VCD_FUNCTION_ENB_DLSCH_ENCODING_W T_ID(676)
#define T_VCD_FUNCTION_ENB_DLSCH_SCRAMBLING T_ID(677)
#define T_VCD_FUNCTION_ENB_BEAM_PRECODING T_ID(678)
#define T_VCD_FUNCTION_ENB_OFDM_MODULATION T_ID(679)
#define T_VCD_FUNCTION_MACPHY_INIT T_ID(680)
#define T_VCD_FUNCTION_MACPHY_EXIT T_ID(681)
#define T_VCD_FUNCTION_ENB_DLSCH_ULSCH_SCHEDULER T_ID(682)
#define T_VCD_FUNCTION_FILL_RAR T_ID(683)
#define T_VCD_FUNCTION_TERMINATE_RA_PROC T_ID(684)
#define T_VCD_FUNCTION_INITIATE_RA_PROC T_ID(685)
#define T_VCD_FUNCTION_CANCEL_RA_PROC T_ID(686)
#define T_VCD_FUNCTION_GET_DCI_SDU T_ID(687)
#define T_VCD_FUNCTION_GET_DLSCH_SDU T_ID(688)
#define T_VCD_FUNCTION_RX_SDU T_ID(689)
#define T_VCD_FUNCTION_MRBCH_PHY_SYNC_FAILURE T_ID(690)
#define T_VCD_FUNCTION_SR_INDICATION T_ID(691)
#define T_VCD_FUNCTION_DLSCH_PREPROCESSOR T_ID(692)
#define T_VCD_FUNCTION_SCHEDULE_DLSCH T_ID(693)
#define T_VCD_FUNCTION_FILL_DLSCH_DCI T_ID(694)
#define T_VCD_FUNCTION_OUT_OF_SYNC_IND T_ID(695)
#define T_VCD_FUNCTION_UE_DECODE_SI T_ID(696)
#define T_VCD_FUNCTION_UE_DECODE_PCCH T_ID(697)
#define T_VCD_FUNCTION_UE_DECODE_CCCH T_ID(698)
#define T_VCD_FUNCTION_UE_DECODE_BCCH T_ID(699)
#define T_VCD_FUNCTION_UE_SEND_SDU T_ID(700)
#define T_VCD_FUNCTION_UE_GET_SDU T_ID(701)
#define T_VCD_FUNCTION_UE_GET_RACH T_ID(702)
#define T_VCD_FUNCTION_UE_PROCESS_RAR T_ID(703)
#define T_VCD_FUNCTION_UE_SCHEDULER T_ID(704)
#define T_VCD_FUNCTION_UE_GET_SR T_ID(705)
#define T_VCD_FUNCTION_UE_SEND_MCH_SDU T_ID(706)
#define T_VCD_FUNCTION_RLC_DATA_REQ T_ID(707)
#define T_VCD_FUNCTION_MAC_RLC_STATUS_IND T_ID(708)
#define T_VCD_FUNCTION_MAC_RLC_DATA_REQ T_ID(709)
#define T_VCD_FUNCTION_MAC_RLC_DATA_IND T_ID(710)
#define T_VCD_FUNCTION_RLC_UM_TRY_REASSEMBLY T_ID(711)
#define T_VCD_FUNCTION_RLC_UM_CHECK_TIMER_DAR_TIME_OUT T_ID(712)
#define T_VCD_FUNCTION_RLC_UM_RECEIVE_PROCESS_DAR T_ID(713)
#define T_VCD_FUNCTION_PDCP_RUN T_ID(714)
#define T_VCD_FUNCTION_PDCP_DATA_REQ T_ID(715)
#define T_VCD_FUNCTION_PDCP_DATA_IND T_ID(716)
#define T_VCD_FUNCTION_PDCP_APPLY_SECURITY T_ID(717)
#define T_VCD_FUNCTION_PDCP_VALIDATE_SECURITY T_ID(718)
#define T_VCD_FUNCTION_PDCP_FIFO_READ T_ID(719)
#define T_VCD_FUNCTION_PDCP_FIFO_READ_BUFFER T_ID(720)
#define T_VCD_FUNCTION_PDCP_FIFO_FLUSH T_ID(721)
#define T_VCD_FUNCTION_PDCP_FIFO_FLUSH_BUFFER T_ID(722)
#define T_VCD_FUNCTION_PDCP_MBMS_FIFO_READ T_ID(723)
#define T_VCD_FUNCTION_PDCP_MBMS_FIFO_READ_BUFFER T_ID(724)
#define T_VCD_FUNCTION_RRC_RX_TX T_ID(725)
#define T_VCD_FUNCTION_RRC_MAC_CONFIG T_ID(726)
#define T_VCD_FUNCTION_RRC_UE_DECODE_SIB1 T_ID(727)
#define T_VCD_FUNCTION_RRC_UE_DECODE_SI T_ID(728)
#define T_VCD_FUNCTION_GTPV1U_ENB_TASK T_ID(729)
#define T_VCD_FUNCTION_GTPV1U_PROCESS_UDP_REQ T_ID(730)
#define T_VCD_FUNCTION_GTPV1U_PROCESS_TUNNEL_DATA_REQ T_ID(731)
#define T_VCD_FUNCTION_UDP_ENB_TASK T_ID(732)
#define T_VCD_FUNCTION_EMU_TRANSPORT T_ID(733)
#define T_VCD_FUNCTION_LOG_RECORD T_ID(734)
#define T_VCD_FUNCTION_ITTI_ENQUEUE_MESSAGE T_ID(735)
#define T_VCD_FUNCTION_ITTI_DUMP_ENQUEUE_MESSAGE T_ID(736)
#define T_VCD_FUNCTION_ITTI_DUMP_ENQUEUE_MESSAGE_MALLOC T_ID(737)
#define T_VCD_FUNCTION_ITTI_RELAY_THREAD T_ID(738)
#define T_VCD_FUNCTION_TEST T_ID(739)
#define T_VCD_FUNCTION_SEND_IF4_RU T_ID(740)
#define T_VCD_FUNCTION_SEND_IF4_RU1 T_ID(741)
#define T_VCD_FUNCTION_SEND_IF4_RU2 T_ID(742)
#define T_VCD_FUNCTION_RECV_IF4_RU T_ID(743)
#define T_VCD_FUNCTION_RECV_IF4_RU1 T_ID(744)
#define T_VCD_FUNCTION_RECV_IF4_RU2 T_ID(745)
#define T_VCD_FUNCTION_SEND_IF5 T_ID(746)
#define T_VCD_FUNCTION_RECV_IF5 T_ID(747)
#define T_VCD_FUNCTION_TRX_COMPR_IF T_ID(748)
#define T_VCD_FUNCTION_TRX_DECOMPR_IF T_ID(749)
#define T_VCD_FUNCTION_NFAPI T_ID(750)
#define T_VCD_FUNCTION_GENERATE_PCFICH T_ID(751)
#define T_VCD_FUNCTION_GENERATE_DCI0 T_ID(752)
#define T_VCD_FUNCTION_GENERATE_DLSCH T_ID(753)
#define T_VCD_FUNCTION_GENERATE_PHICH T_ID(754)
#define T_VCD_FUNCTION_PDCCH_SCRAMBLING T_ID(755)
#define T_VCD_FUNCTION_PDCCH_MODULATION T_ID(756)
#define T_VCD_FUNCTION_PDCCH_INTERLEAVING T_ID(757)
#define T_VCD_FUNCTION_PDCCH_TX T_ID(758)
#define T_VCD_FUNCTION_WAKEUP_TXFH T_ID(759)
#define T_VCD_FUNCTION_gNB_PROC_RXTX0 T_ID(760)
#define T_VCD_FUNCTION_gNB_PROC_RXTX1 T_ID(761)
#define T_VCD_FUNCTION_RU_TX_WAIT T_ID(762)
#define T_VCD_FUNCTION_PHY_gNB_ULSCH_DECODING T_ID(763)
#define T_VCD_FUNCTION_gNB_PDSCH_CODEWORD_SCRAMBLING T_ID(764)
#define T_VCD_FUNCTION_gNB_DLSCH_ENCODING T_ID(765)
#define T_VCD_FUNCTION_gNB_PDSCH_MODULATION T_ID(766)
#define T_VCD_FUNCTION_PHY_gNB_PDCCH_TX T_ID(767)
#define T_VCD_FUNCTION_PHY_PROCEDURES_gNB_TX T_ID(768)
#define T_VCD_FUNCTION_PHY_PROCEDURES_gNB_COMMON_TX T_ID(769)
#define T_VCD_FUNCTION_PHY_PROCEDURES_gNB_UESPEC_RX T_ID(770)
#define T_VCD_FUNCTION_NR_RX_PUSCH T_ID(771)
#define T_VCD_FUNCTION_NR_ULSCH_PROCEDURES_RX T_ID(772)
#define T_VCD_FUNCTION_gNB_DLSCH_ULSCH_SCHEDULER T_ID(773)
#define T_VCD_FUNCTION_NR_UE_ULSCH_ENCODING T_ID(774)
#define T_VCD_FUNCTION_NR_SEGMENTATION T_ID(775)
#define T_VCD_FUNCTION_LDPC_ENCODER_OPTIM T_ID(776)
#define T_VCD_FUNCTION_NR_RATE_MATCHING_LDPC T_ID(777)
#define T_VCD_FUNCTION_NR_INTERLEAVING_LDPC T_ID(778)
#define T_VCD_FUNCTION_PSS_SYNCHRO_NR T_ID(779)
#define T_VCD_FUNCTION_PSS_SEARCH_TIME_NR T_ID(780)
#define T_VCD_FUNCTION_NR_INITIAL_UE_SYNC T_ID(781)
#define T_VCD_FUNCTION_BEAM_SWITCHING_GPIO T_ID(782)
#define T_NUMBER_OF_IDS 783
