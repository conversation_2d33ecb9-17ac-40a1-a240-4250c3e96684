# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/oaiseu

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/oaiseu/build_debug

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/oaiseu/build_debug/CMakeFiles /home/<USER>/oaiseu/build_debug/common/utils/T/tracer/view//CMakeFiles/progress.marks
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 common/utils/T/tracer/view/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/oaiseu/build_debug/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 common/utils/T/tracer/view/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 common/utils/T/tracer/view/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 common/utils/T/tracer/view/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/rule:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/rule
.PHONY : common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/rule

# Convenience name for target.
tracer_view: common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/rule
.PHONY : tracer_view

# fast build rule for target.
tracer_view/fast:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/build.make common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/build
.PHONY : tracer_view/fast

scrolltti.o: scrolltti.c.o
.PHONY : scrolltti.o

# target to build an object file
scrolltti.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/build.make common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/scrolltti.c.o
.PHONY : scrolltti.c.o

scrolltti.i: scrolltti.c.i
.PHONY : scrolltti.i

# target to preprocess a source file
scrolltti.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/build.make common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/scrolltti.c.i
.PHONY : scrolltti.c.i

scrolltti.s: scrolltti.c.s
.PHONY : scrolltti.s

# target to generate assembly for a file
scrolltti.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/build.make common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/scrolltti.c.s
.PHONY : scrolltti.c.s

stdout.o: stdout.c.o
.PHONY : stdout.o

# target to build an object file
stdout.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/build.make common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/stdout.c.o
.PHONY : stdout.c.o

stdout.i: stdout.c.i
.PHONY : stdout.i

# target to preprocess a source file
stdout.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/build.make common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/stdout.c.i
.PHONY : stdout.c.i

stdout.s: stdout.c.s
.PHONY : stdout.s

# target to generate assembly for a file
stdout.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/build.make common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/stdout.c.s
.PHONY : stdout.c.s

textlist.o: textlist.c.o
.PHONY : textlist.o

# target to build an object file
textlist.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/build.make common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/textlist.c.o
.PHONY : textlist.c.o

textlist.i: textlist.c.i
.PHONY : textlist.i

# target to preprocess a source file
textlist.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/build.make common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/textlist.c.i
.PHONY : textlist.c.i

textlist.s: textlist.c.s
.PHONY : textlist.s

# target to generate assembly for a file
textlist.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/build.make common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/textlist.c.s
.PHONY : textlist.c.s

ticktime.o: ticktime.c.o
.PHONY : ticktime.o

# target to build an object file
ticktime.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/build.make common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/ticktime.c.o
.PHONY : ticktime.c.o

ticktime.i: ticktime.c.i
.PHONY : ticktime.i

# target to preprocess a source file
ticktime.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/build.make common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/ticktime.c.i
.PHONY : ticktime.c.i

ticktime.s: ticktime.c.s
.PHONY : ticktime.s

# target to generate assembly for a file
ticktime.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/build.make common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/ticktime.c.s
.PHONY : ticktime.c.s

time.o: time.c.o
.PHONY : time.o

# target to build an object file
time.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/build.make common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/time.c.o
.PHONY : time.c.o

time.i: time.c.i
.PHONY : time.i

# target to preprocess a source file
time.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/build.make common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/time.c.i
.PHONY : time.c.i

time.s: time.c.s
.PHONY : time.s

# target to generate assembly for a file
time.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/build.make common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/time.c.s
.PHONY : time.c.s

tti.o: tti.c.o
.PHONY : tti.o

# target to build an object file
tti.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/build.make common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/tti.c.o
.PHONY : tti.c.o

tti.i: tti.c.i
.PHONY : tti.i

# target to preprocess a source file
tti.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/build.make common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/tti.c.i
.PHONY : tti.c.i

tti.s: tti.c.s
.PHONY : tti.s

# target to generate assembly for a file
tti.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/build.make common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/tti.c.s
.PHONY : tti.c.s

xy.o: xy.c.o
.PHONY : xy.o

# target to build an object file
xy.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/build.make common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/xy.c.o
.PHONY : xy.c.o

xy.i: xy.c.i
.PHONY : xy.i

# target to preprocess a source file
xy.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/build.make common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/xy.c.i
.PHONY : xy.c.i

xy.s: xy.c.s
.PHONY : xy.s

# target to generate assembly for a file
xy.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/build.make common/utils/T/tracer/view/CMakeFiles/tracer_view.dir/xy.c.s
.PHONY : xy.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... tracer_view"
	@echo "... scrolltti.o"
	@echo "... scrolltti.i"
	@echo "... scrolltti.s"
	@echo "... stdout.o"
	@echo "... stdout.i"
	@echo "... stdout.s"
	@echo "... textlist.o"
	@echo "... textlist.i"
	@echo "... textlist.s"
	@echo "... ticktime.o"
	@echo "... ticktime.i"
	@echo "... ticktime.s"
	@echo "... time.o"
	@echo "... time.i"
	@echo "... time.s"
	@echo "... tti.o"
	@echo "... tti.i"
	@echo "... tti.s"
	@echo "... xy.o"
	@echo "... xy.i"
	@echo "... xy.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

