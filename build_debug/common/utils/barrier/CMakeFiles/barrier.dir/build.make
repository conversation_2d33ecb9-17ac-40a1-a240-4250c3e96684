# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/oaiseu

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/oaiseu/build_debug

# Include any dependencies generated for this target.
include common/utils/barrier/CMakeFiles/barrier.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include common/utils/barrier/CMakeFiles/barrier.dir/compiler_depend.make

# Include the progress variables for this target.
include common/utils/barrier/CMakeFiles/barrier.dir/progress.make

# Include the compile flags for this target's objects.
include common/utils/barrier/CMakeFiles/barrier.dir/flags.make

common/utils/barrier/CMakeFiles/barrier.dir/barrier.c.o: common/utils/barrier/CMakeFiles/barrier.dir/flags.make
common/utils/barrier/CMakeFiles/barrier.dir/barrier.c.o: ../common/utils/barrier/barrier.c
common/utils/barrier/CMakeFiles/barrier.dir/barrier.c.o: common/utils/barrier/CMakeFiles/barrier.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object common/utils/barrier/CMakeFiles/barrier.dir/barrier.c.o"
	cd /home/<USER>/oaiseu/build_debug/common/utils/barrier && /usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT common/utils/barrier/CMakeFiles/barrier.dir/barrier.c.o -MF CMakeFiles/barrier.dir/barrier.c.o.d -o CMakeFiles/barrier.dir/barrier.c.o -c /home/<USER>/oaiseu/common/utils/barrier/barrier.c

common/utils/barrier/CMakeFiles/barrier.dir/barrier.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/barrier.dir/barrier.c.i"
	cd /home/<USER>/oaiseu/build_debug/common/utils/barrier && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/oaiseu/common/utils/barrier/barrier.c > CMakeFiles/barrier.dir/barrier.c.i

common/utils/barrier/CMakeFiles/barrier.dir/barrier.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/barrier.dir/barrier.c.s"
	cd /home/<USER>/oaiseu/build_debug/common/utils/barrier && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/oaiseu/common/utils/barrier/barrier.c -o CMakeFiles/barrier.dir/barrier.c.s

# Object files for target barrier
barrier_OBJECTS = \
"CMakeFiles/barrier.dir/barrier.c.o"

# External object files for target barrier
barrier_EXTERNAL_OBJECTS =

common/utils/barrier/libbarrier.a: common/utils/barrier/CMakeFiles/barrier.dir/barrier.c.o
common/utils/barrier/libbarrier.a: common/utils/barrier/CMakeFiles/barrier.dir/build.make
common/utils/barrier/libbarrier.a: common/utils/barrier/CMakeFiles/barrier.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking C static library libbarrier.a"
	cd /home/<USER>/oaiseu/build_debug/common/utils/barrier && $(CMAKE_COMMAND) -P CMakeFiles/barrier.dir/cmake_clean_target.cmake
	cd /home/<USER>/oaiseu/build_debug/common/utils/barrier && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/barrier.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
common/utils/barrier/CMakeFiles/barrier.dir/build: common/utils/barrier/libbarrier.a
.PHONY : common/utils/barrier/CMakeFiles/barrier.dir/build

common/utils/barrier/CMakeFiles/barrier.dir/clean:
	cd /home/<USER>/oaiseu/build_debug/common/utils/barrier && $(CMAKE_COMMAND) -P CMakeFiles/barrier.dir/cmake_clean.cmake
.PHONY : common/utils/barrier/CMakeFiles/barrier.dir/clean

common/utils/barrier/CMakeFiles/barrier.dir/depend:
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/oaiseu /home/<USER>/oaiseu/common/utils/barrier /home/<USER>/oaiseu/build_debug /home/<USER>/oaiseu/build_debug/common/utils/barrier /home/<USER>/oaiseu/build_debug/common/utils/barrier/CMakeFiles/barrier.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : common/utils/barrier/CMakeFiles/barrier.dir/depend

