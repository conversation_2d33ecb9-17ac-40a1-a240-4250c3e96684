# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/oaiseu

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/oaiseu/build_debug

# Include any dependencies generated for this target.
include openair1/PHY/nr_phy_common/CMakeFiles/nr_ue_phy_meas.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include openair1/PHY/nr_phy_common/CMakeFiles/nr_ue_phy_meas.dir/compiler_depend.make

# Include the progress variables for this target.
include openair1/PHY/nr_phy_common/CMakeFiles/nr_ue_phy_meas.dir/progress.make

# Include the compile flags for this target's objects.
include openair1/PHY/nr_phy_common/CMakeFiles/nr_ue_phy_meas.dir/flags.make

openair1/PHY/nr_phy_common/CMakeFiles/nr_ue_phy_meas.dir/src/nr_ue_phy_meas.c.o: openair1/PHY/nr_phy_common/CMakeFiles/nr_ue_phy_meas.dir/flags.make
openair1/PHY/nr_phy_common/CMakeFiles/nr_ue_phy_meas.dir/src/nr_ue_phy_meas.c.o: ../openair1/PHY/nr_phy_common/src/nr_ue_phy_meas.c
openair1/PHY/nr_phy_common/CMakeFiles/nr_ue_phy_meas.dir/src/nr_ue_phy_meas.c.o: openair1/PHY/nr_phy_common/CMakeFiles/nr_ue_phy_meas.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object openair1/PHY/nr_phy_common/CMakeFiles/nr_ue_phy_meas.dir/src/nr_ue_phy_meas.c.o"
	cd /home/<USER>/oaiseu/build_debug/openair1/PHY/nr_phy_common && /usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT openair1/PHY/nr_phy_common/CMakeFiles/nr_ue_phy_meas.dir/src/nr_ue_phy_meas.c.o -MF CMakeFiles/nr_ue_phy_meas.dir/src/nr_ue_phy_meas.c.o.d -o CMakeFiles/nr_ue_phy_meas.dir/src/nr_ue_phy_meas.c.o -c /home/<USER>/oaiseu/openair1/PHY/nr_phy_common/src/nr_ue_phy_meas.c

openair1/PHY/nr_phy_common/CMakeFiles/nr_ue_phy_meas.dir/src/nr_ue_phy_meas.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nr_ue_phy_meas.dir/src/nr_ue_phy_meas.c.i"
	cd /home/<USER>/oaiseu/build_debug/openair1/PHY/nr_phy_common && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/oaiseu/openair1/PHY/nr_phy_common/src/nr_ue_phy_meas.c > CMakeFiles/nr_ue_phy_meas.dir/src/nr_ue_phy_meas.c.i

openair1/PHY/nr_phy_common/CMakeFiles/nr_ue_phy_meas.dir/src/nr_ue_phy_meas.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nr_ue_phy_meas.dir/src/nr_ue_phy_meas.c.s"
	cd /home/<USER>/oaiseu/build_debug/openair1/PHY/nr_phy_common && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/oaiseu/openair1/PHY/nr_phy_common/src/nr_ue_phy_meas.c -o CMakeFiles/nr_ue_phy_meas.dir/src/nr_ue_phy_meas.c.s

# Object files for target nr_ue_phy_meas
nr_ue_phy_meas_OBJECTS = \
"CMakeFiles/nr_ue_phy_meas.dir/src/nr_ue_phy_meas.c.o"

# External object files for target nr_ue_phy_meas
nr_ue_phy_meas_EXTERNAL_OBJECTS =

openair1/PHY/nr_phy_common/libnr_ue_phy_meas.a: openair1/PHY/nr_phy_common/CMakeFiles/nr_ue_phy_meas.dir/src/nr_ue_phy_meas.c.o
openair1/PHY/nr_phy_common/libnr_ue_phy_meas.a: openair1/PHY/nr_phy_common/CMakeFiles/nr_ue_phy_meas.dir/build.make
openair1/PHY/nr_phy_common/libnr_ue_phy_meas.a: openair1/PHY/nr_phy_common/CMakeFiles/nr_ue_phy_meas.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking C static library libnr_ue_phy_meas.a"
	cd /home/<USER>/oaiseu/build_debug/openair1/PHY/nr_phy_common && $(CMAKE_COMMAND) -P CMakeFiles/nr_ue_phy_meas.dir/cmake_clean_target.cmake
	cd /home/<USER>/oaiseu/build_debug/openair1/PHY/nr_phy_common && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/nr_ue_phy_meas.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
openair1/PHY/nr_phy_common/CMakeFiles/nr_ue_phy_meas.dir/build: openair1/PHY/nr_phy_common/libnr_ue_phy_meas.a
.PHONY : openair1/PHY/nr_phy_common/CMakeFiles/nr_ue_phy_meas.dir/build

openair1/PHY/nr_phy_common/CMakeFiles/nr_ue_phy_meas.dir/clean:
	cd /home/<USER>/oaiseu/build_debug/openair1/PHY/nr_phy_common && $(CMAKE_COMMAND) -P CMakeFiles/nr_ue_phy_meas.dir/cmake_clean.cmake
.PHONY : openair1/PHY/nr_phy_common/CMakeFiles/nr_ue_phy_meas.dir/clean

openair1/PHY/nr_phy_common/CMakeFiles/nr_ue_phy_meas.dir/depend:
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/oaiseu /home/<USER>/oaiseu/openair1/PHY/nr_phy_common /home/<USER>/oaiseu/build_debug /home/<USER>/oaiseu/build_debug/openair1/PHY/nr_phy_common /home/<USER>/oaiseu/build_debug/openair1/PHY/nr_phy_common/CMakeFiles/nr_ue_phy_meas.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : openair1/PHY/nr_phy_common/CMakeFiles/nr_ue_phy_meas.dir/depend

