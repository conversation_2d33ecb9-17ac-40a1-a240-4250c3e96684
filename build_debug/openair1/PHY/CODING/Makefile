# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/oaiseu

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/oaiseu/build_debug

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/oaiseu/build_debug/CMakeFiles /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING//CMakeFiles/progress.marks
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 openair1/PHY/CODING/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/oaiseu/build_debug/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 openair1/PHY/CODING/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 openair1/PHY/CODING/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 openair1/PHY/CODING/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
openair1/PHY/CODING/CMakeFiles/coding.dir/rule:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 openair1/PHY/CODING/CMakeFiles/coding.dir/rule
.PHONY : openair1/PHY/CODING/CMakeFiles/coding.dir/rule

# Convenience name for target.
coding: openair1/PHY/CODING/CMakeFiles/coding.dir/rule
.PHONY : coding

# fast build rule for target.
coding/fast:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/coding.dir/build.make openair1/PHY/CODING/CMakeFiles/coding.dir/build
.PHONY : coding/fast

# Convenience name for target.
openair1/PHY/CODING/CMakeFiles/ldpc_orig.dir/rule:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 openair1/PHY/CODING/CMakeFiles/ldpc_orig.dir/rule
.PHONY : openair1/PHY/CODING/CMakeFiles/ldpc_orig.dir/rule

# Convenience name for target.
ldpc_orig: openair1/PHY/CODING/CMakeFiles/ldpc_orig.dir/rule
.PHONY : ldpc_orig

# fast build rule for target.
ldpc_orig/fast:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/ldpc_orig.dir/build.make openair1/PHY/CODING/CMakeFiles/ldpc_orig.dir/build
.PHONY : ldpc_orig/fast

# Convenience name for target.
openair1/PHY/CODING/CMakeFiles/ldpc.dir/rule:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 openair1/PHY/CODING/CMakeFiles/ldpc.dir/rule
.PHONY : openair1/PHY/CODING/CMakeFiles/ldpc.dir/rule

# Convenience name for target.
ldpc: openair1/PHY/CODING/CMakeFiles/ldpc.dir/rule
.PHONY : ldpc

# fast build rule for target.
ldpc/fast:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/ldpc.dir/build.make openair1/PHY/CODING/CMakeFiles/ldpc.dir/build
.PHONY : ldpc/fast

# Convenience name for target.
openair1/PHY/CODING/CMakeFiles/nrLDPC_decoder_kernels_CL.dir/rule:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 openair1/PHY/CODING/CMakeFiles/nrLDPC_decoder_kernels_CL.dir/rule
.PHONY : openair1/PHY/CODING/CMakeFiles/nrLDPC_decoder_kernels_CL.dir/rule

# Convenience name for target.
nrLDPC_decoder_kernels_CL: openair1/PHY/CODING/CMakeFiles/nrLDPC_decoder_kernels_CL.dir/rule
.PHONY : nrLDPC_decoder_kernels_CL

# fast build rule for target.
nrLDPC_decoder_kernels_CL/fast:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/nrLDPC_decoder_kernels_CL.dir/build.make openair1/PHY/CODING/CMakeFiles/nrLDPC_decoder_kernels_CL.dir/build
.PHONY : nrLDPC_decoder_kernels_CL/fast

# Convenience name for target.
openair1/PHY/CODING/CMakeFiles/ldpc_cl.dir/rule:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 openair1/PHY/CODING/CMakeFiles/ldpc_cl.dir/rule
.PHONY : openair1/PHY/CODING/CMakeFiles/ldpc_cl.dir/rule

# Convenience name for target.
ldpc_cl: openair1/PHY/CODING/CMakeFiles/ldpc_cl.dir/rule
.PHONY : ldpc_cl

# fast build rule for target.
ldpc_cl/fast:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/ldpc_cl.dir/build.make openair1/PHY/CODING/CMakeFiles/ldpc_cl.dir/build
.PHONY : ldpc_cl/fast

3gpplte.o: 3gpplte.c.o
.PHONY : 3gpplte.o

# target to build an object file
3gpplte.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/coding.dir/build.make openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte.c.o
.PHONY : 3gpplte.c.o

3gpplte.i: 3gpplte.c.i
.PHONY : 3gpplte.i

# target to preprocess a source file
3gpplte.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/coding.dir/build.make openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte.c.i
.PHONY : 3gpplte.c.i

3gpplte.s: 3gpplte.c.s
.PHONY : 3gpplte.s

# target to generate assembly for a file
3gpplte.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/coding.dir/build.make openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte.c.s
.PHONY : 3gpplte.c.s

3gpplte_sse.o: 3gpplte_sse.c.o
.PHONY : 3gpplte_sse.o

# target to build an object file
3gpplte_sse.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/coding.dir/build.make openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_sse.c.o
.PHONY : 3gpplte_sse.c.o

3gpplte_sse.i: 3gpplte_sse.c.i
.PHONY : 3gpplte_sse.i

# target to preprocess a source file
3gpplte_sse.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/coding.dir/build.make openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_sse.c.i
.PHONY : 3gpplte_sse.c.i

3gpplte_sse.s: 3gpplte_sse.c.s
.PHONY : 3gpplte_sse.s

# target to generate assembly for a file
3gpplte_sse.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/coding.dir/build.make openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_sse.c.s
.PHONY : 3gpplte_sse.c.s

3gpplte_turbo_decoder.o: 3gpplte_turbo_decoder.c.o
.PHONY : 3gpplte_turbo_decoder.o

# target to build an object file
3gpplte_turbo_decoder.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/coding.dir/build.make openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder.c.o
.PHONY : 3gpplte_turbo_decoder.c.o

3gpplte_turbo_decoder.i: 3gpplte_turbo_decoder.c.i
.PHONY : 3gpplte_turbo_decoder.i

# target to preprocess a source file
3gpplte_turbo_decoder.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/coding.dir/build.make openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder.c.i
.PHONY : 3gpplte_turbo_decoder.c.i

3gpplte_turbo_decoder.s: 3gpplte_turbo_decoder.c.s
.PHONY : 3gpplte_turbo_decoder.s

# target to generate assembly for a file
3gpplte_turbo_decoder.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/coding.dir/build.make openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder.c.s
.PHONY : 3gpplte_turbo_decoder.c.s

3gpplte_turbo_decoder_avx2_16bit.o: 3gpplte_turbo_decoder_avx2_16bit.c.o
.PHONY : 3gpplte_turbo_decoder_avx2_16bit.o

# target to build an object file
3gpplte_turbo_decoder_avx2_16bit.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/coding.dir/build.make openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_avx2_16bit.c.o
.PHONY : 3gpplte_turbo_decoder_avx2_16bit.c.o

3gpplte_turbo_decoder_avx2_16bit.i: 3gpplte_turbo_decoder_avx2_16bit.c.i
.PHONY : 3gpplte_turbo_decoder_avx2_16bit.i

# target to preprocess a source file
3gpplte_turbo_decoder_avx2_16bit.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/coding.dir/build.make openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_avx2_16bit.c.i
.PHONY : 3gpplte_turbo_decoder_avx2_16bit.c.i

3gpplte_turbo_decoder_avx2_16bit.s: 3gpplte_turbo_decoder_avx2_16bit.c.s
.PHONY : 3gpplte_turbo_decoder_avx2_16bit.s

# target to generate assembly for a file
3gpplte_turbo_decoder_avx2_16bit.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/coding.dir/build.make openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_avx2_16bit.c.s
.PHONY : 3gpplte_turbo_decoder_avx2_16bit.c.s

3gpplte_turbo_decoder_sse_16bit.o: 3gpplte_turbo_decoder_sse_16bit.c.o
.PHONY : 3gpplte_turbo_decoder_sse_16bit.o

# target to build an object file
3gpplte_turbo_decoder_sse_16bit.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/coding.dir/build.make openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_16bit.c.o
.PHONY : 3gpplte_turbo_decoder_sse_16bit.c.o

3gpplte_turbo_decoder_sse_16bit.i: 3gpplte_turbo_decoder_sse_16bit.c.i
.PHONY : 3gpplte_turbo_decoder_sse_16bit.i

# target to preprocess a source file
3gpplte_turbo_decoder_sse_16bit.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/coding.dir/build.make openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_16bit.c.i
.PHONY : 3gpplte_turbo_decoder_sse_16bit.c.i

3gpplte_turbo_decoder_sse_16bit.s: 3gpplte_turbo_decoder_sse_16bit.c.s
.PHONY : 3gpplte_turbo_decoder_sse_16bit.s

# target to generate assembly for a file
3gpplte_turbo_decoder_sse_16bit.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/coding.dir/build.make openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_16bit.c.s
.PHONY : 3gpplte_turbo_decoder_sse_16bit.c.s

3gpplte_turbo_decoder_sse_8bit.o: 3gpplte_turbo_decoder_sse_8bit.c.o
.PHONY : 3gpplte_turbo_decoder_sse_8bit.o

# target to build an object file
3gpplte_turbo_decoder_sse_8bit.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/coding.dir/build.make openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_8bit.c.o
.PHONY : 3gpplte_turbo_decoder_sse_8bit.c.o

3gpplte_turbo_decoder_sse_8bit.i: 3gpplte_turbo_decoder_sse_8bit.c.i
.PHONY : 3gpplte_turbo_decoder_sse_8bit.i

# target to preprocess a source file
3gpplte_turbo_decoder_sse_8bit.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/coding.dir/build.make openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_8bit.c.i
.PHONY : 3gpplte_turbo_decoder_sse_8bit.c.i

3gpplte_turbo_decoder_sse_8bit.s: 3gpplte_turbo_decoder_sse_8bit.c.s
.PHONY : 3gpplte_turbo_decoder_sse_8bit.s

# target to generate assembly for a file
3gpplte_turbo_decoder_sse_8bit.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/coding.dir/build.make openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_8bit.c.s
.PHONY : 3gpplte_turbo_decoder_sse_8bit.c.s

nrLDPC_decoder/nrLDPC_decoder.o: nrLDPC_decoder/nrLDPC_decoder.c.o
.PHONY : nrLDPC_decoder/nrLDPC_decoder.o

# target to build an object file
nrLDPC_decoder/nrLDPC_decoder.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/ldpc_orig.dir/build.make openair1/PHY/CODING/CMakeFiles/ldpc_orig.dir/nrLDPC_decoder/nrLDPC_decoder.c.o
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/ldpc.dir/build.make openair1/PHY/CODING/CMakeFiles/ldpc.dir/nrLDPC_decoder/nrLDPC_decoder.c.o
.PHONY : nrLDPC_decoder/nrLDPC_decoder.c.o

nrLDPC_decoder/nrLDPC_decoder.i: nrLDPC_decoder/nrLDPC_decoder.c.i
.PHONY : nrLDPC_decoder/nrLDPC_decoder.i

# target to preprocess a source file
nrLDPC_decoder/nrLDPC_decoder.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/ldpc_orig.dir/build.make openair1/PHY/CODING/CMakeFiles/ldpc_orig.dir/nrLDPC_decoder/nrLDPC_decoder.c.i
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/ldpc.dir/build.make openair1/PHY/CODING/CMakeFiles/ldpc.dir/nrLDPC_decoder/nrLDPC_decoder.c.i
.PHONY : nrLDPC_decoder/nrLDPC_decoder.c.i

nrLDPC_decoder/nrLDPC_decoder.s: nrLDPC_decoder/nrLDPC_decoder.c.s
.PHONY : nrLDPC_decoder/nrLDPC_decoder.s

# target to generate assembly for a file
nrLDPC_decoder/nrLDPC_decoder.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/ldpc_orig.dir/build.make openair1/PHY/CODING/CMakeFiles/ldpc_orig.dir/nrLDPC_decoder/nrLDPC_decoder.c.s
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/ldpc.dir/build.make openair1/PHY/CODING/CMakeFiles/ldpc.dir/nrLDPC_decoder/nrLDPC_decoder.c.s
.PHONY : nrLDPC_decoder/nrLDPC_decoder.c.s

nrLDPC_decoder/nrLDPC_decoder_CL.o: nrLDPC_decoder/nrLDPC_decoder_CL.c.o
.PHONY : nrLDPC_decoder/nrLDPC_decoder_CL.o

# target to build an object file
nrLDPC_decoder/nrLDPC_decoder_CL.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/ldpc_cl.dir/build.make openair1/PHY/CODING/CMakeFiles/ldpc_cl.dir/nrLDPC_decoder/nrLDPC_decoder_CL.c.o
.PHONY : nrLDPC_decoder/nrLDPC_decoder_CL.c.o

nrLDPC_decoder/nrLDPC_decoder_CL.i: nrLDPC_decoder/nrLDPC_decoder_CL.c.i
.PHONY : nrLDPC_decoder/nrLDPC_decoder_CL.i

# target to preprocess a source file
nrLDPC_decoder/nrLDPC_decoder_CL.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/ldpc_cl.dir/build.make openair1/PHY/CODING/CMakeFiles/ldpc_cl.dir/nrLDPC_decoder/nrLDPC_decoder_CL.c.i
.PHONY : nrLDPC_decoder/nrLDPC_decoder_CL.c.i

nrLDPC_decoder/nrLDPC_decoder_CL.s: nrLDPC_decoder/nrLDPC_decoder_CL.c.s
.PHONY : nrLDPC_decoder/nrLDPC_decoder_CL.s

# target to generate assembly for a file
nrLDPC_decoder/nrLDPC_decoder_CL.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/ldpc_cl.dir/build.make openair1/PHY/CODING/CMakeFiles/ldpc_cl.dir/nrLDPC_decoder/nrLDPC_decoder_CL.c.s
.PHONY : nrLDPC_decoder/nrLDPC_decoder_CL.c.s

nrLDPC_encoder/ldpc_encoder.o: nrLDPC_encoder/ldpc_encoder.c.o
.PHONY : nrLDPC_encoder/ldpc_encoder.o

# target to build an object file
nrLDPC_encoder/ldpc_encoder.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/ldpc_orig.dir/build.make openair1/PHY/CODING/CMakeFiles/ldpc_orig.dir/nrLDPC_encoder/ldpc_encoder.c.o
.PHONY : nrLDPC_encoder/ldpc_encoder.c.o

nrLDPC_encoder/ldpc_encoder.i: nrLDPC_encoder/ldpc_encoder.c.i
.PHONY : nrLDPC_encoder/ldpc_encoder.i

# target to preprocess a source file
nrLDPC_encoder/ldpc_encoder.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/ldpc_orig.dir/build.make openair1/PHY/CODING/CMakeFiles/ldpc_orig.dir/nrLDPC_encoder/ldpc_encoder.c.i
.PHONY : nrLDPC_encoder/ldpc_encoder.c.i

nrLDPC_encoder/ldpc_encoder.s: nrLDPC_encoder/ldpc_encoder.c.s
.PHONY : nrLDPC_encoder/ldpc_encoder.s

# target to generate assembly for a file
nrLDPC_encoder/ldpc_encoder.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/ldpc_orig.dir/build.make openair1/PHY/CODING/CMakeFiles/ldpc_orig.dir/nrLDPC_encoder/ldpc_encoder.c.s
.PHONY : nrLDPC_encoder/ldpc_encoder.c.s

nrLDPC_encoder/ldpc_encoder_optim8segmulti.o: nrLDPC_encoder/ldpc_encoder_optim8segmulti.c.o
.PHONY : nrLDPC_encoder/ldpc_encoder_optim8segmulti.o

# target to build an object file
nrLDPC_encoder/ldpc_encoder_optim8segmulti.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/ldpc.dir/build.make openair1/PHY/CODING/CMakeFiles/ldpc.dir/nrLDPC_encoder/ldpc_encoder_optim8segmulti.c.o
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/ldpc_cl.dir/build.make openair1/PHY/CODING/CMakeFiles/ldpc_cl.dir/nrLDPC_encoder/ldpc_encoder_optim8segmulti.c.o
.PHONY : nrLDPC_encoder/ldpc_encoder_optim8segmulti.c.o

nrLDPC_encoder/ldpc_encoder_optim8segmulti.i: nrLDPC_encoder/ldpc_encoder_optim8segmulti.c.i
.PHONY : nrLDPC_encoder/ldpc_encoder_optim8segmulti.i

# target to preprocess a source file
nrLDPC_encoder/ldpc_encoder_optim8segmulti.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/ldpc.dir/build.make openair1/PHY/CODING/CMakeFiles/ldpc.dir/nrLDPC_encoder/ldpc_encoder_optim8segmulti.c.i
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/ldpc_cl.dir/build.make openair1/PHY/CODING/CMakeFiles/ldpc_cl.dir/nrLDPC_encoder/ldpc_encoder_optim8segmulti.c.i
.PHONY : nrLDPC_encoder/ldpc_encoder_optim8segmulti.c.i

nrLDPC_encoder/ldpc_encoder_optim8segmulti.s: nrLDPC_encoder/ldpc_encoder_optim8segmulti.c.s
.PHONY : nrLDPC_encoder/ldpc_encoder_optim8segmulti.s

# target to generate assembly for a file
nrLDPC_encoder/ldpc_encoder_optim8segmulti.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/ldpc.dir/build.make openair1/PHY/CODING/CMakeFiles/ldpc.dir/nrLDPC_encoder/ldpc_encoder_optim8segmulti.c.s
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair1/PHY/CODING/CMakeFiles/ldpc_cl.dir/build.make openair1/PHY/CODING/CMakeFiles/ldpc_cl.dir/nrLDPC_encoder/ldpc_encoder_optim8segmulti.c.s
.PHONY : nrLDPC_encoder/ldpc_encoder_optim8segmulti.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... nrLDPC_decoder_kernels_CL"
	@echo "... coding"
	@echo "... ldpc"
	@echo "... ldpc_cl"
	@echo "... ldpc_orig"
	@echo "... 3gpplte.o"
	@echo "... 3gpplte.i"
	@echo "... 3gpplte.s"
	@echo "... 3gpplte_sse.o"
	@echo "... 3gpplte_sse.i"
	@echo "... 3gpplte_sse.s"
	@echo "... 3gpplte_turbo_decoder.o"
	@echo "... 3gpplte_turbo_decoder.i"
	@echo "... 3gpplte_turbo_decoder.s"
	@echo "... 3gpplte_turbo_decoder_avx2_16bit.o"
	@echo "... 3gpplte_turbo_decoder_avx2_16bit.i"
	@echo "... 3gpplte_turbo_decoder_avx2_16bit.s"
	@echo "... 3gpplte_turbo_decoder_sse_16bit.o"
	@echo "... 3gpplte_turbo_decoder_sse_16bit.i"
	@echo "... 3gpplte_turbo_decoder_sse_16bit.s"
	@echo "... 3gpplte_turbo_decoder_sse_8bit.o"
	@echo "... 3gpplte_turbo_decoder_sse_8bit.i"
	@echo "... 3gpplte_turbo_decoder_sse_8bit.s"
	@echo "... nrLDPC_decoder/nrLDPC_decoder.o"
	@echo "... nrLDPC_decoder/nrLDPC_decoder.i"
	@echo "... nrLDPC_decoder/nrLDPC_decoder.s"
	@echo "... nrLDPC_decoder/nrLDPC_decoder_CL.o"
	@echo "... nrLDPC_decoder/nrLDPC_decoder_CL.i"
	@echo "... nrLDPC_decoder/nrLDPC_decoder_CL.s"
	@echo "... nrLDPC_encoder/ldpc_encoder.o"
	@echo "... nrLDPC_encoder/ldpc_encoder.i"
	@echo "... nrLDPC_encoder/ldpc_encoder.s"
	@echo "... nrLDPC_encoder/ldpc_encoder_optim8segmulti.o"
	@echo "... nrLDPC_encoder/ldpc_encoder_optim8segmulti.i"
	@echo "... nrLDPC_encoder/ldpc_encoder_optim8segmulti.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

