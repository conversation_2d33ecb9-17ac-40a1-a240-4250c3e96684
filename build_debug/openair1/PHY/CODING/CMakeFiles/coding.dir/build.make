# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/oaiseu

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/oaiseu/build_debug

# Include any dependencies generated for this target.
include openair1/PHY/CODING/CMakeFiles/coding.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include openair1/PHY/CODING/CMakeFiles/coding.dir/compiler_depend.make

# Include the progress variables for this target.
include openair1/PHY/CODING/CMakeFiles/coding.dir/progress.make

# Include the compile flags for this target's objects.
include openair1/PHY/CODING/CMakeFiles/coding.dir/flags.make

openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_sse.c.o: openair1/PHY/CODING/CMakeFiles/coding.dir/flags.make
openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_sse.c.o: ../openair1/PHY/CODING/3gpplte_sse.c
openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_sse.c.o: openair1/PHY/CODING/CMakeFiles/coding.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_sse.c.o"
	cd /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING && /usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_sse.c.o -MF CMakeFiles/coding.dir/3gpplte_sse.c.o.d -o CMakeFiles/coding.dir/3gpplte_sse.c.o -c /home/<USER>/oaiseu/openair1/PHY/CODING/3gpplte_sse.c

openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_sse.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/coding.dir/3gpplte_sse.c.i"
	cd /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/oaiseu/openair1/PHY/CODING/3gpplte_sse.c > CMakeFiles/coding.dir/3gpplte_sse.c.i

openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_sse.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/coding.dir/3gpplte_sse.c.s"
	cd /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/oaiseu/openair1/PHY/CODING/3gpplte_sse.c -o CMakeFiles/coding.dir/3gpplte_sse.c.s

openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte.c.o: openair1/PHY/CODING/CMakeFiles/coding.dir/flags.make
openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte.c.o: ../openair1/PHY/CODING/3gpplte.c
openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte.c.o: openair1/PHY/CODING/CMakeFiles/coding.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte.c.o"
	cd /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING && /usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte.c.o -MF CMakeFiles/coding.dir/3gpplte.c.o.d -o CMakeFiles/coding.dir/3gpplte.c.o -c /home/<USER>/oaiseu/openair1/PHY/CODING/3gpplte.c

openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/coding.dir/3gpplte.c.i"
	cd /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/oaiseu/openair1/PHY/CODING/3gpplte.c > CMakeFiles/coding.dir/3gpplte.c.i

openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/coding.dir/3gpplte.c.s"
	cd /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/oaiseu/openair1/PHY/CODING/3gpplte.c -o CMakeFiles/coding.dir/3gpplte.c.s

openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_8bit.c.o: openair1/PHY/CODING/CMakeFiles/coding.dir/flags.make
openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_8bit.c.o: ../openair1/PHY/CODING/3gpplte_turbo_decoder_sse_8bit.c
openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_8bit.c.o: openair1/PHY/CODING/CMakeFiles/coding.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_8bit.c.o"
	cd /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING && /usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_8bit.c.o -MF CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_8bit.c.o.d -o CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_8bit.c.o -c /home/<USER>/oaiseu/openair1/PHY/CODING/3gpplte_turbo_decoder_sse_8bit.c

openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_8bit.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_8bit.c.i"
	cd /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/oaiseu/openair1/PHY/CODING/3gpplte_turbo_decoder_sse_8bit.c > CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_8bit.c.i

openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_8bit.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_8bit.c.s"
	cd /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/oaiseu/openair1/PHY/CODING/3gpplte_turbo_decoder_sse_8bit.c -o CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_8bit.c.s

openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_16bit.c.o: openair1/PHY/CODING/CMakeFiles/coding.dir/flags.make
openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_16bit.c.o: ../openair1/PHY/CODING/3gpplte_turbo_decoder_sse_16bit.c
openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_16bit.c.o: openair1/PHY/CODING/CMakeFiles/coding.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_16bit.c.o"
	cd /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING && /usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_16bit.c.o -MF CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_16bit.c.o.d -o CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_16bit.c.o -c /home/<USER>/oaiseu/openair1/PHY/CODING/3gpplte_turbo_decoder_sse_16bit.c

openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_16bit.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_16bit.c.i"
	cd /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/oaiseu/openair1/PHY/CODING/3gpplte_turbo_decoder_sse_16bit.c > CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_16bit.c.i

openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_16bit.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_16bit.c.s"
	cd /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/oaiseu/openair1/PHY/CODING/3gpplte_turbo_decoder_sse_16bit.c -o CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_16bit.c.s

openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_avx2_16bit.c.o: openair1/PHY/CODING/CMakeFiles/coding.dir/flags.make
openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_avx2_16bit.c.o: ../openair1/PHY/CODING/3gpplte_turbo_decoder_avx2_16bit.c
openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_avx2_16bit.c.o: openair1/PHY/CODING/CMakeFiles/coding.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_avx2_16bit.c.o"
	cd /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING && /usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_avx2_16bit.c.o -MF CMakeFiles/coding.dir/3gpplte_turbo_decoder_avx2_16bit.c.o.d -o CMakeFiles/coding.dir/3gpplte_turbo_decoder_avx2_16bit.c.o -c /home/<USER>/oaiseu/openair1/PHY/CODING/3gpplte_turbo_decoder_avx2_16bit.c

openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_avx2_16bit.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/coding.dir/3gpplte_turbo_decoder_avx2_16bit.c.i"
	cd /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/oaiseu/openair1/PHY/CODING/3gpplte_turbo_decoder_avx2_16bit.c > CMakeFiles/coding.dir/3gpplte_turbo_decoder_avx2_16bit.c.i

openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_avx2_16bit.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/coding.dir/3gpplte_turbo_decoder_avx2_16bit.c.s"
	cd /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/oaiseu/openair1/PHY/CODING/3gpplte_turbo_decoder_avx2_16bit.c -o CMakeFiles/coding.dir/3gpplte_turbo_decoder_avx2_16bit.c.s

openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder.c.o: openair1/PHY/CODING/CMakeFiles/coding.dir/flags.make
openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder.c.o: ../openair1/PHY/CODING/3gpplte_turbo_decoder.c
openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder.c.o: openair1/PHY/CODING/CMakeFiles/coding.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder.c.o"
	cd /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING && /usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder.c.o -MF CMakeFiles/coding.dir/3gpplte_turbo_decoder.c.o.d -o CMakeFiles/coding.dir/3gpplte_turbo_decoder.c.o -c /home/<USER>/oaiseu/openair1/PHY/CODING/3gpplte_turbo_decoder.c

openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/coding.dir/3gpplte_turbo_decoder.c.i"
	cd /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/oaiseu/openair1/PHY/CODING/3gpplte_turbo_decoder.c > CMakeFiles/coding.dir/3gpplte_turbo_decoder.c.i

openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/coding.dir/3gpplte_turbo_decoder.c.s"
	cd /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/oaiseu/openair1/PHY/CODING/3gpplte_turbo_decoder.c -o CMakeFiles/coding.dir/3gpplte_turbo_decoder.c.s

# Object files for target coding
coding_OBJECTS = \
"CMakeFiles/coding.dir/3gpplte_sse.c.o" \
"CMakeFiles/coding.dir/3gpplte.c.o" \
"CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_8bit.c.o" \
"CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_16bit.c.o" \
"CMakeFiles/coding.dir/3gpplte_turbo_decoder_avx2_16bit.c.o" \
"CMakeFiles/coding.dir/3gpplte_turbo_decoder.c.o"

# External object files for target coding
coding_EXTERNAL_OBJECTS =

libcoding.so: openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_sse.c.o
libcoding.so: openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte.c.o
libcoding.so: openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_8bit.c.o
libcoding.so: openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_16bit.c.o
libcoding.so: openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_avx2_16bit.c.o
libcoding.so: openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder.c.o
libcoding.so: openair1/PHY/CODING/CMakeFiles/coding.dir/build.make
libcoding.so: openair1/PHY/CODING/CMakeFiles/coding.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Linking C shared module ../../../libcoding.so"
	cd /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/coding.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
openair1/PHY/CODING/CMakeFiles/coding.dir/build: libcoding.so
.PHONY : openair1/PHY/CODING/CMakeFiles/coding.dir/build

openair1/PHY/CODING/CMakeFiles/coding.dir/clean:
	cd /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING && $(CMAKE_COMMAND) -P CMakeFiles/coding.dir/cmake_clean.cmake
.PHONY : openair1/PHY/CODING/CMakeFiles/coding.dir/clean

openair1/PHY/CODING/CMakeFiles/coding.dir/depend:
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/oaiseu /home/<USER>/oaiseu/openair1/PHY/CODING /home/<USER>/oaiseu/build_debug /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING/CMakeFiles/coding.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : openair1/PHY/CODING/CMakeFiles/coding.dir/depend

