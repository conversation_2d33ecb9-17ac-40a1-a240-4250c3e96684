
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/oaiseu/openair1/PHY/CODING/3gpplte.c" "openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte.c.o" "gcc" "openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte.c.o.d"
  "/home/<USER>/oaiseu/openair1/PHY/CODING/3gpplte_sse.c" "openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_sse.c.o" "gcc" "openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_sse.c.o.d"
  "/home/<USER>/oaiseu/openair1/PHY/CODING/3gpplte_turbo_decoder.c" "openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder.c.o" "gcc" "openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder.c.o.d"
  "/home/<USER>/oaiseu/openair1/PHY/CODING/3gpplte_turbo_decoder_avx2_16bit.c" "openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_avx2_16bit.c.o" "gcc" "openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_avx2_16bit.c.o.d"
  "/home/<USER>/oaiseu/openair1/PHY/CODING/3gpplte_turbo_decoder_sse_16bit.c" "openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_16bit.c.o" "gcc" "openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_16bit.c.o.d"
  "/home/<USER>/oaiseu/openair1/PHY/CODING/3gpplte_turbo_decoder_sse_8bit.c" "openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_8bit.c.o" "gcc" "openair1/PHY/CODING/CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_8bit.c.o.d"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
