/usr/bin/cc -fPIC  -DSIMDE_X86_AVX_NATIVE -DSIMDE_X86_AVX_NATIVE -DSIMDE_X86_F16C_NATIVE -DSIMDE_X86_FMA_NATIVE -DSIMDE_X86_GFNI_NATIVE -DSIMDE_X86_MMX_NATIVE -DSIMDE_X86_PCLMUL_NATIVE -DSIMDE_X86_SSE2_NATIVE -DSIMDE_X86_SSE3_NATIVE -DSIMDE_X86_SSE_NATIVE -DSIMDE_X86_XOP_HAVE_COM_ -DSIMDE_X86_XOP_NATIVE -mno-avx512f -march=native -DSIMDE_X86_AVX2_NATIVE -DSIMDE_X86_VPCLMULQDQ_NATIVE -march=native -pipe -fPIC -Wall -fno-strict-aliasing -rdynamic -Wno-packed-bitfield-compat -std=gnu11 -funroll-loops  -ggdb2 -DMALLOC_CHECK_=3 -fno-delete-null-pointer-checks -O0 -shared  -o ../../../libcoding.so CMakeFiles/coding.dir/3gpplte_sse.c.o CMakeFiles/coding.dir/3gpplte.c.o CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_8bit.c.o CMakeFiles/coding.dir/3gpplte_turbo_decoder_sse_16bit.c.o CMakeFiles/coding.dir/3gpplte_turbo_decoder_avx2_16bit.c.o CMakeFiles/coding.dir/3gpplte_turbo_decoder.c.o 
