# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/oaiseu

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/oaiseu/build_debug

# Utility rule file for nrLDPC_decoder_kernels_CL.

# Include any custom commands dependencies for this target.
include openair1/PHY/CODING/CMakeFiles/nrLDPC_decoder_kernels_CL.dir/compiler_depend.make

# Include the progress variables for this target.
include openair1/PHY/CODING/CMakeFiles/nrLDPC_decoder_kernels_CL.dir/progress.make

openair1/PHY/CODING/CMakeFiles/nrLDPC_decoder_kernels_CL:
	cd /home/<USER>/oaiseu/openair1/PHY/CODING && gcc nrLDPC_decoder/nrLDPC_decoder_CL.c -dD -DNRLDPC_KERNEL_SOURCE -E -o /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING/nrLDPC_decoder_kernels_CL.clc

nrLDPC_decoder_kernels_CL: openair1/PHY/CODING/CMakeFiles/nrLDPC_decoder_kernels_CL
nrLDPC_decoder_kernels_CL: openair1/PHY/CODING/CMakeFiles/nrLDPC_decoder_kernels_CL.dir/build.make
.PHONY : nrLDPC_decoder_kernels_CL

# Rule to build all files generated by this target.
openair1/PHY/CODING/CMakeFiles/nrLDPC_decoder_kernels_CL.dir/build: nrLDPC_decoder_kernels_CL
.PHONY : openair1/PHY/CODING/CMakeFiles/nrLDPC_decoder_kernels_CL.dir/build

openair1/PHY/CODING/CMakeFiles/nrLDPC_decoder_kernels_CL.dir/clean:
	cd /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING && $(CMAKE_COMMAND) -P CMakeFiles/nrLDPC_decoder_kernels_CL.dir/cmake_clean.cmake
.PHONY : openair1/PHY/CODING/CMakeFiles/nrLDPC_decoder_kernels_CL.dir/clean

openair1/PHY/CODING/CMakeFiles/nrLDPC_decoder_kernels_CL.dir/depend:
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/oaiseu /home/<USER>/oaiseu/openair1/PHY/CODING /home/<USER>/oaiseu/build_debug /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING/CMakeFiles/nrLDPC_decoder_kernels_CL.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : openair1/PHY/CODING/CMakeFiles/nrLDPC_decoder_kernels_CL.dir/depend

