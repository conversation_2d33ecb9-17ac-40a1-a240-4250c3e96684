# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/oaiseu

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/oaiseu/build_debug

# Include any dependencies generated for this target.
include openair1/PHY/CODING/CMakeFiles/ldpc.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include openair1/PHY/CODING/CMakeFiles/ldpc.dir/compiler_depend.make

# Include the progress variables for this target.
include openair1/PHY/CODING/CMakeFiles/ldpc.dir/progress.make

# Include the compile flags for this target's objects.
include openair1/PHY/CODING/CMakeFiles/ldpc.dir/flags.make

openair1/PHY/CODING/CMakeFiles/ldpc.dir/nrLDPC_decoder/nrLDPC_decoder.c.o: openair1/PHY/CODING/CMakeFiles/ldpc.dir/flags.make
openair1/PHY/CODING/CMakeFiles/ldpc.dir/nrLDPC_decoder/nrLDPC_decoder.c.o: ../openair1/PHY/CODING/nrLDPC_decoder/nrLDPC_decoder.c
openair1/PHY/CODING/CMakeFiles/ldpc.dir/nrLDPC_decoder/nrLDPC_decoder.c.o: openair1/PHY/CODING/CMakeFiles/ldpc.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object openair1/PHY/CODING/CMakeFiles/ldpc.dir/nrLDPC_decoder/nrLDPC_decoder.c.o"
	cd /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING && /usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT openair1/PHY/CODING/CMakeFiles/ldpc.dir/nrLDPC_decoder/nrLDPC_decoder.c.o -MF CMakeFiles/ldpc.dir/nrLDPC_decoder/nrLDPC_decoder.c.o.d -o CMakeFiles/ldpc.dir/nrLDPC_decoder/nrLDPC_decoder.c.o -c /home/<USER>/oaiseu/openair1/PHY/CODING/nrLDPC_decoder/nrLDPC_decoder.c

openair1/PHY/CODING/CMakeFiles/ldpc.dir/nrLDPC_decoder/nrLDPC_decoder.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/ldpc.dir/nrLDPC_decoder/nrLDPC_decoder.c.i"
	cd /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/oaiseu/openair1/PHY/CODING/nrLDPC_decoder/nrLDPC_decoder.c > CMakeFiles/ldpc.dir/nrLDPC_decoder/nrLDPC_decoder.c.i

openair1/PHY/CODING/CMakeFiles/ldpc.dir/nrLDPC_decoder/nrLDPC_decoder.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/ldpc.dir/nrLDPC_decoder/nrLDPC_decoder.c.s"
	cd /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/oaiseu/openair1/PHY/CODING/nrLDPC_decoder/nrLDPC_decoder.c -o CMakeFiles/ldpc.dir/nrLDPC_decoder/nrLDPC_decoder.c.s

openair1/PHY/CODING/CMakeFiles/ldpc.dir/nrLDPC_encoder/ldpc_encoder_optim8segmulti.c.o: openair1/PHY/CODING/CMakeFiles/ldpc.dir/flags.make
openair1/PHY/CODING/CMakeFiles/ldpc.dir/nrLDPC_encoder/ldpc_encoder_optim8segmulti.c.o: ../openair1/PHY/CODING/nrLDPC_encoder/ldpc_encoder_optim8segmulti.c
openair1/PHY/CODING/CMakeFiles/ldpc.dir/nrLDPC_encoder/ldpc_encoder_optim8segmulti.c.o: openair1/PHY/CODING/CMakeFiles/ldpc.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object openair1/PHY/CODING/CMakeFiles/ldpc.dir/nrLDPC_encoder/ldpc_encoder_optim8segmulti.c.o"
	cd /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING && /usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT openair1/PHY/CODING/CMakeFiles/ldpc.dir/nrLDPC_encoder/ldpc_encoder_optim8segmulti.c.o -MF CMakeFiles/ldpc.dir/nrLDPC_encoder/ldpc_encoder_optim8segmulti.c.o.d -o CMakeFiles/ldpc.dir/nrLDPC_encoder/ldpc_encoder_optim8segmulti.c.o -c /home/<USER>/oaiseu/openair1/PHY/CODING/nrLDPC_encoder/ldpc_encoder_optim8segmulti.c

openair1/PHY/CODING/CMakeFiles/ldpc.dir/nrLDPC_encoder/ldpc_encoder_optim8segmulti.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/ldpc.dir/nrLDPC_encoder/ldpc_encoder_optim8segmulti.c.i"
	cd /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/oaiseu/openair1/PHY/CODING/nrLDPC_encoder/ldpc_encoder_optim8segmulti.c > CMakeFiles/ldpc.dir/nrLDPC_encoder/ldpc_encoder_optim8segmulti.c.i

openair1/PHY/CODING/CMakeFiles/ldpc.dir/nrLDPC_encoder/ldpc_encoder_optim8segmulti.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/ldpc.dir/nrLDPC_encoder/ldpc_encoder_optim8segmulti.c.s"
	cd /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/oaiseu/openair1/PHY/CODING/nrLDPC_encoder/ldpc_encoder_optim8segmulti.c -o CMakeFiles/ldpc.dir/nrLDPC_encoder/ldpc_encoder_optim8segmulti.c.s

# Object files for target ldpc
ldpc_OBJECTS = \
"CMakeFiles/ldpc.dir/nrLDPC_decoder/nrLDPC_decoder.c.o" \
"CMakeFiles/ldpc.dir/nrLDPC_encoder/ldpc_encoder_optim8segmulti.c.o"

# External object files for target ldpc
ldpc_EXTERNAL_OBJECTS = \
"/home/<USER>/oaiseu/build_debug/openair1/PHY/CODING/nrLDPC_coding/nrLDPC_coding_segment/CMakeFiles/ldpc_segment.dir/nr_rate_matching.c.o" \
"/home/<USER>/oaiseu/build_debug/openair1/PHY/CODING/nrLDPC_coding/nrLDPC_coding_segment/CMakeFiles/ldpc_segment.dir/nrLDPC_coding_segment_decoder.c.o" \
"/home/<USER>/oaiseu/build_debug/openair1/PHY/CODING/nrLDPC_coding/nrLDPC_coding_segment/CMakeFiles/ldpc_segment.dir/nrLDPC_coding_segment_encoder.c.o"

libldpc.so: openair1/PHY/CODING/CMakeFiles/ldpc.dir/nrLDPC_decoder/nrLDPC_decoder.c.o
libldpc.so: openair1/PHY/CODING/CMakeFiles/ldpc.dir/nrLDPC_encoder/ldpc_encoder_optim8segmulti.c.o
libldpc.so: openair1/PHY/CODING/nrLDPC_coding/nrLDPC_coding_segment/CMakeFiles/ldpc_segment.dir/nr_rate_matching.c.o
libldpc.so: openair1/PHY/CODING/nrLDPC_coding/nrLDPC_coding_segment/CMakeFiles/ldpc_segment.dir/nrLDPC_coding_segment_decoder.c.o
libldpc.so: openair1/PHY/CODING/nrLDPC_coding/nrLDPC_coding_segment/CMakeFiles/ldpc_segment.dir/nrLDPC_coding_segment_encoder.c.o
libldpc.so: openair1/PHY/CODING/CMakeFiles/ldpc.dir/build.make
libldpc.so: openair1/PHY/CODING/CMakeFiles/ldpc.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking C shared module ../../../libldpc.so"
	cd /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/ldpc.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
openair1/PHY/CODING/CMakeFiles/ldpc.dir/build: libldpc.so
.PHONY : openair1/PHY/CODING/CMakeFiles/ldpc.dir/build

openair1/PHY/CODING/CMakeFiles/ldpc.dir/clean:
	cd /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING && $(CMAKE_COMMAND) -P CMakeFiles/ldpc.dir/cmake_clean.cmake
.PHONY : openair1/PHY/CODING/CMakeFiles/ldpc.dir/clean

openair1/PHY/CODING/CMakeFiles/ldpc.dir/depend:
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/oaiseu /home/<USER>/oaiseu/openair1/PHY/CODING /home/<USER>/oaiseu/build_debug /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING /home/<USER>/oaiseu/build_debug/openair1/PHY/CODING/CMakeFiles/ldpc.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : openair1/PHY/CODING/CMakeFiles/ldpc.dir/depend

