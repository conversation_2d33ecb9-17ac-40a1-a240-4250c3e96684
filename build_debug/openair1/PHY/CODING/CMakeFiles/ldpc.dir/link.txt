/usr/bin/cc -fPIC  -DSIMDE_X86_AVX_NATIVE -DSIMDE_X86_AVX_NATIVE -DSIMDE_X86_F16C_NATIVE -DSIMDE_X86_FMA_NATIVE -DSIMDE_X86_GFNI_NATIVE -DSIMDE_X86_MMX_NATIVE -DSIMDE_X86_PCLMUL_NATIVE -DSIMDE_X86_SSE2_NATIVE -DSIMDE_X86_SSE3_NATIVE -DSIMDE_X86_SSE_NATIVE -DSIMDE_X86_XOP_HAVE_COM_ -DSIMDE_X86_XOP_NATIVE -mno-avx512f -march=native -DSIMDE_X86_AVX2_NATIVE -DSIMDE_X86_VPCLMULQDQ_NATIVE -march=native -pipe -fPIC -Wall -fno-strict-aliasing -rdynamic -Wno-packed-bitfield-compat -std=gnu11 -funroll-loops  -ggdb2 -DMALLOC_CHECK_=3 -fno-delete-null-pointer-checks -O0 -shared  -o ../../../libldpc.so CMakeFiles/ldpc.dir/nrLDPC_decoder/nrLDPC_decoder.c.o CMakeFiles/ldpc.dir/nrLDPC_encoder/ldpc_encoder_optim8segmulti.c.o nrLDPC_coding/nrLDPC_coding_segment/CMakeFiles/ldpc_segment.dir/nr_rate_matching.c.o nrLDPC_coding/nrLDPC_coding_segment/CMakeFiles/ldpc_segment.dir/nrLDPC_coding_segment_decoder.c.o nrLDPC_coding/nrLDPC_coding_segment/CMakeFiles/ldpc_segment.dir/nrLDPC_coding_segment_encoder.c.o 
