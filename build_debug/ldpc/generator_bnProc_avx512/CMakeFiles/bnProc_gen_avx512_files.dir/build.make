# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/oaiseu

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/oaiseu/build_debug

# Utility rule file for bnProc_gen_avx512_files.

# Include any custom commands dependencies for this target.
include ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512_files.dir/compiler_depend.make

# Include the progress variables for this target.
include ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512_files.dir/progress.make

ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512_files: ldpc/generator_bnProc_avx512/bnProc_avx512/nrLDPC_bnProc_BG1_R13_AVX512.h
ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512_files: ldpc/generator_bnProc_avx512/bnProc_avx512/nrLDPC_bnProc_BG1_R23_AVX512.h
ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512_files: ldpc/generator_bnProc_avx512/bnProc_avx512/nrLDPC_bnProc_BG1_R89_AVX512.h
ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512_files: ldpc/generator_bnProc_avx512/bnProc_avx512/nrLDPC_bnProc_BG2_R13_AVX512.h
ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512_files: ldpc/generator_bnProc_avx512/bnProc_avx512/nrLDPC_bnProc_BG2_R15_AVX512.h
ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512_files: ldpc/generator_bnProc_avx512/bnProc_avx512/nrLDPC_bnProc_BG2_R23_AVX512.h
ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512_files: ldpc/generator_bnProc_avx512/bnProcPc_avx512/nrLDPC_bnProcPc_BG1_R13_AVX512.h
ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512_files: ldpc/generator_bnProc_avx512/bnProcPc_avx512/nrLDPC_bnProcPc_BG1_R23_AVX512.h
ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512_files: ldpc/generator_bnProc_avx512/bnProcPc_avx512/nrLDPC_bnProcPc_BG1_R89_AVX512.h
ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512_files: ldpc/generator_bnProc_avx512/bnProcPc_avx512/nrLDPC_bnProcPc_BG2_R13_AVX512.h
ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512_files: ldpc/generator_bnProc_avx512/bnProcPc_avx512/nrLDPC_bnProcPc_BG2_R15_AVX512.h
ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512_files: ldpc/generator_bnProc_avx512/bnProcPc_avx512/nrLDPC_bnProcPc_BG2_R23_AVX512.h

ldpc/generator_bnProc_avx512/bnProc_avx512/nrLDPC_bnProc_BG1_R13_AVX512.h: ldpc/generator_bnProc_avx512/bnProc_gen_avx512
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Generating LDPC bnProc header files for AVX512"
	cd /home/<USER>/oaiseu/build_debug/ldpc/generator_bnProc_avx512 && /usr/bin/cmake -E make_directory bnProc_avx512
	cd /home/<USER>/oaiseu/build_debug/ldpc/generator_bnProc_avx512 && /usr/bin/cmake -E make_directory bnProcPc_avx512
	cd /home/<USER>/oaiseu/build_debug/ldpc/generator_bnProc_avx512 && ./bnProc_gen_avx512 .

ldpc/generator_bnProc_avx512/bnProc_avx512/nrLDPC_bnProc_BG1_R23_AVX512.h: ldpc/generator_bnProc_avx512/bnProc_avx512/nrLDPC_bnProc_BG1_R13_AVX512.h
	@$(CMAKE_COMMAND) -E touch_nocreate ldpc/generator_bnProc_avx512/bnProc_avx512/nrLDPC_bnProc_BG1_R23_AVX512.h

ldpc/generator_bnProc_avx512/bnProc_avx512/nrLDPC_bnProc_BG1_R89_AVX512.h: ldpc/generator_bnProc_avx512/bnProc_avx512/nrLDPC_bnProc_BG1_R13_AVX512.h
	@$(CMAKE_COMMAND) -E touch_nocreate ldpc/generator_bnProc_avx512/bnProc_avx512/nrLDPC_bnProc_BG1_R89_AVX512.h

ldpc/generator_bnProc_avx512/bnProc_avx512/nrLDPC_bnProc_BG2_R13_AVX512.h: ldpc/generator_bnProc_avx512/bnProc_avx512/nrLDPC_bnProc_BG1_R13_AVX512.h
	@$(CMAKE_COMMAND) -E touch_nocreate ldpc/generator_bnProc_avx512/bnProc_avx512/nrLDPC_bnProc_BG2_R13_AVX512.h

ldpc/generator_bnProc_avx512/bnProc_avx512/nrLDPC_bnProc_BG2_R15_AVX512.h: ldpc/generator_bnProc_avx512/bnProc_avx512/nrLDPC_bnProc_BG1_R13_AVX512.h
	@$(CMAKE_COMMAND) -E touch_nocreate ldpc/generator_bnProc_avx512/bnProc_avx512/nrLDPC_bnProc_BG2_R15_AVX512.h

ldpc/generator_bnProc_avx512/bnProc_avx512/nrLDPC_bnProc_BG2_R23_AVX512.h: ldpc/generator_bnProc_avx512/bnProc_avx512/nrLDPC_bnProc_BG1_R13_AVX512.h
	@$(CMAKE_COMMAND) -E touch_nocreate ldpc/generator_bnProc_avx512/bnProc_avx512/nrLDPC_bnProc_BG2_R23_AVX512.h

ldpc/generator_bnProc_avx512/bnProcPc_avx512/nrLDPC_bnProcPc_BG1_R13_AVX512.h: ldpc/generator_bnProc_avx512/bnProc_avx512/nrLDPC_bnProc_BG1_R13_AVX512.h
	@$(CMAKE_COMMAND) -E touch_nocreate ldpc/generator_bnProc_avx512/bnProcPc_avx512/nrLDPC_bnProcPc_BG1_R13_AVX512.h

ldpc/generator_bnProc_avx512/bnProcPc_avx512/nrLDPC_bnProcPc_BG1_R23_AVX512.h: ldpc/generator_bnProc_avx512/bnProc_avx512/nrLDPC_bnProc_BG1_R13_AVX512.h
	@$(CMAKE_COMMAND) -E touch_nocreate ldpc/generator_bnProc_avx512/bnProcPc_avx512/nrLDPC_bnProcPc_BG1_R23_AVX512.h

ldpc/generator_bnProc_avx512/bnProcPc_avx512/nrLDPC_bnProcPc_BG1_R89_AVX512.h: ldpc/generator_bnProc_avx512/bnProc_avx512/nrLDPC_bnProc_BG1_R13_AVX512.h
	@$(CMAKE_COMMAND) -E touch_nocreate ldpc/generator_bnProc_avx512/bnProcPc_avx512/nrLDPC_bnProcPc_BG1_R89_AVX512.h

ldpc/generator_bnProc_avx512/bnProcPc_avx512/nrLDPC_bnProcPc_BG2_R13_AVX512.h: ldpc/generator_bnProc_avx512/bnProc_avx512/nrLDPC_bnProc_BG1_R13_AVX512.h
	@$(CMAKE_COMMAND) -E touch_nocreate ldpc/generator_bnProc_avx512/bnProcPc_avx512/nrLDPC_bnProcPc_BG2_R13_AVX512.h

ldpc/generator_bnProc_avx512/bnProcPc_avx512/nrLDPC_bnProcPc_BG2_R15_AVX512.h: ldpc/generator_bnProc_avx512/bnProc_avx512/nrLDPC_bnProc_BG1_R13_AVX512.h
	@$(CMAKE_COMMAND) -E touch_nocreate ldpc/generator_bnProc_avx512/bnProcPc_avx512/nrLDPC_bnProcPc_BG2_R15_AVX512.h

ldpc/generator_bnProc_avx512/bnProcPc_avx512/nrLDPC_bnProcPc_BG2_R23_AVX512.h: ldpc/generator_bnProc_avx512/bnProc_avx512/nrLDPC_bnProc_BG1_R13_AVX512.h
	@$(CMAKE_COMMAND) -E touch_nocreate ldpc/generator_bnProc_avx512/bnProcPc_avx512/nrLDPC_bnProcPc_BG2_R23_AVX512.h

bnProc_gen_avx512_files: ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512_files
bnProc_gen_avx512_files: ldpc/generator_bnProc_avx512/bnProcPc_avx512/nrLDPC_bnProcPc_BG1_R13_AVX512.h
bnProc_gen_avx512_files: ldpc/generator_bnProc_avx512/bnProcPc_avx512/nrLDPC_bnProcPc_BG1_R23_AVX512.h
bnProc_gen_avx512_files: ldpc/generator_bnProc_avx512/bnProcPc_avx512/nrLDPC_bnProcPc_BG1_R89_AVX512.h
bnProc_gen_avx512_files: ldpc/generator_bnProc_avx512/bnProcPc_avx512/nrLDPC_bnProcPc_BG2_R13_AVX512.h
bnProc_gen_avx512_files: ldpc/generator_bnProc_avx512/bnProcPc_avx512/nrLDPC_bnProcPc_BG2_R15_AVX512.h
bnProc_gen_avx512_files: ldpc/generator_bnProc_avx512/bnProcPc_avx512/nrLDPC_bnProcPc_BG2_R23_AVX512.h
bnProc_gen_avx512_files: ldpc/generator_bnProc_avx512/bnProc_avx512/nrLDPC_bnProc_BG1_R13_AVX512.h
bnProc_gen_avx512_files: ldpc/generator_bnProc_avx512/bnProc_avx512/nrLDPC_bnProc_BG1_R23_AVX512.h
bnProc_gen_avx512_files: ldpc/generator_bnProc_avx512/bnProc_avx512/nrLDPC_bnProc_BG1_R89_AVX512.h
bnProc_gen_avx512_files: ldpc/generator_bnProc_avx512/bnProc_avx512/nrLDPC_bnProc_BG2_R13_AVX512.h
bnProc_gen_avx512_files: ldpc/generator_bnProc_avx512/bnProc_avx512/nrLDPC_bnProc_BG2_R15_AVX512.h
bnProc_gen_avx512_files: ldpc/generator_bnProc_avx512/bnProc_avx512/nrLDPC_bnProc_BG2_R23_AVX512.h
bnProc_gen_avx512_files: ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512_files.dir/build.make
.PHONY : bnProc_gen_avx512_files

# Rule to build all files generated by this target.
ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512_files.dir/build: bnProc_gen_avx512_files
.PHONY : ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512_files.dir/build

ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512_files.dir/clean:
	cd /home/<USER>/oaiseu/build_debug/ldpc/generator_bnProc_avx512 && $(CMAKE_COMMAND) -P CMakeFiles/bnProc_gen_avx512_files.dir/cmake_clean.cmake
.PHONY : ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512_files.dir/clean

ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512_files.dir/depend:
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/oaiseu /home/<USER>/oaiseu/openair1/PHY/CODING/nrLDPC_decoder/nrLDPC_tools/generator_bnProc_avx512 /home/<USER>/oaiseu/build_debug /home/<USER>/oaiseu/build_debug/ldpc/generator_bnProc_avx512 /home/<USER>/oaiseu/build_debug/ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512_files.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512_files.dir/depend

