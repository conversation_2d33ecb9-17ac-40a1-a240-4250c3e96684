# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/oaiseu

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/oaiseu/build_debug

# Include any dependencies generated for this target.
include ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/compiler_depend.make

# Include the progress variables for this target.
include ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/progress.make

# Include the compile flags for this target's objects.
include ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/flags.make

ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProc_gen_BG1_avx512.c.o: ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/flags.make
ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProc_gen_BG1_avx512.c.o: ../openair1/PHY/CODING/nrLDPC_decoder/nrLDPC_tools/generator_bnProc_avx512/bnProc_gen_BG1_avx512.c
ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProc_gen_BG1_avx512.c.o: ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProc_gen_BG1_avx512.c.o"
	cd /home/<USER>/oaiseu/build_debug/ldpc/generator_bnProc_avx512 && /usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProc_gen_BG1_avx512.c.o -MF CMakeFiles/bnProc_gen_avx512.dir/bnProc_gen_BG1_avx512.c.o.d -o CMakeFiles/bnProc_gen_avx512.dir/bnProc_gen_BG1_avx512.c.o -c /home/<USER>/oaiseu/openair1/PHY/CODING/nrLDPC_decoder/nrLDPC_tools/generator_bnProc_avx512/bnProc_gen_BG1_avx512.c

ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProc_gen_BG1_avx512.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/bnProc_gen_avx512.dir/bnProc_gen_BG1_avx512.c.i"
	cd /home/<USER>/oaiseu/build_debug/ldpc/generator_bnProc_avx512 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/oaiseu/openair1/PHY/CODING/nrLDPC_decoder/nrLDPC_tools/generator_bnProc_avx512/bnProc_gen_BG1_avx512.c > CMakeFiles/bnProc_gen_avx512.dir/bnProc_gen_BG1_avx512.c.i

ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProc_gen_BG1_avx512.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/bnProc_gen_avx512.dir/bnProc_gen_BG1_avx512.c.s"
	cd /home/<USER>/oaiseu/build_debug/ldpc/generator_bnProc_avx512 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/oaiseu/openair1/PHY/CODING/nrLDPC_decoder/nrLDPC_tools/generator_bnProc_avx512/bnProc_gen_BG1_avx512.c -o CMakeFiles/bnProc_gen_avx512.dir/bnProc_gen_BG1_avx512.c.s

ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProc_gen_BG2_avx512.c.o: ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/flags.make
ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProc_gen_BG2_avx512.c.o: ../openair1/PHY/CODING/nrLDPC_decoder/nrLDPC_tools/generator_bnProc_avx512/bnProc_gen_BG2_avx512.c
ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProc_gen_BG2_avx512.c.o: ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProc_gen_BG2_avx512.c.o"
	cd /home/<USER>/oaiseu/build_debug/ldpc/generator_bnProc_avx512 && /usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProc_gen_BG2_avx512.c.o -MF CMakeFiles/bnProc_gen_avx512.dir/bnProc_gen_BG2_avx512.c.o.d -o CMakeFiles/bnProc_gen_avx512.dir/bnProc_gen_BG2_avx512.c.o -c /home/<USER>/oaiseu/openair1/PHY/CODING/nrLDPC_decoder/nrLDPC_tools/generator_bnProc_avx512/bnProc_gen_BG2_avx512.c

ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProc_gen_BG2_avx512.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/bnProc_gen_avx512.dir/bnProc_gen_BG2_avx512.c.i"
	cd /home/<USER>/oaiseu/build_debug/ldpc/generator_bnProc_avx512 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/oaiseu/openair1/PHY/CODING/nrLDPC_decoder/nrLDPC_tools/generator_bnProc_avx512/bnProc_gen_BG2_avx512.c > CMakeFiles/bnProc_gen_avx512.dir/bnProc_gen_BG2_avx512.c.i

ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProc_gen_BG2_avx512.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/bnProc_gen_avx512.dir/bnProc_gen_BG2_avx512.c.s"
	cd /home/<USER>/oaiseu/build_debug/ldpc/generator_bnProc_avx512 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/oaiseu/openair1/PHY/CODING/nrLDPC_decoder/nrLDPC_tools/generator_bnProc_avx512/bnProc_gen_BG2_avx512.c -o CMakeFiles/bnProc_gen_avx512.dir/bnProc_gen_BG2_avx512.c.s

ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProcPc_gen_BG1_avx512.c.o: ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/flags.make
ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProcPc_gen_BG1_avx512.c.o: ../openair1/PHY/CODING/nrLDPC_decoder/nrLDPC_tools/generator_bnProc_avx512/bnProcPc_gen_BG1_avx512.c
ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProcPc_gen_BG1_avx512.c.o: ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProcPc_gen_BG1_avx512.c.o"
	cd /home/<USER>/oaiseu/build_debug/ldpc/generator_bnProc_avx512 && /usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProcPc_gen_BG1_avx512.c.o -MF CMakeFiles/bnProc_gen_avx512.dir/bnProcPc_gen_BG1_avx512.c.o.d -o CMakeFiles/bnProc_gen_avx512.dir/bnProcPc_gen_BG1_avx512.c.o -c /home/<USER>/oaiseu/openair1/PHY/CODING/nrLDPC_decoder/nrLDPC_tools/generator_bnProc_avx512/bnProcPc_gen_BG1_avx512.c

ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProcPc_gen_BG1_avx512.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/bnProc_gen_avx512.dir/bnProcPc_gen_BG1_avx512.c.i"
	cd /home/<USER>/oaiseu/build_debug/ldpc/generator_bnProc_avx512 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/oaiseu/openair1/PHY/CODING/nrLDPC_decoder/nrLDPC_tools/generator_bnProc_avx512/bnProcPc_gen_BG1_avx512.c > CMakeFiles/bnProc_gen_avx512.dir/bnProcPc_gen_BG1_avx512.c.i

ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProcPc_gen_BG1_avx512.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/bnProc_gen_avx512.dir/bnProcPc_gen_BG1_avx512.c.s"
	cd /home/<USER>/oaiseu/build_debug/ldpc/generator_bnProc_avx512 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/oaiseu/openair1/PHY/CODING/nrLDPC_decoder/nrLDPC_tools/generator_bnProc_avx512/bnProcPc_gen_BG1_avx512.c -o CMakeFiles/bnProc_gen_avx512.dir/bnProcPc_gen_BG1_avx512.c.s

ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProcPc_gen_BG2_avx512.c.o: ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/flags.make
ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProcPc_gen_BG2_avx512.c.o: ../openair1/PHY/CODING/nrLDPC_decoder/nrLDPC_tools/generator_bnProc_avx512/bnProcPc_gen_BG2_avx512.c
ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProcPc_gen_BG2_avx512.c.o: ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProcPc_gen_BG2_avx512.c.o"
	cd /home/<USER>/oaiseu/build_debug/ldpc/generator_bnProc_avx512 && /usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProcPc_gen_BG2_avx512.c.o -MF CMakeFiles/bnProc_gen_avx512.dir/bnProcPc_gen_BG2_avx512.c.o.d -o CMakeFiles/bnProc_gen_avx512.dir/bnProcPc_gen_BG2_avx512.c.o -c /home/<USER>/oaiseu/openair1/PHY/CODING/nrLDPC_decoder/nrLDPC_tools/generator_bnProc_avx512/bnProcPc_gen_BG2_avx512.c

ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProcPc_gen_BG2_avx512.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/bnProc_gen_avx512.dir/bnProcPc_gen_BG2_avx512.c.i"
	cd /home/<USER>/oaiseu/build_debug/ldpc/generator_bnProc_avx512 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/oaiseu/openair1/PHY/CODING/nrLDPC_decoder/nrLDPC_tools/generator_bnProc_avx512/bnProcPc_gen_BG2_avx512.c > CMakeFiles/bnProc_gen_avx512.dir/bnProcPc_gen_BG2_avx512.c.i

ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProcPc_gen_BG2_avx512.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/bnProc_gen_avx512.dir/bnProcPc_gen_BG2_avx512.c.s"
	cd /home/<USER>/oaiseu/build_debug/ldpc/generator_bnProc_avx512 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/oaiseu/openair1/PHY/CODING/nrLDPC_decoder/nrLDPC_tools/generator_bnProc_avx512/bnProcPc_gen_BG2_avx512.c -o CMakeFiles/bnProc_gen_avx512.dir/bnProcPc_gen_BG2_avx512.c.s

ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/main.c.o: ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/flags.make
ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/main.c.o: ../openair1/PHY/CODING/nrLDPC_decoder/nrLDPC_tools/generator_bnProc_avx512/main.c
ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/main.c.o: ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/main.c.o"
	cd /home/<USER>/oaiseu/build_debug/ldpc/generator_bnProc_avx512 && /usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/main.c.o -MF CMakeFiles/bnProc_gen_avx512.dir/main.c.o.d -o CMakeFiles/bnProc_gen_avx512.dir/main.c.o -c /home/<USER>/oaiseu/openair1/PHY/CODING/nrLDPC_decoder/nrLDPC_tools/generator_bnProc_avx512/main.c

ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/main.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/bnProc_gen_avx512.dir/main.c.i"
	cd /home/<USER>/oaiseu/build_debug/ldpc/generator_bnProc_avx512 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/oaiseu/openair1/PHY/CODING/nrLDPC_decoder/nrLDPC_tools/generator_bnProc_avx512/main.c > CMakeFiles/bnProc_gen_avx512.dir/main.c.i

ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/main.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/bnProc_gen_avx512.dir/main.c.s"
	cd /home/<USER>/oaiseu/build_debug/ldpc/generator_bnProc_avx512 && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/oaiseu/openair1/PHY/CODING/nrLDPC_decoder/nrLDPC_tools/generator_bnProc_avx512/main.c -o CMakeFiles/bnProc_gen_avx512.dir/main.c.s

# Object files for target bnProc_gen_avx512
bnProc_gen_avx512_OBJECTS = \
"CMakeFiles/bnProc_gen_avx512.dir/bnProc_gen_BG1_avx512.c.o" \
"CMakeFiles/bnProc_gen_avx512.dir/bnProc_gen_BG2_avx512.c.o" \
"CMakeFiles/bnProc_gen_avx512.dir/bnProcPc_gen_BG1_avx512.c.o" \
"CMakeFiles/bnProc_gen_avx512.dir/bnProcPc_gen_BG2_avx512.c.o" \
"CMakeFiles/bnProc_gen_avx512.dir/main.c.o"

# External object files for target bnProc_gen_avx512
bnProc_gen_avx512_EXTERNAL_OBJECTS =

ldpc/generator_bnProc_avx512/bnProc_gen_avx512: ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProc_gen_BG1_avx512.c.o
ldpc/generator_bnProc_avx512/bnProc_gen_avx512: ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProc_gen_BG2_avx512.c.o
ldpc/generator_bnProc_avx512/bnProc_gen_avx512: ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProcPc_gen_BG1_avx512.c.o
ldpc/generator_bnProc_avx512/bnProc_gen_avx512: ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProcPc_gen_BG2_avx512.c.o
ldpc/generator_bnProc_avx512/bnProc_gen_avx512: ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/main.c.o
ldpc/generator_bnProc_avx512/bnProc_gen_avx512: ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/build.make
ldpc/generator_bnProc_avx512/bnProc_gen_avx512: ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Linking C executable bnProc_gen_avx512"
	cd /home/<USER>/oaiseu/build_debug/ldpc/generator_bnProc_avx512 && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/bnProc_gen_avx512.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/build: ldpc/generator_bnProc_avx512/bnProc_gen_avx512
.PHONY : ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/build

ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/clean:
	cd /home/<USER>/oaiseu/build_debug/ldpc/generator_bnProc_avx512 && $(CMAKE_COMMAND) -P CMakeFiles/bnProc_gen_avx512.dir/cmake_clean.cmake
.PHONY : ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/clean

ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/depend:
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/oaiseu /home/<USER>/oaiseu/openair1/PHY/CODING/nrLDPC_decoder/nrLDPC_tools/generator_bnProc_avx512 /home/<USER>/oaiseu/build_debug /home/<USER>/oaiseu/build_debug/ldpc/generator_bnProc_avx512 /home/<USER>/oaiseu/build_debug/ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/depend

