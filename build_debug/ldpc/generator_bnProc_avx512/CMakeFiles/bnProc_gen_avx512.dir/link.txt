/usr/bin/cc  -DSIMDE_X86_AVX_NATIVE -DSIMDE_X86_AVX_NATIVE -DSIMDE_X86_F16C_NATIVE -DSIMDE_X86_FMA_NATIVE -DSIMDE_X86_GFNI_NATIVE -DSIMDE_X86_MMX_NATIVE -DSIMDE_X86_PCLMUL_NATIVE -DSIMDE_X86_SSE2_NATIVE -DSIMDE_X86_SSE3_NATIVE -DSIMDE_X86_SSE_NATIVE -DSIMDE_X86_XOP_HAVE_COM_ -DSIMDE_X86_XOP_NATIVE -mno-avx512f -march=native -DSIMDE_X86_AVX2_NATIVE -DSIMDE_X86_VPCLMULQDQ_NATIVE -march=native -pipe -fPIC -Wall -fno-strict-aliasing -rdynamic -Wno-packed-bitfield-compat -std=gnu11 -funroll-loops  -ggdb2 -DMALLOC_CHECK_=3 -fno-delete-null-pointer-checks -O0  -ggdb2 -Wl,-rpath -Wl,/home/<USER>/oaiseu/build_debug CMakeFiles/bnProc_gen_avx512.dir/bnProc_gen_BG1_avx512.c.o CMakeFiles/bnProc_gen_avx512.dir/bnProc_gen_BG2_avx512.c.o CMakeFiles/bnProc_gen_avx512.dir/bnProcPc_gen_BG1_avx512.c.o CMakeFiles/bnProc_gen_avx512.dir/bnProcPc_gen_BG2_avx512.c.o CMakeFiles/bnProc_gen_avx512.dir/main.c.o -o bnProc_gen_avx512 
