
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/oaiseu/openair1/PHY/CODING/nrLDPC_decoder/nrLDPC_tools/generator_bnProc_avx512/bnProcPc_gen_BG1_avx512.c" "ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProcPc_gen_BG1_avx512.c.o" "gcc" "ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProcPc_gen_BG1_avx512.c.o.d"
  "/home/<USER>/oaiseu/openair1/PHY/CODING/nrLDPC_decoder/nrLDPC_tools/generator_bnProc_avx512/bnProcPc_gen_BG2_avx512.c" "ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProcPc_gen_BG2_avx512.c.o" "gcc" "ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProcPc_gen_BG2_avx512.c.o.d"
  "/home/<USER>/oaiseu/openair1/PHY/CODING/nrLDPC_decoder/nrLDPC_tools/generator_bnProc_avx512/bnProc_gen_BG1_avx512.c" "ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProc_gen_BG1_avx512.c.o" "gcc" "ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProc_gen_BG1_avx512.c.o.d"
  "/home/<USER>/oaiseu/openair1/PHY/CODING/nrLDPC_decoder/nrLDPC_tools/generator_bnProc_avx512/bnProc_gen_BG2_avx512.c" "ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProc_gen_BG2_avx512.c.o" "gcc" "ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/bnProc_gen_BG2_avx512.c.o.d"
  "/home/<USER>/oaiseu/openair1/PHY/CODING/nrLDPC_decoder/nrLDPC_tools/generator_bnProc_avx512/main.c" "ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/main.c.o" "gcc" "ldpc/generator_bnProc_avx512/CMakeFiles/bnProc_gen_avx512.dir/main.c.o.d"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
