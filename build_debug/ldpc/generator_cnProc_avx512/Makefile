# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/oaiseu

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/oaiseu/build_debug

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/oaiseu/build_debug/CMakeFiles /home/<USER>/oaiseu/build_debug/ldpc/generator_cnProc_avx512//CMakeFiles/progress.marks
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 ldpc/generator_cnProc_avx512/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/oaiseu/build_debug/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 ldpc/generator_cnProc_avx512/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 ldpc/generator_cnProc_avx512/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 ldpc/generator_cnProc_avx512/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
ldpc/generator_cnProc_avx512/CMakeFiles/cnProc_gen_avx512.dir/rule:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 ldpc/generator_cnProc_avx512/CMakeFiles/cnProc_gen_avx512.dir/rule
.PHONY : ldpc/generator_cnProc_avx512/CMakeFiles/cnProc_gen_avx512.dir/rule

# Convenience name for target.
cnProc_gen_avx512: ldpc/generator_cnProc_avx512/CMakeFiles/cnProc_gen_avx512.dir/rule
.PHONY : cnProc_gen_avx512

# fast build rule for target.
cnProc_gen_avx512/fast:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f ldpc/generator_cnProc_avx512/CMakeFiles/cnProc_gen_avx512.dir/build.make ldpc/generator_cnProc_avx512/CMakeFiles/cnProc_gen_avx512.dir/build
.PHONY : cnProc_gen_avx512/fast

# Convenience name for target.
ldpc/generator_cnProc_avx512/CMakeFiles/cnProc_gen_avx512_files.dir/rule:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 ldpc/generator_cnProc_avx512/CMakeFiles/cnProc_gen_avx512_files.dir/rule
.PHONY : ldpc/generator_cnProc_avx512/CMakeFiles/cnProc_gen_avx512_files.dir/rule

# Convenience name for target.
cnProc_gen_avx512_files: ldpc/generator_cnProc_avx512/CMakeFiles/cnProc_gen_avx512_files.dir/rule
.PHONY : cnProc_gen_avx512_files

# fast build rule for target.
cnProc_gen_avx512_files/fast:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f ldpc/generator_cnProc_avx512/CMakeFiles/cnProc_gen_avx512_files.dir/build.make ldpc/generator_cnProc_avx512/CMakeFiles/cnProc_gen_avx512_files.dir/build
.PHONY : cnProc_gen_avx512_files/fast

cnProc_gen_BG1_avx512.o: cnProc_gen_BG1_avx512.c.o
.PHONY : cnProc_gen_BG1_avx512.o

# target to build an object file
cnProc_gen_BG1_avx512.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f ldpc/generator_cnProc_avx512/CMakeFiles/cnProc_gen_avx512.dir/build.make ldpc/generator_cnProc_avx512/CMakeFiles/cnProc_gen_avx512.dir/cnProc_gen_BG1_avx512.c.o
.PHONY : cnProc_gen_BG1_avx512.c.o

cnProc_gen_BG1_avx512.i: cnProc_gen_BG1_avx512.c.i
.PHONY : cnProc_gen_BG1_avx512.i

# target to preprocess a source file
cnProc_gen_BG1_avx512.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f ldpc/generator_cnProc_avx512/CMakeFiles/cnProc_gen_avx512.dir/build.make ldpc/generator_cnProc_avx512/CMakeFiles/cnProc_gen_avx512.dir/cnProc_gen_BG1_avx512.c.i
.PHONY : cnProc_gen_BG1_avx512.c.i

cnProc_gen_BG1_avx512.s: cnProc_gen_BG1_avx512.c.s
.PHONY : cnProc_gen_BG1_avx512.s

# target to generate assembly for a file
cnProc_gen_BG1_avx512.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f ldpc/generator_cnProc_avx512/CMakeFiles/cnProc_gen_avx512.dir/build.make ldpc/generator_cnProc_avx512/CMakeFiles/cnProc_gen_avx512.dir/cnProc_gen_BG1_avx512.c.s
.PHONY : cnProc_gen_BG1_avx512.c.s

cnProc_gen_BG2_avx512.o: cnProc_gen_BG2_avx512.c.o
.PHONY : cnProc_gen_BG2_avx512.o

# target to build an object file
cnProc_gen_BG2_avx512.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f ldpc/generator_cnProc_avx512/CMakeFiles/cnProc_gen_avx512.dir/build.make ldpc/generator_cnProc_avx512/CMakeFiles/cnProc_gen_avx512.dir/cnProc_gen_BG2_avx512.c.o
.PHONY : cnProc_gen_BG2_avx512.c.o

cnProc_gen_BG2_avx512.i: cnProc_gen_BG2_avx512.c.i
.PHONY : cnProc_gen_BG2_avx512.i

# target to preprocess a source file
cnProc_gen_BG2_avx512.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f ldpc/generator_cnProc_avx512/CMakeFiles/cnProc_gen_avx512.dir/build.make ldpc/generator_cnProc_avx512/CMakeFiles/cnProc_gen_avx512.dir/cnProc_gen_BG2_avx512.c.i
.PHONY : cnProc_gen_BG2_avx512.c.i

cnProc_gen_BG2_avx512.s: cnProc_gen_BG2_avx512.c.s
.PHONY : cnProc_gen_BG2_avx512.s

# target to generate assembly for a file
cnProc_gen_BG2_avx512.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f ldpc/generator_cnProc_avx512/CMakeFiles/cnProc_gen_avx512.dir/build.make ldpc/generator_cnProc_avx512/CMakeFiles/cnProc_gen_avx512.dir/cnProc_gen_BG2_avx512.c.s
.PHONY : cnProc_gen_BG2_avx512.c.s

main.o: main.c.o
.PHONY : main.o

# target to build an object file
main.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f ldpc/generator_cnProc_avx512/CMakeFiles/cnProc_gen_avx512.dir/build.make ldpc/generator_cnProc_avx512/CMakeFiles/cnProc_gen_avx512.dir/main.c.o
.PHONY : main.c.o

main.i: main.c.i
.PHONY : main.i

# target to preprocess a source file
main.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f ldpc/generator_cnProc_avx512/CMakeFiles/cnProc_gen_avx512.dir/build.make ldpc/generator_cnProc_avx512/CMakeFiles/cnProc_gen_avx512.dir/main.c.i
.PHONY : main.c.i

main.s: main.c.s
.PHONY : main.s

# target to generate assembly for a file
main.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f ldpc/generator_cnProc_avx512/CMakeFiles/cnProc_gen_avx512.dir/build.make ldpc/generator_cnProc_avx512/CMakeFiles/cnProc_gen_avx512.dir/main.c.s
.PHONY : main.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... cnProc_gen_avx512_files"
	@echo "... cnProc_gen_avx512"
	@echo "... cnProc_gen_BG1_avx512.o"
	@echo "... cnProc_gen_BG1_avx512.i"
	@echo "... cnProc_gen_BG1_avx512.s"
	@echo "... cnProc_gen_BG2_avx512.o"
	@echo "... cnProc_gen_BG2_avx512.i"
	@echo "... cnProc_gen_BG2_avx512.s"
	@echo "... main.o"
	@echo "... main.i"
	@echo "... main.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

