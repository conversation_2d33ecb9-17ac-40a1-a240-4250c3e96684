# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/oaiseu

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/oaiseu/build_debug

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/oaiseu/build_debug/CMakeFiles /home/<USER>/oaiseu/build_debug/radio/rfsimulator//CMakeFiles/progress.marks
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 radio/rfsimulator/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/oaiseu/build_debug/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 radio/rfsimulator/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 radio/rfsimulator/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 radio/rfsimulator/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
radio/rfsimulator/CMakeFiles/rfsimulator.dir/rule:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 radio/rfsimulator/CMakeFiles/rfsimulator.dir/rule
.PHONY : radio/rfsimulator/CMakeFiles/rfsimulator.dir/rule

# Convenience name for target.
rfsimulator: radio/rfsimulator/CMakeFiles/rfsimulator.dir/rule
.PHONY : rfsimulator

# fast build rule for target.
rfsimulator/fast:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f radio/rfsimulator/CMakeFiles/rfsimulator.dir/build.make radio/rfsimulator/CMakeFiles/rfsimulator.dir/build
.PHONY : rfsimulator/fast

# Convenience name for target.
radio/rfsimulator/CMakeFiles/replay_node.dir/rule:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 radio/rfsimulator/CMakeFiles/replay_node.dir/rule
.PHONY : radio/rfsimulator/CMakeFiles/replay_node.dir/rule

# Convenience name for target.
replay_node: radio/rfsimulator/CMakeFiles/replay_node.dir/rule
.PHONY : replay_node

# fast build rule for target.
replay_node/fast:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f radio/rfsimulator/CMakeFiles/replay_node.dir/build.make radio/rfsimulator/CMakeFiles/replay_node.dir/build
.PHONY : replay_node/fast

__/__/openair1/PHY/TOOLS/signal_energy.o: __/__/openair1/PHY/TOOLS/signal_energy.c.o
.PHONY : __/__/openair1/PHY/TOOLS/signal_energy.o

# target to build an object file
__/__/openair1/PHY/TOOLS/signal_energy.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f radio/rfsimulator/CMakeFiles/rfsimulator.dir/build.make radio/rfsimulator/CMakeFiles/rfsimulator.dir/__/__/openair1/PHY/TOOLS/signal_energy.c.o
.PHONY : __/__/openair1/PHY/TOOLS/signal_energy.c.o

__/__/openair1/PHY/TOOLS/signal_energy.i: __/__/openair1/PHY/TOOLS/signal_energy.c.i
.PHONY : __/__/openair1/PHY/TOOLS/signal_energy.i

# target to preprocess a source file
__/__/openair1/PHY/TOOLS/signal_energy.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f radio/rfsimulator/CMakeFiles/rfsimulator.dir/build.make radio/rfsimulator/CMakeFiles/rfsimulator.dir/__/__/openair1/PHY/TOOLS/signal_energy.c.i
.PHONY : __/__/openair1/PHY/TOOLS/signal_energy.c.i

__/__/openair1/PHY/TOOLS/signal_energy.s: __/__/openair1/PHY/TOOLS/signal_energy.c.s
.PHONY : __/__/openair1/PHY/TOOLS/signal_energy.s

# target to generate assembly for a file
__/__/openair1/PHY/TOOLS/signal_energy.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f radio/rfsimulator/CMakeFiles/rfsimulator.dir/build.make radio/rfsimulator/CMakeFiles/rfsimulator.dir/__/__/openair1/PHY/TOOLS/signal_energy.c.s
.PHONY : __/__/openair1/PHY/TOOLS/signal_energy.c.s

apply_channelmod.o: apply_channelmod.c.o
.PHONY : apply_channelmod.o

# target to build an object file
apply_channelmod.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f radio/rfsimulator/CMakeFiles/rfsimulator.dir/build.make radio/rfsimulator/CMakeFiles/rfsimulator.dir/apply_channelmod.c.o
.PHONY : apply_channelmod.c.o

apply_channelmod.i: apply_channelmod.c.i
.PHONY : apply_channelmod.i

# target to preprocess a source file
apply_channelmod.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f radio/rfsimulator/CMakeFiles/rfsimulator.dir/build.make radio/rfsimulator/CMakeFiles/rfsimulator.dir/apply_channelmod.c.i
.PHONY : apply_channelmod.c.i

apply_channelmod.s: apply_channelmod.c.s
.PHONY : apply_channelmod.s

# target to generate assembly for a file
apply_channelmod.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f radio/rfsimulator/CMakeFiles/rfsimulator.dir/build.make radio/rfsimulator/CMakeFiles/rfsimulator.dir/apply_channelmod.c.s
.PHONY : apply_channelmod.c.s

simulator.o: simulator.c.o
.PHONY : simulator.o

# target to build an object file
simulator.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f radio/rfsimulator/CMakeFiles/rfsimulator.dir/build.make radio/rfsimulator/CMakeFiles/rfsimulator.dir/simulator.c.o
.PHONY : simulator.c.o

simulator.i: simulator.c.i
.PHONY : simulator.i

# target to preprocess a source file
simulator.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f radio/rfsimulator/CMakeFiles/rfsimulator.dir/build.make radio/rfsimulator/CMakeFiles/rfsimulator.dir/simulator.c.i
.PHONY : simulator.c.i

simulator.s: simulator.c.s
.PHONY : simulator.s

# target to generate assembly for a file
simulator.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f radio/rfsimulator/CMakeFiles/rfsimulator.dir/build.make radio/rfsimulator/CMakeFiles/rfsimulator.dir/simulator.c.s
.PHONY : simulator.c.s

stored_node.o: stored_node.c.o
.PHONY : stored_node.o

# target to build an object file
stored_node.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f radio/rfsimulator/CMakeFiles/replay_node.dir/build.make radio/rfsimulator/CMakeFiles/replay_node.dir/stored_node.c.o
.PHONY : stored_node.c.o

stored_node.i: stored_node.c.i
.PHONY : stored_node.i

# target to preprocess a source file
stored_node.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f radio/rfsimulator/CMakeFiles/replay_node.dir/build.make radio/rfsimulator/CMakeFiles/replay_node.dir/stored_node.c.i
.PHONY : stored_node.c.i

stored_node.s: stored_node.c.s
.PHONY : stored_node.s

# target to generate assembly for a file
stored_node.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f radio/rfsimulator/CMakeFiles/replay_node.dir/build.make radio/rfsimulator/CMakeFiles/replay_node.dir/stored_node.c.s
.PHONY : stored_node.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... replay_node"
	@echo "... rfsimulator"
	@echo "... __/__/openair1/PHY/TOOLS/signal_energy.o"
	@echo "... __/__/openair1/PHY/TOOLS/signal_energy.i"
	@echo "... __/__/openair1/PHY/TOOLS/signal_energy.s"
	@echo "... apply_channelmod.o"
	@echo "... apply_channelmod.i"
	@echo "... apply_channelmod.s"
	@echo "... simulator.o"
	@echo "... simulator.i"
	@echo "... simulator.s"
	@echo "... stored_node.o"
	@echo "... stored_node.i"
	@echo "... stored_node.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

