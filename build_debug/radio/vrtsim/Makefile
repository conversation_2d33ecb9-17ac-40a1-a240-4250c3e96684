# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/oaiseu

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/oaiseu/build_debug

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/oaiseu/build_debug/CMakeFiles /home/<USER>/oaiseu/build_debug/radio/vrtsim//CMakeFiles/progress.marks
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 radio/vrtsim/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/oaiseu/build_debug/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 radio/vrtsim/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 radio/vrtsim/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 radio/vrtsim/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
radio/vrtsim/CMakeFiles/vrtsim.dir/rule:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 radio/vrtsim/CMakeFiles/vrtsim.dir/rule
.PHONY : radio/vrtsim/CMakeFiles/vrtsim.dir/rule

# Convenience name for target.
vrtsim: radio/vrtsim/CMakeFiles/vrtsim.dir/rule
.PHONY : vrtsim

# fast build rule for target.
vrtsim/fast:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f radio/vrtsim/CMakeFiles/vrtsim.dir/build.make radio/vrtsim/CMakeFiles/vrtsim.dir/build
.PHONY : vrtsim/fast

noise_device.o: noise_device.c.o
.PHONY : noise_device.o

# target to build an object file
noise_device.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f radio/vrtsim/CMakeFiles/vrtsim.dir/build.make radio/vrtsim/CMakeFiles/vrtsim.dir/noise_device.c.o
.PHONY : noise_device.c.o

noise_device.i: noise_device.c.i
.PHONY : noise_device.i

# target to preprocess a source file
noise_device.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f radio/vrtsim/CMakeFiles/vrtsim.dir/build.make radio/vrtsim/CMakeFiles/vrtsim.dir/noise_device.c.i
.PHONY : noise_device.c.i

noise_device.s: noise_device.c.s
.PHONY : noise_device.s

# target to generate assembly for a file
noise_device.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f radio/vrtsim/CMakeFiles/vrtsim.dir/build.make radio/vrtsim/CMakeFiles/vrtsim.dir/noise_device.c.s
.PHONY : noise_device.c.s

vrtsim.o: vrtsim.c.o
.PHONY : vrtsim.o

# target to build an object file
vrtsim.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f radio/vrtsim/CMakeFiles/vrtsim.dir/build.make radio/vrtsim/CMakeFiles/vrtsim.dir/vrtsim.c.o
.PHONY : vrtsim.c.o

vrtsim.i: vrtsim.c.i
.PHONY : vrtsim.i

# target to preprocess a source file
vrtsim.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f radio/vrtsim/CMakeFiles/vrtsim.dir/build.make radio/vrtsim/CMakeFiles/vrtsim.dir/vrtsim.c.i
.PHONY : vrtsim.c.i

vrtsim.s: vrtsim.c.s
.PHONY : vrtsim.s

# target to generate assembly for a file
vrtsim.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f radio/vrtsim/CMakeFiles/vrtsim.dir/build.make radio/vrtsim/CMakeFiles/vrtsim.dir/vrtsim.c.s
.PHONY : vrtsim.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... vrtsim"
	@echo "... noise_device.o"
	@echo "... noise_device.i"
	@echo "... noise_device.s"
	@echo "... vrtsim.o"
	@echo "... vrtsim.i"
	@echo "... vrtsim.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

