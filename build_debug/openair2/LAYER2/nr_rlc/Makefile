# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/oaiseu

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/oaiseu/build_debug

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/oaiseu/build_debug/CMakeFiles /home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc//CMakeFiles/progress.marks
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 openair2/LAYER2/nr_rlc/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/oaiseu/build_debug/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 openair2/LAYER2/nr_rlc/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 openair2/LAYER2/nr_rlc/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 openair2/LAYER2/nr_rlc/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/rule:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/rule
.PHONY : openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/rule

# Convenience name for target.
nr_rlc_core: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/rule
.PHONY : nr_rlc_core

# fast build rule for target.
nr_rlc_core/fast:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/build.make openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/build
.PHONY : nr_rlc_core/fast

# Convenience name for target.
openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/rule:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/rule
.PHONY : openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/rule

# Convenience name for target.
nr_rlc: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/rule
.PHONY : nr_rlc

# fast build rule for target.
nr_rlc/fast:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/build.make openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/build
.PHONY : nr_rlc/fast

asn1_utils.o: asn1_utils.c.o
.PHONY : asn1_utils.o

# target to build an object file
asn1_utils.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/build.make openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/asn1_utils.c.o
.PHONY : asn1_utils.c.o

asn1_utils.i: asn1_utils.c.i
.PHONY : asn1_utils.i

# target to preprocess a source file
asn1_utils.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/build.make openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/asn1_utils.c.i
.PHONY : asn1_utils.c.i

asn1_utils.s: asn1_utils.c.s
.PHONY : asn1_utils.s

# target to generate assembly for a file
asn1_utils.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/build.make openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/asn1_utils.c.s
.PHONY : asn1_utils.c.s

nr_rlc_entity.o: nr_rlc_entity.c.o
.PHONY : nr_rlc_entity.o

# target to build an object file
nr_rlc_entity.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/build.make openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity.c.o
.PHONY : nr_rlc_entity.c.o

nr_rlc_entity.i: nr_rlc_entity.c.i
.PHONY : nr_rlc_entity.i

# target to preprocess a source file
nr_rlc_entity.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/build.make openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity.c.i
.PHONY : nr_rlc_entity.c.i

nr_rlc_entity.s: nr_rlc_entity.c.s
.PHONY : nr_rlc_entity.s

# target to generate assembly for a file
nr_rlc_entity.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/build.make openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity.c.s
.PHONY : nr_rlc_entity.c.s

nr_rlc_entity_am.o: nr_rlc_entity_am.c.o
.PHONY : nr_rlc_entity_am.o

# target to build an object file
nr_rlc_entity_am.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/build.make openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_am.c.o
.PHONY : nr_rlc_entity_am.c.o

nr_rlc_entity_am.i: nr_rlc_entity_am.c.i
.PHONY : nr_rlc_entity_am.i

# target to preprocess a source file
nr_rlc_entity_am.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/build.make openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_am.c.i
.PHONY : nr_rlc_entity_am.c.i

nr_rlc_entity_am.s: nr_rlc_entity_am.c.s
.PHONY : nr_rlc_entity_am.s

# target to generate assembly for a file
nr_rlc_entity_am.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/build.make openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_am.c.s
.PHONY : nr_rlc_entity_am.c.s

nr_rlc_entity_tm.o: nr_rlc_entity_tm.c.o
.PHONY : nr_rlc_entity_tm.o

# target to build an object file
nr_rlc_entity_tm.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/build.make openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_tm.c.o
.PHONY : nr_rlc_entity_tm.c.o

nr_rlc_entity_tm.i: nr_rlc_entity_tm.c.i
.PHONY : nr_rlc_entity_tm.i

# target to preprocess a source file
nr_rlc_entity_tm.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/build.make openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_tm.c.i
.PHONY : nr_rlc_entity_tm.c.i

nr_rlc_entity_tm.s: nr_rlc_entity_tm.c.s
.PHONY : nr_rlc_entity_tm.s

# target to generate assembly for a file
nr_rlc_entity_tm.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/build.make openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_tm.c.s
.PHONY : nr_rlc_entity_tm.c.s

nr_rlc_entity_um.o: nr_rlc_entity_um.c.o
.PHONY : nr_rlc_entity_um.o

# target to build an object file
nr_rlc_entity_um.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/build.make openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_um.c.o
.PHONY : nr_rlc_entity_um.c.o

nr_rlc_entity_um.i: nr_rlc_entity_um.c.i
.PHONY : nr_rlc_entity_um.i

# target to preprocess a source file
nr_rlc_entity_um.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/build.make openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_um.c.i
.PHONY : nr_rlc_entity_um.c.i

nr_rlc_entity_um.s: nr_rlc_entity_um.c.s
.PHONY : nr_rlc_entity_um.s

# target to generate assembly for a file
nr_rlc_entity_um.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/build.make openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_um.c.s
.PHONY : nr_rlc_entity_um.c.s

nr_rlc_oai_api.o: nr_rlc_oai_api.c.o
.PHONY : nr_rlc_oai_api.o

# target to build an object file
nr_rlc_oai_api.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/build.make openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/nr_rlc_oai_api.c.o
.PHONY : nr_rlc_oai_api.c.o

nr_rlc_oai_api.i: nr_rlc_oai_api.c.i
.PHONY : nr_rlc_oai_api.i

# target to preprocess a source file
nr_rlc_oai_api.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/build.make openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/nr_rlc_oai_api.c.i
.PHONY : nr_rlc_oai_api.c.i

nr_rlc_oai_api.s: nr_rlc_oai_api.c.s
.PHONY : nr_rlc_oai_api.s

# target to generate assembly for a file
nr_rlc_oai_api.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/build.make openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/nr_rlc_oai_api.c.s
.PHONY : nr_rlc_oai_api.c.s

nr_rlc_pdu.o: nr_rlc_pdu.c.o
.PHONY : nr_rlc_pdu.o

# target to build an object file
nr_rlc_pdu.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/build.make openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_pdu.c.o
.PHONY : nr_rlc_pdu.c.o

nr_rlc_pdu.i: nr_rlc_pdu.c.i
.PHONY : nr_rlc_pdu.i

# target to preprocess a source file
nr_rlc_pdu.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/build.make openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_pdu.c.i
.PHONY : nr_rlc_pdu.c.i

nr_rlc_pdu.s: nr_rlc_pdu.c.s
.PHONY : nr_rlc_pdu.s

# target to generate assembly for a file
nr_rlc_pdu.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/build.make openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_pdu.c.s
.PHONY : nr_rlc_pdu.c.s

nr_rlc_sdu.o: nr_rlc_sdu.c.o
.PHONY : nr_rlc_sdu.o

# target to build an object file
nr_rlc_sdu.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/build.make openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_sdu.c.o
.PHONY : nr_rlc_sdu.c.o

nr_rlc_sdu.i: nr_rlc_sdu.c.i
.PHONY : nr_rlc_sdu.i

# target to preprocess a source file
nr_rlc_sdu.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/build.make openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_sdu.c.i
.PHONY : nr_rlc_sdu.c.i

nr_rlc_sdu.s: nr_rlc_sdu.c.s
.PHONY : nr_rlc_sdu.s

# target to generate assembly for a file
nr_rlc_sdu.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/build.make openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_sdu.c.s
.PHONY : nr_rlc_sdu.c.s

nr_rlc_ue_manager.o: nr_rlc_ue_manager.c.o
.PHONY : nr_rlc_ue_manager.o

# target to build an object file
nr_rlc_ue_manager.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/build.make openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/nr_rlc_ue_manager.c.o
.PHONY : nr_rlc_ue_manager.c.o

nr_rlc_ue_manager.i: nr_rlc_ue_manager.c.i
.PHONY : nr_rlc_ue_manager.i

# target to preprocess a source file
nr_rlc_ue_manager.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/build.make openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/nr_rlc_ue_manager.c.i
.PHONY : nr_rlc_ue_manager.c.i

nr_rlc_ue_manager.s: nr_rlc_ue_manager.c.s
.PHONY : nr_rlc_ue_manager.s

# target to generate assembly for a file
nr_rlc_ue_manager.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/build.make openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/nr_rlc_ue_manager.c.s
.PHONY : nr_rlc_ue_manager.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... nr_rlc"
	@echo "... nr_rlc_core"
	@echo "... asn1_utils.o"
	@echo "... asn1_utils.i"
	@echo "... asn1_utils.s"
	@echo "... nr_rlc_entity.o"
	@echo "... nr_rlc_entity.i"
	@echo "... nr_rlc_entity.s"
	@echo "... nr_rlc_entity_am.o"
	@echo "... nr_rlc_entity_am.i"
	@echo "... nr_rlc_entity_am.s"
	@echo "... nr_rlc_entity_tm.o"
	@echo "... nr_rlc_entity_tm.i"
	@echo "... nr_rlc_entity_tm.s"
	@echo "... nr_rlc_entity_um.o"
	@echo "... nr_rlc_entity_um.i"
	@echo "... nr_rlc_entity_um.s"
	@echo "... nr_rlc_oai_api.o"
	@echo "... nr_rlc_oai_api.i"
	@echo "... nr_rlc_oai_api.s"
	@echo "... nr_rlc_pdu.o"
	@echo "... nr_rlc_pdu.i"
	@echo "... nr_rlc_pdu.s"
	@echo "... nr_rlc_sdu.o"
	@echo "... nr_rlc_sdu.i"
	@echo "... nr_rlc_sdu.s"
	@echo "... nr_rlc_ue_manager.o"
	@echo "... nr_rlc_ue_manager.i"
	@echo "... nr_rlc_ue_manager.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

