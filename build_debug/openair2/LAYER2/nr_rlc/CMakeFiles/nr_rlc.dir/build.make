# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/oaiseu

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/oaiseu/build_debug

# Include any dependencies generated for this target.
include openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/compiler_depend.make

# Include the progress variables for this target.
include openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/progress.make

# Include the compile flags for this target's objects.
include openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/flags.make

openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/asn1_utils.c.o: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/flags.make
openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/asn1_utils.c.o: ../openair2/LAYER2/nr_rlc/asn1_utils.c
openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/asn1_utils.c.o: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/asn1_utils.c.o"
	cd /home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc && /usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/asn1_utils.c.o -MF CMakeFiles/nr_rlc.dir/asn1_utils.c.o.d -o CMakeFiles/nr_rlc.dir/asn1_utils.c.o -c /home/<USER>/oaiseu/openair2/LAYER2/nr_rlc/asn1_utils.c

openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/asn1_utils.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nr_rlc.dir/asn1_utils.c.i"
	cd /home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/oaiseu/openair2/LAYER2/nr_rlc/asn1_utils.c > CMakeFiles/nr_rlc.dir/asn1_utils.c.i

openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/asn1_utils.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nr_rlc.dir/asn1_utils.c.s"
	cd /home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/oaiseu/openair2/LAYER2/nr_rlc/asn1_utils.c -o CMakeFiles/nr_rlc.dir/asn1_utils.c.s

openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/nr_rlc_oai_api.c.o: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/flags.make
openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/nr_rlc_oai_api.c.o: ../openair2/LAYER2/nr_rlc/nr_rlc_oai_api.c
openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/nr_rlc_oai_api.c.o: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/nr_rlc_oai_api.c.o"
	cd /home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc && /usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/nr_rlc_oai_api.c.o -MF CMakeFiles/nr_rlc.dir/nr_rlc_oai_api.c.o.d -o CMakeFiles/nr_rlc.dir/nr_rlc_oai_api.c.o -c /home/<USER>/oaiseu/openair2/LAYER2/nr_rlc/nr_rlc_oai_api.c

openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/nr_rlc_oai_api.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nr_rlc.dir/nr_rlc_oai_api.c.i"
	cd /home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/oaiseu/openair2/LAYER2/nr_rlc/nr_rlc_oai_api.c > CMakeFiles/nr_rlc.dir/nr_rlc_oai_api.c.i

openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/nr_rlc_oai_api.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nr_rlc.dir/nr_rlc_oai_api.c.s"
	cd /home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/oaiseu/openair2/LAYER2/nr_rlc/nr_rlc_oai_api.c -o CMakeFiles/nr_rlc.dir/nr_rlc_oai_api.c.s

openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/nr_rlc_ue_manager.c.o: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/flags.make
openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/nr_rlc_ue_manager.c.o: ../openair2/LAYER2/nr_rlc/nr_rlc_ue_manager.c
openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/nr_rlc_ue_manager.c.o: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/nr_rlc_ue_manager.c.o"
	cd /home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc && /usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/nr_rlc_ue_manager.c.o -MF CMakeFiles/nr_rlc.dir/nr_rlc_ue_manager.c.o.d -o CMakeFiles/nr_rlc.dir/nr_rlc_ue_manager.c.o -c /home/<USER>/oaiseu/openair2/LAYER2/nr_rlc/nr_rlc_ue_manager.c

openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/nr_rlc_ue_manager.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nr_rlc.dir/nr_rlc_ue_manager.c.i"
	cd /home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/oaiseu/openair2/LAYER2/nr_rlc/nr_rlc_ue_manager.c > CMakeFiles/nr_rlc.dir/nr_rlc_ue_manager.c.i

openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/nr_rlc_ue_manager.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nr_rlc.dir/nr_rlc_ue_manager.c.s"
	cd /home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/oaiseu/openair2/LAYER2/nr_rlc/nr_rlc_ue_manager.c -o CMakeFiles/nr_rlc.dir/nr_rlc_ue_manager.c.s

# Object files for target nr_rlc
nr_rlc_OBJECTS = \
"CMakeFiles/nr_rlc.dir/asn1_utils.c.o" \
"CMakeFiles/nr_rlc.dir/nr_rlc_oai_api.c.o" \
"CMakeFiles/nr_rlc.dir/nr_rlc_ue_manager.c.o"

# External object files for target nr_rlc
nr_rlc_EXTERNAL_OBJECTS = \
"/home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity.c.o" \
"/home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_am.c.o" \
"/home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_tm.c.o" \
"/home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_um.c.o" \
"/home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_pdu.c.o" \
"/home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_sdu.c.o"

openair2/LAYER2/nr_rlc/libnr_rlc.a: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/asn1_utils.c.o
openair2/LAYER2/nr_rlc/libnr_rlc.a: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/nr_rlc_oai_api.c.o
openair2/LAYER2/nr_rlc/libnr_rlc.a: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/nr_rlc_ue_manager.c.o
openair2/LAYER2/nr_rlc/libnr_rlc.a: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity.c.o
openair2/LAYER2/nr_rlc/libnr_rlc.a: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_am.c.o
openair2/LAYER2/nr_rlc/libnr_rlc.a: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_tm.c.o
openair2/LAYER2/nr_rlc/libnr_rlc.a: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_um.c.o
openair2/LAYER2/nr_rlc/libnr_rlc.a: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_pdu.c.o
openair2/LAYER2/nr_rlc/libnr_rlc.a: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_sdu.c.o
openair2/LAYER2/nr_rlc/libnr_rlc.a: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/build.make
openair2/LAYER2/nr_rlc/libnr_rlc.a: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Linking CXX static library libnr_rlc.a"
	cd /home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc && $(CMAKE_COMMAND) -P CMakeFiles/nr_rlc.dir/cmake_clean_target.cmake
	cd /home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/nr_rlc.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/build: openair2/LAYER2/nr_rlc/libnr_rlc.a
.PHONY : openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/build

openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/clean:
	cd /home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc && $(CMAKE_COMMAND) -P CMakeFiles/nr_rlc.dir/cmake_clean.cmake
.PHONY : openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/clean

openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/depend:
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/oaiseu /home/<USER>/oaiseu/openair2/LAYER2/nr_rlc /home/<USER>/oaiseu/build_debug /home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc /home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc.dir/depend

