# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/oaiseu

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/oaiseu/build_debug

# Include any dependencies generated for this target.
include openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/compiler_depend.make

# Include the progress variables for this target.
include openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/progress.make

# Include the compile flags for this target's objects.
include openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/flags.make

openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity.c.o: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/flags.make
openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity.c.o: ../openair2/LAYER2/nr_rlc/nr_rlc_entity.c
openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity.c.o: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity.c.o"
	cd /home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc && /usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity.c.o -MF CMakeFiles/nr_rlc_core.dir/nr_rlc_entity.c.o.d -o CMakeFiles/nr_rlc_core.dir/nr_rlc_entity.c.o -c /home/<USER>/oaiseu/openair2/LAYER2/nr_rlc/nr_rlc_entity.c

openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nr_rlc_core.dir/nr_rlc_entity.c.i"
	cd /home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/oaiseu/openair2/LAYER2/nr_rlc/nr_rlc_entity.c > CMakeFiles/nr_rlc_core.dir/nr_rlc_entity.c.i

openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nr_rlc_core.dir/nr_rlc_entity.c.s"
	cd /home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/oaiseu/openair2/LAYER2/nr_rlc/nr_rlc_entity.c -o CMakeFiles/nr_rlc_core.dir/nr_rlc_entity.c.s

openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_am.c.o: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/flags.make
openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_am.c.o: ../openair2/LAYER2/nr_rlc/nr_rlc_entity_am.c
openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_am.c.o: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_am.c.o"
	cd /home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc && /usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_am.c.o -MF CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_am.c.o.d -o CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_am.c.o -c /home/<USER>/oaiseu/openair2/LAYER2/nr_rlc/nr_rlc_entity_am.c

openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_am.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_am.c.i"
	cd /home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/oaiseu/openair2/LAYER2/nr_rlc/nr_rlc_entity_am.c > CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_am.c.i

openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_am.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_am.c.s"
	cd /home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/oaiseu/openair2/LAYER2/nr_rlc/nr_rlc_entity_am.c -o CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_am.c.s

openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_tm.c.o: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/flags.make
openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_tm.c.o: ../openair2/LAYER2/nr_rlc/nr_rlc_entity_tm.c
openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_tm.c.o: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_tm.c.o"
	cd /home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc && /usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_tm.c.o -MF CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_tm.c.o.d -o CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_tm.c.o -c /home/<USER>/oaiseu/openair2/LAYER2/nr_rlc/nr_rlc_entity_tm.c

openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_tm.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_tm.c.i"
	cd /home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/oaiseu/openair2/LAYER2/nr_rlc/nr_rlc_entity_tm.c > CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_tm.c.i

openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_tm.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_tm.c.s"
	cd /home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/oaiseu/openair2/LAYER2/nr_rlc/nr_rlc_entity_tm.c -o CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_tm.c.s

openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_um.c.o: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/flags.make
openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_um.c.o: ../openair2/LAYER2/nr_rlc/nr_rlc_entity_um.c
openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_um.c.o: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_um.c.o"
	cd /home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc && /usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_um.c.o -MF CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_um.c.o.d -o CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_um.c.o -c /home/<USER>/oaiseu/openair2/LAYER2/nr_rlc/nr_rlc_entity_um.c

openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_um.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_um.c.i"
	cd /home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/oaiseu/openair2/LAYER2/nr_rlc/nr_rlc_entity_um.c > CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_um.c.i

openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_um.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_um.c.s"
	cd /home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/oaiseu/openair2/LAYER2/nr_rlc/nr_rlc_entity_um.c -o CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_um.c.s

openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_pdu.c.o: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/flags.make
openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_pdu.c.o: ../openair2/LAYER2/nr_rlc/nr_rlc_pdu.c
openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_pdu.c.o: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_pdu.c.o"
	cd /home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc && /usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_pdu.c.o -MF CMakeFiles/nr_rlc_core.dir/nr_rlc_pdu.c.o.d -o CMakeFiles/nr_rlc_core.dir/nr_rlc_pdu.c.o -c /home/<USER>/oaiseu/openair2/LAYER2/nr_rlc/nr_rlc_pdu.c

openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_pdu.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nr_rlc_core.dir/nr_rlc_pdu.c.i"
	cd /home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/oaiseu/openair2/LAYER2/nr_rlc/nr_rlc_pdu.c > CMakeFiles/nr_rlc_core.dir/nr_rlc_pdu.c.i

openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_pdu.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nr_rlc_core.dir/nr_rlc_pdu.c.s"
	cd /home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/oaiseu/openair2/LAYER2/nr_rlc/nr_rlc_pdu.c -o CMakeFiles/nr_rlc_core.dir/nr_rlc_pdu.c.s

openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_sdu.c.o: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/flags.make
openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_sdu.c.o: ../openair2/LAYER2/nr_rlc/nr_rlc_sdu.c
openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_sdu.c.o: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_sdu.c.o"
	cd /home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc && /usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_sdu.c.o -MF CMakeFiles/nr_rlc_core.dir/nr_rlc_sdu.c.o.d -o CMakeFiles/nr_rlc_core.dir/nr_rlc_sdu.c.o -c /home/<USER>/oaiseu/openair2/LAYER2/nr_rlc/nr_rlc_sdu.c

openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_sdu.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/nr_rlc_core.dir/nr_rlc_sdu.c.i"
	cd /home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/oaiseu/openair2/LAYER2/nr_rlc/nr_rlc_sdu.c > CMakeFiles/nr_rlc_core.dir/nr_rlc_sdu.c.i

openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_sdu.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/nr_rlc_core.dir/nr_rlc_sdu.c.s"
	cd /home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/oaiseu/openair2/LAYER2/nr_rlc/nr_rlc_sdu.c -o CMakeFiles/nr_rlc_core.dir/nr_rlc_sdu.c.s

nr_rlc_core: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity.c.o
nr_rlc_core: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_am.c.o
nr_rlc_core: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_tm.c.o
nr_rlc_core: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_um.c.o
nr_rlc_core: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_pdu.c.o
nr_rlc_core: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_sdu.c.o
nr_rlc_core: openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/build.make
.PHONY : nr_rlc_core

# Rule to build all files generated by this target.
openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/build: nr_rlc_core
.PHONY : openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/build

openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/clean:
	cd /home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc && $(CMAKE_COMMAND) -P CMakeFiles/nr_rlc_core.dir/cmake_clean.cmake
.PHONY : openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/clean

openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/depend:
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/oaiseu /home/<USER>/oaiseu/openair2/LAYER2/nr_rlc /home/<USER>/oaiseu/build_debug /home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc /home/<USER>/oaiseu/build_debug/openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/depend

