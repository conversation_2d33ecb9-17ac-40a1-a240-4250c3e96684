
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/oaiseu/openair2/LAYER2/nr_rlc/nr_rlc_entity.c" "openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity.c.o" "gcc" "openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity.c.o.d"
  "/home/<USER>/oaiseu/openair2/LAYER2/nr_rlc/nr_rlc_entity_am.c" "openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_am.c.o" "gcc" "openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_am.c.o.d"
  "/home/<USER>/oaiseu/openair2/LAYER2/nr_rlc/nr_rlc_entity_tm.c" "openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_tm.c.o" "gcc" "openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_tm.c.o.d"
  "/home/<USER>/oaiseu/openair2/LAYER2/nr_rlc/nr_rlc_entity_um.c" "openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_um.c.o" "gcc" "openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_entity_um.c.o.d"
  "/home/<USER>/oaiseu/openair2/LAYER2/nr_rlc/nr_rlc_pdu.c" "openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_pdu.c.o" "gcc" "openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_pdu.c.o.d"
  "/home/<USER>/oaiseu/openair2/LAYER2/nr_rlc/nr_rlc_sdu.c" "openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_sdu.c.o" "gcc" "openair2/LAYER2/nr_rlc/CMakeFiles/nr_rlc_core.dir/nr_rlc_sdu.c.o.d"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
