file(REMOVE_RECURSE
  "ANY.c"
  "ANY.h"
  "ANY_aper.c"
  "ANY_uper.c"
  "ANY_xer.c"
  "BIT_STRING.c"
  "BIT_STRING.h"
  "BIT_STRING_print.c"
  "BIT_STRING_rfill.c"
  "BIT_STRING_uper.c"
  "BIT_STRING_xer.c"
  "BOOLEAN.c"
  "BOOLEAN.h"
  "BOOLEAN_aper.c"
  "BOOLEAN_print.c"
  "BOOLEAN_rfill.c"
  "BOOLEAN_uper.c"
  "BOOLEAN_xer.c"
  "CMakeFiles/gen_nr_rrc_hdrs"
  "GraphicString.c"
  "GraphicString.h"
  "INTEGER.c"
  "INTEGER.h"
  "INTEGER_aper.c"
  "INTEGER_print.c"
  "INTEGER_rfill.c"
  "INTEGER_uper.c"
  "INTEGER_xer.c"
  "NR_AI-RNTI-r16.c"
  "NR_AI-RNTI-r16.h"
  "NR_AMF-Identifier.c"
  "NR_AMF-Identifier.h"
  "NR_ARFCN-ValueEUTRA.c"
  "NR_ARFCN-ValueEUTRA.h"
  "NR_ARFCN-ValueNR.c"
  "NR_ARFCN-ValueNR.h"
  "NR_ARFCN-ValueUTRA-FDD-r16.c"
  "NR_ARFCN-ValueUTRA-FDD-r16.h"
  "NR_AS-Config.c"
  "NR_AS-Config.h"
  "NR_AS-Context.c"
  "NR_AS-Context.h"
  "NR_AbsoluteTimeInfo-r16.c"
  "NR_AbsoluteTimeInfo-r16.h"
  "NR_AccessStratumRelease.c"
  "NR_AccessStratumRelease.h"
  "NR_AccessStratumReleaseSidelink-r16.c"
  "NR_AccessStratumReleaseSidelink-r16.h"
  "NR_AdditionalPCIIndex-r17.c"
  "NR_AdditionalPCIIndex-r17.h"
  "NR_AdditionalRACH-Config-r17.c"
  "NR_AdditionalRACH-Config-r17.h"
  "NR_AdditionalRACH-ConfigList-r17.c"
  "NR_AdditionalRACH-ConfigList-r17.h"
  "NR_AdditionalSpectrumEmission.c"
  "NR_AdditionalSpectrumEmission.h"
  "NR_AffectedCarrierFreq-r16.c"
  "NR_AffectedCarrierFreq-r16.h"
  "NR_AffectedCarrierFreqComb-r16.c"
  "NR_AffectedCarrierFreqComb-r16.h"
  "NR_AffectedCarrierFreqCombEUTRA.c"
  "NR_AffectedCarrierFreqCombEUTRA.h"
  "NR_AffectedCarrierFreqCombInfoMRDC.c"
  "NR_AffectedCarrierFreqCombInfoMRDC.h"
  "NR_AffectedCarrierFreqCombList-r16.c"
  "NR_AffectedCarrierFreqCombList-r16.h"
  "NR_AffectedCarrierFreqCombNR.c"
  "NR_AffectedCarrierFreqCombNR.h"
  "NR_AffectedCarrierFreqList-r16.c"
  "NR_AffectedCarrierFreqList-r16.h"
  "NR_AggregatedBandwidth.c"
  "NR_AggregatedBandwidth.h"
  "NR_Alpha.c"
  "NR_Alpha.h"
  "NR_AppLayerBufferLevel-r17.c"
  "NR_AppLayerBufferLevel-r17.h"
  "NR_AppLayerMeasConfig-r17.c"
  "NR_AppLayerMeasConfig-r17.h"
  "NR_AppLayerMeasParameters-r17.c"
  "NR_AppLayerMeasParameters-r17.h"
  "NR_ApplicableDisasterInfo-r17.c"
  "NR_ApplicableDisasterInfo-r17.h"
  "NR_AreaConfig-r16.c"
  "NR_AreaConfig-r16.h"
  "NR_AreaConfiguration-r16.c"
  "NR_AreaConfiguration-r16.h"
  "NR_AreaConfiguration-v1700.c"
  "NR_AreaConfiguration-v1700.h"
  "NR_AvailabilityCombination-r16.c"
  "NR_AvailabilityCombination-r16.h"
  "NR_AvailabilityCombinationId-r16.c"
  "NR_AvailabilityCombinationId-r16.h"
  "NR_AvailabilityCombinationRB-Groups-r17.c"
  "NR_AvailabilityCombinationRB-Groups-r17.h"
  "NR_AvailabilityCombinationsPerCell-r16.c"
  "NR_AvailabilityCombinationsPerCell-r16.h"
  "NR_AvailabilityCombinationsPerCellIndex-r16.c"
  "NR_AvailabilityCombinationsPerCellIndex-r16.h"
  "NR_AvailabilityIndicator-r16.c"
  "NR_AvailabilityIndicator-r16.h"
  "NR_AvailableRB-SetsPerCell-r16.c"
  "NR_AvailableRB-SetsPerCell-r16.h"
  "NR_AvailableSlotOffset-r17.c"
  "NR_AvailableSlotOffset-r17.h"
  "NR_BAP-Config-r16.c"
  "NR_BAP-Config-r16.h"
  "NR_BAP-Parameters-r16.c"
  "NR_BAP-Parameters-r16.h"
  "NR_BAP-Parameters-v1700.c"
  "NR_BAP-Parameters-v1700.h"
  "NR_BAP-RoutingID-r16.c"
  "NR_BAP-RoutingID-r16.h"
  "NR_BCCH-BCH-Message.c"
  "NR_BCCH-BCH-Message.h"
  "NR_BCCH-BCH-MessageType.c"
  "NR_BCCH-BCH-MessageType.h"
  "NR_BCCH-Config.c"
  "NR_BCCH-Config.h"
  "NR_BCCH-DL-SCH-Message.c"
  "NR_BCCH-DL-SCH-Message.h"
  "NR_BCCH-DL-SCH-MessageType.c"
  "NR_BCCH-DL-SCH-MessageType.h"
  "NR_BFD-RelaxationReportingConfig-r17.c"
  "NR_BFD-RelaxationReportingConfig-r17.h"
  "NR_BFR-CSIRS-Resource.c"
  "NR_BFR-CSIRS-Resource.h"
  "NR_BFR-SSB-Resource.c"
  "NR_BFR-SSB-Resource.h"
  "NR_BH-LogicalChannelIdentity-Ext-r16.c"
  "NR_BH-LogicalChannelIdentity-Ext-r16.h"
  "NR_BH-LogicalChannelIdentity-r16.c"
  "NR_BH-LogicalChannelIdentity-r16.h"
  "NR_BH-RLC-ChannelConfig-r16.c"
  "NR_BH-RLC-ChannelConfig-r16.h"
  "NR_BH-RLC-ChannelID-r16.c"
  "NR_BH-RLC-ChannelID-r16.h"
  "NR_BSR-Config.c"
  "NR_BSR-Config.h"
  "NR_BT-Name-r16.c"
  "NR_BT-Name-r16.h"
  "NR_BT-NameList-r16.c"
  "NR_BT-NameList-r16.h"
  "NR_BWP-Downlink.c"
  "NR_BWP-Downlink.h"
  "NR_BWP-DownlinkCommon.c"
  "NR_BWP-DownlinkCommon.h"
  "NR_BWP-DownlinkDedicated.c"
  "NR_BWP-DownlinkDedicated.h"
  "NR_BWP-DownlinkDedicatedSDT-r17.c"
  "NR_BWP-DownlinkDedicatedSDT-r17.h"
  "NR_BWP-Id.c"
  "NR_BWP-Id.h"
  "NR_BWP-Uplink.c"
  "NR_BWP-Uplink.h"
  "NR_BWP-UplinkCommon.c"
  "NR_BWP-UplinkCommon.h"
  "NR_BWP-UplinkDedicated.c"
  "NR_BWP-UplinkDedicated.h"
  "NR_BWP-UplinkDedicatedSDT-r17.c"
  "NR_BWP-UplinkDedicatedSDT-r17.h"
  "NR_BWP.c"
  "NR_BWP.h"
  "NR_BandCombination-UplinkTxSwitch-r16.c"
  "NR_BandCombination-UplinkTxSwitch-r16.h"
  "NR_BandCombination-UplinkTxSwitch-v1630.c"
  "NR_BandCombination-UplinkTxSwitch-v1630.h"
  "NR_BandCombination-UplinkTxSwitch-v1640.c"
  "NR_BandCombination-UplinkTxSwitch-v1640.h"
  "NR_BandCombination-UplinkTxSwitch-v1650.c"
  "NR_BandCombination-UplinkTxSwitch-v1650.h"
  "NR_BandCombination-UplinkTxSwitch-v1670.c"
  "NR_BandCombination-UplinkTxSwitch-v1670.h"
  "NR_BandCombination-UplinkTxSwitch-v1690.c"
  "NR_BandCombination-UplinkTxSwitch-v1690.h"
  "NR_BandCombination-UplinkTxSwitch-v16a0.c"
  "NR_BandCombination-UplinkTxSwitch-v16a0.h"
  "NR_BandCombination-UplinkTxSwitch-v1700.c"
  "NR_BandCombination-UplinkTxSwitch-v1700.h"
  "NR_BandCombination-UplinkTxSwitch-v1720.c"
  "NR_BandCombination-UplinkTxSwitch-v1720.h"
  "NR_BandCombination-UplinkTxSwitch-v1730.c"
  "NR_BandCombination-UplinkTxSwitch-v1730.h"
  "NR_BandCombination-v1540.c"
  "NR_BandCombination-v1540.h"
  "NR_BandCombination-v1550.c"
  "NR_BandCombination-v1550.h"
  "NR_BandCombination-v1560.c"
  "NR_BandCombination-v1560.h"
  "NR_BandCombination-v1570.c"
  "NR_BandCombination-v1570.h"
  "NR_BandCombination-v1580.c"
  "NR_BandCombination-v1580.h"
  "NR_BandCombination-v1590.c"
  "NR_BandCombination-v1590.h"
  "NR_BandCombination-v15g0.c"
  "NR_BandCombination-v15g0.h"
  "NR_BandCombination-v1610.c"
  "NR_BandCombination-v1610.h"
  "NR_BandCombination-v1630.c"
  "NR_BandCombination-v1630.h"
  "NR_BandCombination-v1640.c"
  "NR_BandCombination-v1640.h"
  "NR_BandCombination-v1650.c"
  "NR_BandCombination-v1650.h"
  "NR_BandCombination-v1680.c"
  "NR_BandCombination-v1680.h"
  "NR_BandCombination-v1690.c"
  "NR_BandCombination-v1690.h"
  "NR_BandCombination-v16a0.c"
  "NR_BandCombination-v16a0.h"
  "NR_BandCombination-v1700.c"
  "NR_BandCombination-v1700.h"
  "NR_BandCombination-v1720.c"
  "NR_BandCombination-v1720.h"
  "NR_BandCombination-v1730.c"
  "NR_BandCombination-v1730.h"
  "NR_BandCombination.c"
  "NR_BandCombination.h"
  "NR_BandCombinationIndex.c"
  "NR_BandCombinationIndex.h"
  "NR_BandCombinationInfo.c"
  "NR_BandCombinationInfo.h"
  "NR_BandCombinationInfoList.c"
  "NR_BandCombinationInfoList.h"
  "NR_BandCombinationInfoSN.c"
  "NR_BandCombinationInfoSN.h"
  "NR_BandCombinationList-UplinkTxSwitch-r16.c"
  "NR_BandCombinationList-UplinkTxSwitch-r16.h"
  "NR_BandCombinationList-UplinkTxSwitch-v1630.c"
  "NR_BandCombinationList-UplinkTxSwitch-v1630.h"
  "NR_BandCombinationList-UplinkTxSwitch-v1640.c"
  "NR_BandCombinationList-UplinkTxSwitch-v1640.h"
  "NR_BandCombinationList-UplinkTxSwitch-v1650.c"
  "NR_BandCombinationList-UplinkTxSwitch-v1650.h"
  "NR_BandCombinationList-UplinkTxSwitch-v1670.c"
  "NR_BandCombinationList-UplinkTxSwitch-v1670.h"
  "NR_BandCombinationList-UplinkTxSwitch-v1690.c"
  "NR_BandCombinationList-UplinkTxSwitch-v1690.h"
  "NR_BandCombinationList-UplinkTxSwitch-v16a0.c"
  "NR_BandCombinationList-UplinkTxSwitch-v16a0.h"
  "NR_BandCombinationList-UplinkTxSwitch-v1700.c"
  "NR_BandCombinationList-UplinkTxSwitch-v1700.h"
  "NR_BandCombinationList-UplinkTxSwitch-v1720.c"
  "NR_BandCombinationList-UplinkTxSwitch-v1720.h"
  "NR_BandCombinationList-UplinkTxSwitch-v1730.c"
  "NR_BandCombinationList-UplinkTxSwitch-v1730.h"
  "NR_BandCombinationList-v1540.c"
  "NR_BandCombinationList-v1540.h"
  "NR_BandCombinationList-v1550.c"
  "NR_BandCombinationList-v1550.h"
  "NR_BandCombinationList-v1560.c"
  "NR_BandCombinationList-v1560.h"
  "NR_BandCombinationList-v1570.c"
  "NR_BandCombinationList-v1570.h"
  "NR_BandCombinationList-v1580.c"
  "NR_BandCombinationList-v1580.h"
  "NR_BandCombinationList-v1590.c"
  "NR_BandCombinationList-v1590.h"
  "NR_BandCombinationList-v15g0.c"
  "NR_BandCombinationList-v15g0.h"
  "NR_BandCombinationList-v1610.c"
  "NR_BandCombinationList-v1610.h"
  "NR_BandCombinationList-v1630.c"
  "NR_BandCombinationList-v1630.h"
  "NR_BandCombinationList-v1640.c"
  "NR_BandCombinationList-v1640.h"
  "NR_BandCombinationList-v1650.c"
  "NR_BandCombinationList-v1650.h"
  "NR_BandCombinationList-v1680.c"
  "NR_BandCombinationList-v1680.h"
  "NR_BandCombinationList-v1690.c"
  "NR_BandCombinationList-v1690.h"
  "NR_BandCombinationList-v16a0.c"
  "NR_BandCombinationList-v16a0.h"
  "NR_BandCombinationList-v1700.c"
  "NR_BandCombinationList-v1700.h"
  "NR_BandCombinationList-v1720.c"
  "NR_BandCombinationList-v1720.h"
  "NR_BandCombinationList-v1730.c"
  "NR_BandCombinationList-v1730.h"
  "NR_BandCombinationList.c"
  "NR_BandCombinationList.h"
  "NR_BandCombinationListSL-Discovery-r17.c"
  "NR_BandCombinationListSL-Discovery-r17.h"
  "NR_BandCombinationListSidelinkEUTRA-NR-r16.c"
  "NR_BandCombinationListSidelinkEUTRA-NR-r16.h"
  "NR_BandCombinationListSidelinkEUTRA-NR-v1630.c"
  "NR_BandCombinationListSidelinkEUTRA-NR-v1630.h"
  "NR_BandCombinationListSidelinkEUTRA-NR-v1710.c"
  "NR_BandCombinationListSidelinkEUTRA-NR-v1710.h"
  "NR_BandCombinationListSidelinkNR-r16.c"
  "NR_BandCombinationListSidelinkNR-r16.h"
  "NR_BandCombinationListSidelinkNR-v1710.c"
  "NR_BandCombinationListSidelinkNR-v1710.h"
  "NR_BandCombinationParametersSidelinkEUTRA-NR-r16.c"
  "NR_BandCombinationParametersSidelinkEUTRA-NR-r16.h"
  "NR_BandCombinationParametersSidelinkEUTRA-NR-v1630.c"
  "NR_BandCombinationParametersSidelinkEUTRA-NR-v1630.h"
  "NR_BandCombinationParametersSidelinkEUTRA-NR-v1710.c"
  "NR_BandCombinationParametersSidelinkEUTRA-NR-v1710.h"
  "NR_BandCombinationParametersSidelinkNR-r16.c"
  "NR_BandCombinationParametersSidelinkNR-r16.h"
  "NR_BandCombinationParametersSidelinkNR-v1710.c"
  "NR_BandCombinationParametersSidelinkNR-v1710.h"
  "NR_BandEntryIndex.c"
  "NR_BandEntryIndex.h"
  "NR_BandNR.c"
  "NR_BandNR.h"
  "NR_BandParameters-v1540.c"
  "NR_BandParameters-v1540.h"
  "NR_BandParameters-v1610.c"
  "NR_BandParameters-v1610.h"
  "NR_BandParameters-v1710.c"
  "NR_BandParameters-v1710.h"
  "NR_BandParameters-v1730.c"
  "NR_BandParameters-v1730.h"
  "NR_BandParameters.c"
  "NR_BandParameters.h"
  "NR_BandParametersSidelink-r16.c"
  "NR_BandParametersSidelink-r16.h"
  "NR_BandParametersSidelink-v1710.c"
  "NR_BandParametersSidelink-v1710.h"
  "NR_BandParametersSidelinkDiscovery-r17.c"
  "NR_BandParametersSidelinkDiscovery-r17.h"
  "NR_BandParametersSidelinkEUTRA-NR-r16.c"
  "NR_BandParametersSidelinkEUTRA-NR-r16.h"
  "NR_BandParametersSidelinkEUTRA-NR-v1630.c"
  "NR_BandParametersSidelinkEUTRA-NR-v1630.h"
  "NR_BandParametersSidelinkEUTRA-NR-v1710.c"
  "NR_BandParametersSidelinkEUTRA-NR-v1710.h"
  "NR_BandSidelink-r16.c"
  "NR_BandSidelink-r16.h"
  "NR_BandSidelinkEUTRA-r16.c"
  "NR_BandSidelinkEUTRA-r16.h"
  "NR_BandSidelinkPC5-r16.c"
  "NR_BandSidelinkPC5-r16.h"
  "NR_BeamFailureDetection-r17.c"
  "NR_BeamFailureDetection-r17.h"
  "NR_BeamFailureDetectionSet-r17.c"
  "NR_BeamFailureDetectionSet-r17.h"
  "NR_BeamFailureRecoveryConfig.c"
  "NR_BeamFailureRecoveryConfig.h"
  "NR_BeamFailureRecoveryRSConfig-r16.c"
  "NR_BeamFailureRecoveryRSConfig-r16.h"
  "NR_BeamLinkMonitoringRS-Id-r17.c"
  "NR_BeamLinkMonitoringRS-Id-r17.h"
  "NR_BeamLinkMonitoringRS-r17.c"
  "NR_BeamLinkMonitoringRS-r17.h"
  "NR_BeamManagementSSB-CSI-RS.c"
  "NR_BeamManagementSSB-CSI-RS.h"
  "NR_BeamMeasConfigIdle-NR-r16.c"
  "NR_BeamMeasConfigIdle-NR-r16.h"
  "NR_BetaOffsets.c"
  "NR_BetaOffsets.h"
  "NR_BetaOffsetsCrossPri-r17.c"
  "NR_BetaOffsetsCrossPri-r17.h"
  "NR_BetaOffsetsCrossPriSel-r17.c"
  "NR_BetaOffsetsCrossPriSel-r17.h"
  "NR_BetaOffsetsCrossPriSelCG-r17.c"
  "NR_BetaOffsetsCrossPriSelCG-r17.h"
  "NR_BetaOffsetsCrossPriSelDCI-0-2-r17.c"
  "NR_BetaOffsetsCrossPriSelDCI-0-2-r17.h"
  "NR_CA-BandwidthClassEUTRA.c"
  "NR_CA-BandwidthClassEUTRA.h"
  "NR_CA-BandwidthClassNR.c"
  "NR_CA-BandwidthClassNR.h"
  "NR_CA-ParametersEUTRA-v1560.c"
  "NR_CA-ParametersEUTRA-v1560.h"
  "NR_CA-ParametersEUTRA-v1570.c"
  "NR_CA-ParametersEUTRA-v1570.h"
  "NR_CA-ParametersEUTRA.c"
  "NR_CA-ParametersEUTRA.h"
  "NR_CA-ParametersNR-v1540.c"
  "NR_CA-ParametersNR-v1540.h"
  "NR_CA-ParametersNR-v1550.c"
  "NR_CA-ParametersNR-v1550.h"
  "NR_CA-ParametersNR-v1560.c"
  "NR_CA-ParametersNR-v1560.h"
  "NR_CA-ParametersNR-v15g0.c"
  "NR_CA-ParametersNR-v15g0.h"
  "NR_CA-ParametersNR-v1610.c"
  "NR_CA-ParametersNR-v1610.h"
  "NR_CA-ParametersNR-v1630.c"
  "NR_CA-ParametersNR-v1630.h"
  "NR_CA-ParametersNR-v1640.c"
  "NR_CA-ParametersNR-v1640.h"
  "NR_CA-ParametersNR-v1690.c"
  "NR_CA-ParametersNR-v1690.h"
  "NR_CA-ParametersNR-v16a0.c"
  "NR_CA-ParametersNR-v16a0.h"
  "NR_CA-ParametersNR-v1700.c"
  "NR_CA-ParametersNR-v1700.h"
  "NR_CA-ParametersNR-v1720.c"
  "NR_CA-ParametersNR-v1720.h"
  "NR_CA-ParametersNR-v1730.c"
  "NR_CA-ParametersNR-v1730.h"
  "NR_CA-ParametersNR.c"
  "NR_CA-ParametersNR.h"
  "NR_CA-ParametersNRDC-v15g0.c"
  "NR_CA-ParametersNRDC-v15g0.h"
  "NR_CA-ParametersNRDC-v1610.c"
  "NR_CA-ParametersNRDC-v1610.h"
  "NR_CA-ParametersNRDC-v1630.c"
  "NR_CA-ParametersNRDC-v1630.h"
  "NR_CA-ParametersNRDC-v1640.c"
  "NR_CA-ParametersNRDC-v1640.h"
  "NR_CA-ParametersNRDC-v1650.c"
  "NR_CA-ParametersNRDC-v1650.h"
  "NR_CA-ParametersNRDC-v16a0.c"
  "NR_CA-ParametersNRDC-v16a0.h"
  "NR_CA-ParametersNRDC-v1700.c"
  "NR_CA-ParametersNRDC-v1700.h"
  "NR_CA-ParametersNRDC-v1720.c"
  "NR_CA-ParametersNRDC-v1720.h"
  "NR_CA-ParametersNRDC-v1730.c"
  "NR_CA-ParametersNRDC-v1730.h"
  "NR_CA-ParametersNRDC.c"
  "NR_CA-ParametersNRDC.h"
  "NR_CAG-IdentityInfo-r16.c"
  "NR_CAG-IdentityInfo-r16.h"
  "NR_CC-Group-r17.c"
  "NR_CC-Group-r17.h"
  "NR_CC-State-r17.c"
  "NR_CC-State-r17.h"
  "NR_CFR-ConfigMCCH-MTCH-r17.c"
  "NR_CFR-ConfigMCCH-MTCH-r17.h"
  "NR_CFR-ConfigMulticast-r17.c"
  "NR_CFR-ConfigMulticast-r17.h"
  "NR_CFRA-CSIRS-Resource.c"
  "NR_CFRA-CSIRS-Resource.h"
  "NR_CFRA-SSB-Resource.c"
  "NR_CFRA-SSB-Resource.h"
  "NR_CFRA-TwoStep-r16.c"
  "NR_CFRA-TwoStep-r16.h"
  "NR_CFRA.c"
  "NR_CFRA.h"
  "NR_CG-COT-Sharing-r16.c"
  "NR_CG-COT-Sharing-r16.h"
  "NR_CG-COT-Sharing-r17.c"
  "NR_CG-COT-Sharing-r17.h"
  "NR_CG-CandidateInfo-r17.c"
  "NR_CG-CandidateInfo-r17.h"
  "NR_CG-CandidateInfoId-r17.c"
  "NR_CG-CandidateInfoId-r17.h"
  "NR_CG-CandidateList-r17-IEs.c"
  "NR_CG-CandidateList-r17-IEs.h"
  "NR_CG-CandidateList.c"
  "NR_CG-CandidateList.h"
  "NR_CG-Config-IEs.c"
  "NR_CG-Config-IEs.h"
  "NR_CG-Config-v1540-IEs.c"
  "NR_CG-Config-v1540-IEs.h"
  "NR_CG-Config-v1560-IEs.c"
  "NR_CG-Config-v1560-IEs.h"
  "NR_CG-Config-v1590-IEs.c"
  "NR_CG-Config-v1590-IEs.h"
  "NR_CG-Config-v1610-IEs.c"
  "NR_CG-Config-v1610-IEs.h"
  "NR_CG-Config-v1620-IEs.c"
  "NR_CG-Config-v1620-IEs.h"
  "NR_CG-Config-v1630-IEs.c"
  "NR_CG-Config-v1630-IEs.h"
  "NR_CG-Config-v1640-IEs.c"
  "NR_CG-Config-v1640-IEs.h"
  "NR_CG-Config-v1700-IEs.c"
  "NR_CG-Config-v1700-IEs.h"
  "NR_CG-Config-v1730-IEs.c"
  "NR_CG-Config-v1730-IEs.h"
  "NR_CG-Config.c"
  "NR_CG-Config.h"
  "NR_CG-ConfigInfo-IEs.c"
  "NR_CG-ConfigInfo-IEs.h"
  "NR_CG-ConfigInfo-v1540-IEs.c"
  "NR_CG-ConfigInfo-v1540-IEs.h"
  "NR_CG-ConfigInfo-v1560-IEs.c"
  "NR_CG-ConfigInfo-v1560-IEs.h"
  "NR_CG-ConfigInfo-v1570-IEs.c"
  "NR_CG-ConfigInfo-v1570-IEs.h"
  "NR_CG-ConfigInfo-v1590-IEs.c"
  "NR_CG-ConfigInfo-v1590-IEs.h"
  "NR_CG-ConfigInfo-v1610-IEs.c"
  "NR_CG-ConfigInfo-v1610-IEs.h"
  "NR_CG-ConfigInfo-v1620-IEs.c"
  "NR_CG-ConfigInfo-v1620-IEs.h"
  "NR_CG-ConfigInfo-v1640-IEs.c"
  "NR_CG-ConfigInfo-v1640-IEs.h"
  "NR_CG-ConfigInfo-v1700-IEs.c"
  "NR_CG-ConfigInfo-v1700-IEs.h"
  "NR_CG-ConfigInfo-v1730-IEs.c"
  "NR_CG-ConfigInfo-v1730-IEs.h"
  "NR_CG-ConfigInfo.c"
  "NR_CG-ConfigInfo.h"
  "NR_CG-SDT-ConfigLCH-Restriction-r17.c"
  "NR_CG-SDT-ConfigLCH-Restriction-r17.h"
  "NR_CG-SDT-Configuration-r17.c"
  "NR_CG-SDT-Configuration-r17.h"
  "NR_CG-SDT-TA-ValidationConfig-r17.c"
  "NR_CG-SDT-TA-ValidationConfig-r17.h"
  "NR_CG-StartingOffsets-r16.c"
  "NR_CG-StartingOffsets-r16.h"
  "NR_CG-UCI-OnPUSCH.c"
  "NR_CG-UCI-OnPUSCH.h"
  "NR_CGI-Info-Logging-r16.c"
  "NR_CGI-Info-Logging-r16.h"
  "NR_CGI-InfoEUTRA.c"
  "NR_CGI-InfoEUTRA.h"
  "NR_CGI-InfoEUTRALogging.c"
  "NR_CGI-InfoEUTRALogging.h"
  "NR_CGI-InfoNR.c"
  "NR_CGI-InfoNR.h"
  "NR_CI-ConfigurationPerServingCell-r16.c"
  "NR_CI-ConfigurationPerServingCell-r16.h"
  "NR_CLI-EventTriggerConfig-r16.c"
  "NR_CLI-EventTriggerConfig-r16.h"
  "NR_CLI-PeriodicalReportConfig-r16.c"
  "NR_CLI-PeriodicalReportConfig-r16.h"
  "NR_CLI-RSSI-Range-r16.c"
  "NR_CLI-RSSI-Range-r16.h"
  "NR_CLI-RSSI-TriggeredList-r16.c"
  "NR_CLI-RSSI-TriggeredList-r16.h"
  "NR_CLI-ResourceConfig-r16.c"
  "NR_CLI-ResourceConfig-r16.h"
  "NR_CLI-TriggeredList-r16.c"
  "NR_CLI-TriggeredList-r16.h"
  "NR_CMRGroupingAndPairing-r17.c"
  "NR_CMRGroupingAndPairing-r17.h"
  "NR_CO-Duration-r16.c"
  "NR_CO-Duration-r16.h"
  "NR_CO-Duration-r17.c"
  "NR_CO-Duration-r17.h"
  "NR_CO-DurationsPerCell-r16.c"
  "NR_CO-DurationsPerCell-r16.h"
  "NR_CO-DurationsPerCell-r17.c"
  "NR_CO-DurationsPerCell-r17.h"
  "NR_CRS-InterfMitigation-r17.c"
  "NR_CRS-InterfMitigation-r17.h"
  "NR_CSI-AperiodicTriggerState.c"
  "NR_CSI-AperiodicTriggerState.h"
  "NR_CSI-AperiodicTriggerStateList.c"
  "NR_CSI-AperiodicTriggerStateList.h"
  "NR_CSI-AssociatedReportConfigInfo.c"
  "NR_CSI-AssociatedReportConfigInfo.h"
  "NR_CSI-FrequencyOccupation.c"
  "NR_CSI-FrequencyOccupation.h"
  "NR_CSI-IM-Resource.c"
  "NR_CSI-IM-Resource.h"
  "NR_CSI-IM-ResourceId.c"
  "NR_CSI-IM-ResourceId.h"
  "NR_CSI-IM-ResourceSet.c"
  "NR_CSI-IM-ResourceSet.h"
  "NR_CSI-IM-ResourceSetId.c"
  "NR_CSI-IM-ResourceSetId.h"
  "NR_CSI-MeasConfig.c"
  "NR_CSI-MeasConfig.h"
  "NR_CSI-MultiTRP-SupportedCombinations-r17.c"
  "NR_CSI-MultiTRP-SupportedCombinations-r17.h"
  "NR_CSI-RS-CellMobility.c"
  "NR_CSI-RS-CellMobility.h"
  "NR_CSI-RS-ForTracking.c"
  "NR_CSI-RS-ForTracking.h"
  "NR_CSI-RS-IM-ReceptionForFeedback.c"
  "NR_CSI-RS-IM-ReceptionForFeedback.h"
  "NR_CSI-RS-Index.c"
  "NR_CSI-RS-Index.h"
  "NR_CSI-RS-ProcFrameworkForSRS.c"
  "NR_CSI-RS-ProcFrameworkForSRS.h"
  "NR_CSI-RS-Resource-Mobility.c"
  "NR_CSI-RS-Resource-Mobility.h"
  "NR_CSI-RS-ResourceConfigMobility.c"
  "NR_CSI-RS-ResourceConfigMobility.h"
  "NR_CSI-RS-ResourceMapping.c"
  "NR_CSI-RS-ResourceMapping.h"
  "NR_CSI-ReportConfig.c"
  "NR_CSI-ReportConfig.h"
  "NR_CSI-ReportConfigId.c"
  "NR_CSI-ReportConfigId.h"
  "NR_CSI-ReportFramework.c"
  "NR_CSI-ReportFramework.h"
  "NR_CSI-ReportFrameworkExt-r16.c"
  "NR_CSI-ReportFrameworkExt-r16.h"
  "NR_CSI-ReportPeriodicityAndOffset.c"
  "NR_CSI-ReportPeriodicityAndOffset.h"
  "NR_CSI-ResourceConfig.c"
  "NR_CSI-ResourceConfig.h"
  "NR_CSI-ResourceConfigId.c"
  "NR_CSI-ResourceConfigId.h"
  "NR_CSI-ResourcePeriodicityAndOffset.c"
  "NR_CSI-ResourcePeriodicityAndOffset.h"
  "NR_CSI-SSB-ResourceSet.c"
  "NR_CSI-SSB-ResourceSet.h"
  "NR_CSI-SSB-ResourceSetId.c"
  "NR_CSI-SSB-ResourceSetId.h"
  "NR_CSI-SemiPersistentOnPUSCH-TriggerState.c"
  "NR_CSI-SemiPersistentOnPUSCH-TriggerState.h"
  "NR_CSI-SemiPersistentOnPUSCH-TriggerStateList.c"
  "NR_CSI-SemiPersistentOnPUSCH-TriggerStateList.h"
  "NR_CandidateBeamRS-r16.c"
  "NR_CandidateBeamRS-r16.h"
  "NR_CandidateBeamRSListExt-r16.c"
  "NR_CandidateBeamRSListExt-r16.h"
  "NR_CandidateCell-r17.c"
  "NR_CandidateCell-r17.h"
  "NR_CandidateCellCPC-r17.c"
  "NR_CandidateCellCPC-r17.h"
  "NR_CandidateCellInfo-r17.c"
  "NR_CandidateCellInfo-r17.h"
  "NR_CandidateCellInfoListCPC-r17.c"
  "NR_CandidateCellInfoListCPC-r17.h"
  "NR_CandidateCellListCPC-r17.c"
  "NR_CandidateCellListCPC-r17.h"
  "NR_CandidateServingFreqListEUTRA.c"
  "NR_CandidateServingFreqListEUTRA.h"
  "NR_CandidateServingFreqListNR-r16.c"
  "NR_CandidateServingFreqListNR-r16.h"
  "NR_CandidateServingFreqListNR.c"
  "NR_CandidateServingFreqListNR.h"
  "NR_CarrierAggregationVariant.c"
  "NR_CarrierAggregationVariant.h"
  "NR_CarrierFreqEUTRA-v1610.c"
  "NR_CarrierFreqEUTRA-v1610.h"
  "NR_CarrierFreqEUTRA-v1700.c"
  "NR_CarrierFreqEUTRA-v1700.h"
  "NR_CarrierFreqEUTRA.c"
  "NR_CarrierFreqEUTRA.h"
  "NR_CarrierFreqListEUTRA-v1610.c"
  "NR_CarrierFreqListEUTRA-v1610.h"
  "NR_CarrierFreqListEUTRA-v1700.c"
  "NR_CarrierFreqListEUTRA-v1700.h"
  "NR_CarrierFreqListEUTRA.c"
  "NR_CarrierFreqListEUTRA.h"
  "NR_CarrierFreqListMBS-r17.c"
  "NR_CarrierFreqListMBS-r17.h"
  "NR_CarrierInfoNR.c"
  "NR_CarrierInfoNR.h"
  "NR_CarrierState-r17.c"
  "NR_CarrierState-r17.h"
  "NR_CarrierTypePair-r16.c"
  "NR_CarrierTypePair-r16.h"
  "NR_CellAccessRelatedInfo-EUTRA-5GC.c"
  "NR_CellAccessRelatedInfo-EUTRA-5GC.h"
  "NR_CellAccessRelatedInfo-EUTRA-EPC.c"
  "NR_CellAccessRelatedInfo-EUTRA-EPC.h"
  "NR_CellAccessRelatedInfo.c"
  "NR_CellAccessRelatedInfo.h"
  "NR_CellGlobalIdList-r16.c"
  "NR_CellGlobalIdList-r16.h"
  "NR_CellGroupConfig.c"
  "NR_CellGroupConfig.h"
  "NR_CellGroupForSwitch-r16.c"
  "NR_CellGroupForSwitch-r16.h"
  "NR_CellGroupId.c"
  "NR_CellGroupId.h"
  "NR_CellGrouping-r16.c"
  "NR_CellGrouping-r16.h"
  "NR_CellIdentity-EUTRA-5GC.c"
  "NR_CellIdentity-EUTRA-5GC.h"
  "NR_CellIdentity.c"
  "NR_CellIdentity.h"
  "NR_CellListEUTRA-r16.c"
  "NR_CellListEUTRA-r16.h"
  "NR_CellListNR-r16.c"
  "NR_CellListNR-r16.h"
  "NR_CellReselectionPriorities.c"
  "NR_CellReselectionPriorities.h"
  "NR_CellReselectionPriority.c"
  "NR_CellReselectionPriority.h"
  "NR_CellReselectionSubPriority.c"
  "NR_CellReselectionSubPriority.h"
  "NR_CellsToAddMod.c"
  "NR_CellsToAddMod.h"
  "NR_CellsToAddModExt-v1710.c"
  "NR_CellsToAddModExt-v1710.h"
  "NR_CellsToAddModList.c"
  "NR_CellsToAddModList.h"
  "NR_CellsToAddModListExt-v1710.c"
  "NR_CellsToAddModListExt-v1710.h"
  "NR_CellsToAddModListUTRA-FDD-r16.c"
  "NR_CellsToAddModListUTRA-FDD-r16.h"
  "NR_CellsToAddModUTRA-FDD-r16.c"
  "NR_CellsToAddModUTRA-FDD-r16.h"
  "NR_CellsTriggeredList.c"
  "NR_CellsTriggeredList.h"
  "NR_ChannelAccessConfig-r16.c"
  "NR_ChannelAccessConfig-r16.h"
  "NR_ChoCandidateCell-r17.c"
  "NR_ChoCandidateCell-r17.h"
  "NR_ChoCandidateCellList-r17.c"
  "NR_ChoCandidateCellList-r17.h"
  "NR_CipheringAlgorithm.c"
  "NR_CipheringAlgorithm.h"
  "NR_CodebookComboParameterMixedType-r17.c"
  "NR_CodebookComboParameterMixedType-r17.h"
  "NR_CodebookComboParameterMixedTypePerBC-r17.c"
  "NR_CodebookComboParameterMixedTypePerBC-r17.h"
  "NR_CodebookComboParameterMultiTRP-PerBC-r17.c"
  "NR_CodebookComboParameterMultiTRP-PerBC-r17.h"
  "NR_CodebookComboParameterMultiTRP-r17.c"
  "NR_CodebookComboParameterMultiTRP-r17.h"
  "NR_CodebookComboParametersAddition-r16.c"
  "NR_CodebookComboParametersAddition-r16.h"
  "NR_CodebookComboParametersAdditionPerBC-r16.c"
  "NR_CodebookComboParametersAdditionPerBC-r16.h"
  "NR_CodebookConfig-r16.c"
  "NR_CodebookConfig-r16.h"
  "NR_CodebookConfig-r17.c"
  "NR_CodebookConfig-r17.h"
  "NR_CodebookConfig-v1730.c"
  "NR_CodebookConfig-v1730.h"
  "NR_CodebookConfig.c"
  "NR_CodebookConfig.h"
  "NR_CodebookParameters-v1610.c"
  "NR_CodebookParameters-v1610.h"
  "NR_CodebookParameters.c"
  "NR_CodebookParameters.h"
  "NR_CodebookParametersAddition-r16.c"
  "NR_CodebookParametersAddition-r16.h"
  "NR_CodebookParametersAdditionPerBC-r16.c"
  "NR_CodebookParametersAdditionPerBC-r16.h"
  "NR_CodebookParametersfetype2-r17.c"
  "NR_CodebookParametersfetype2-r17.h"
  "NR_CodebookParametersfetype2PerBC-r17.c"
  "NR_CodebookParametersfetype2PerBC-r17.h"
  "NR_CodebookVariantsList-r16.c"
  "NR_CodebookVariantsList-r16.h"
  "NR_CommonLocationInfo-r16.c"
  "NR_CommonLocationInfo-r16.h"
  "NR_CondReconfigExecCondSCG-r17.c"
  "NR_CondReconfigExecCondSCG-r17.h"
  "NR_CondReconfigId-r16.c"
  "NR_CondReconfigId-r16.h"
  "NR_CondReconfigToAddMod-r16.c"
  "NR_CondReconfigToAddMod-r16.h"
  "NR_CondReconfigToAddModList-r16.c"
  "NR_CondReconfigToAddModList-r16.h"
  "NR_CondReconfigToRemoveList-r16.c"
  "NR_CondReconfigToRemoveList-r16.h"
  "NR_CondTriggerConfig-r16.c"
  "NR_CondTriggerConfig-r16.h"
  "NR_ConditionalReconfiguration-r16.c"
  "NR_ConditionalReconfiguration-r16.h"
  "NR_ConfigRestrictInfoDAPS-r16.c"
  "NR_ConfigRestrictInfoDAPS-r16.h"
  "NR_ConfigRestrictInfoDAPS-v1640.c"
  "NR_ConfigRestrictInfoDAPS-v1640.h"
  "NR_ConfigRestrictInfoSCG.c"
  "NR_ConfigRestrictInfoSCG.h"
  "NR_ConfigRestrictModReqSCG.c"
  "NR_ConfigRestrictModReqSCG.h"
  "NR_ConfiguredGrantConfig.c"
  "NR_ConfiguredGrantConfig.h"
  "NR_ConfiguredGrantConfigIndex-r16.c"
  "NR_ConfiguredGrantConfigIndex-r16.h"
  "NR_ConfiguredGrantConfigIndexMAC-r16.c"
  "NR_ConfiguredGrantConfigIndexMAC-r16.h"
  "NR_ConfiguredGrantConfigToAddModList-r16.c"
  "NR_ConfiguredGrantConfigToAddModList-r16.h"
  "NR_ConfiguredGrantConfigToReleaseList-r16.c"
  "NR_ConfiguredGrantConfigToReleaseList-r16.h"
  "NR_ConfiguredGrantConfigType2DeactivationState-r16.c"
  "NR_ConfiguredGrantConfigType2DeactivationState-r16.h"
  "NR_ConfiguredGrantConfigType2DeactivationStateList-r16.c"
  "NR_ConfiguredGrantConfigType2DeactivationStateList-r16.h"
  "NR_ConnEstFailReport-r16.c"
  "NR_ConnEstFailReport-r16.h"
  "NR_ConnEstFailReportList-r17.c"
  "NR_ConnEstFailReportList-r17.h"
  "NR_ConnEstFailureControl.c"
  "NR_ConnEstFailureControl.h"
  "NR_ControlResourceSet.c"
  "NR_ControlResourceSet.h"
  "NR_ControlResourceSetId-r16.c"
  "NR_ControlResourceSetId-r16.h"
  "NR_ControlResourceSetId-v1610.c"
  "NR_ControlResourceSetId-v1610.h"
  "NR_ControlResourceSetId.c"
  "NR_ControlResourceSetId.h"
  "NR_ControlResourceSetZero.c"
  "NR_ControlResourceSetZero.h"
  "NR_CounterCheck-IEs.c"
  "NR_CounterCheck-IEs.h"
  "NR_CounterCheck.c"
  "NR_CounterCheck.h"
  "NR_CounterCheckResponse-IEs.c"
  "NR_CounterCheckResponse-IEs.h"
  "NR_CounterCheckResponse.c"
  "NR_CounterCheckResponse.h"
  "NR_CrossCarrierSchedulingConfig.c"
  "NR_CrossCarrierSchedulingConfig.h"
  "NR_CrossCarrierSchedulingSCell-SpCell-r17.c"
  "NR_CrossCarrierSchedulingSCell-SpCell-r17.h"
  "NR_DAPS-UplinkPowerConfig-r16.c"
  "NR_DAPS-UplinkPowerConfig-r16.h"
  "NR_DCP-Config-r16.c"
  "NR_DCP-Config-r16.h"
  "NR_DL-AM-RLC-v1610.c"
  "NR_DL-AM-RLC-v1610.h"
  "NR_DL-AM-RLC-v1700.c"
  "NR_DL-AM-RLC-v1700.h"
  "NR_DL-AM-RLC.c"
  "NR_DL-AM-RLC.h"
  "NR_DL-CCCH-Message.c"
  "NR_DL-CCCH-Message.h"
  "NR_DL-CCCH-MessageType.c"
  "NR_DL-CCCH-MessageType.h"
  "NR_DL-DCCH-Message.c"
  "NR_DL-DCCH-Message.h"
  "NR_DL-DCCH-MessageType.c"
  "NR_DL-DCCH-MessageType.h"
  "NR_DL-DataToUL-ACK-DCI-1-2-r16.c"
  "NR_DL-DataToUL-ACK-DCI-1-2-r16.h"
  "NR_DL-DataToUL-ACK-DCI-1-2-r17.c"
  "NR_DL-DataToUL-ACK-DCI-1-2-r17.h"
  "NR_DL-DataToUL-ACK-MulticastDCI-Format4-1-r17.c"
  "NR_DL-DataToUL-ACK-MulticastDCI-Format4-1-r17.h"
  "NR_DL-DataToUL-ACK-r16.c"
  "NR_DL-DataToUL-ACK-r16.h"
  "NR_DL-DataToUL-ACK-r17.c"
  "NR_DL-DataToUL-ACK-r17.h"
  "NR_DL-DataToUL-ACK-v1700.c"
  "NR_DL-DataToUL-ACK-v1700.h"
  "NR_DL-PPW-ID-r17.c"
  "NR_DL-PPW-ID-r17.h"
  "NR_DL-PPW-PeriodicityAndStartSlot-r17.c"
  "NR_DL-PPW-PeriodicityAndStartSlot-r17.h"
  "NR_DL-PPW-PreConfig-r17.c"
  "NR_DL-PPW-PreConfig-r17.h"
  "NR_DL-PPW-PreConfigToAddModList-r17.c"
  "NR_DL-PPW-PreConfigToAddModList-r17.h"
  "NR_DL-PPW-PreConfigToReleaseList-r17.c"
  "NR_DL-PPW-PreConfigToReleaseList-r17.h"
  "NR_DL-PRS-Info-r16.c"
  "NR_DL-PRS-Info-r16.h"
  "NR_DL-PRS-QCL-Info-r17.c"
  "NR_DL-PRS-QCL-Info-r17.h"
  "NR_DL-UM-RLC-v1700.c"
  "NR_DL-UM-RLC-v1700.h"
  "NR_DL-UM-RLC.c"
  "NR_DL-UM-RLC.h"
  "NR_DLDedicatedMessageSegment-r16-IEs.c"
  "NR_DLDedicatedMessageSegment-r16-IEs.h"
  "NR_DLDedicatedMessageSegment-r16.c"
  "NR_DLDedicatedMessageSegment-r16.h"
  "NR_DLInformationTransfer-IEs.c"
  "NR_DLInformationTransfer-IEs.h"
  "NR_DLInformationTransfer-v1610-IEs.c"
  "NR_DLInformationTransfer-v1610-IEs.h"
  "NR_DLInformationTransfer-v1700-IEs.c"
  "NR_DLInformationTransfer-v1700-IEs.h"
  "NR_DLInformationTransfer.c"
  "NR_DLInformationTransfer.h"
  "NR_DLInformationTransferMRDC-r16-IEs.c"
  "NR_DLInformationTransferMRDC-r16-IEs.h"
  "NR_DLInformationTransferMRDC-r16.c"
  "NR_DLInformationTransferMRDC-r16.h"
  "NR_DMRS-BundlingPUCCH-Config-r17.c"
  "NR_DMRS-BundlingPUCCH-Config-r17.h"
  "NR_DMRS-BundlingPUSCH-Config-r17.c"
  "NR_DMRS-BundlingPUSCH-Config-r17.h"
  "NR_DMRS-DownlinkConfig.c"
  "NR_DMRS-DownlinkConfig.h"
  "NR_DMRS-UplinkConfig.c"
  "NR_DMRS-UplinkConfig.h"
  "NR_DMRS-UplinkTransformPrecoding-r16.c"
  "NR_DMRS-UplinkTransformPrecoding-r16.h"
  "NR_DRB-CountInfo.c"
  "NR_DRB-CountInfo.h"
  "NR_DRB-CountInfoList.c"
  "NR_DRB-CountInfoList.h"
  "NR_DRB-CountMSB-Info.c"
  "NR_DRB-CountMSB-Info.h"
  "NR_DRB-CountMSB-InfoList.c"
  "NR_DRB-CountMSB-InfoList.h"
  "NR_DRB-Identity.c"
  "NR_DRB-Identity.h"
  "NR_DRB-ToAddMod.c"
  "NR_DRB-ToAddMod.h"
  "NR_DRB-ToAddModList.c"
  "NR_DRB-ToAddModList.h"
  "NR_DRB-ToReleaseList.c"
  "NR_DRB-ToReleaseList.h"
  "NR_DRX-Config.c"
  "NR_DRX-Config.h"
  "NR_DRX-ConfigExt-v1700.c"
  "NR_DRX-ConfigExt-v1700.h"
  "NR_DRX-ConfigPTM-Index-r17.c"
  "NR_DRX-ConfigPTM-Index-r17.h"
  "NR_DRX-ConfigPTM-r17.c"
  "NR_DRX-ConfigPTM-r17.h"
  "NR_DRX-ConfigSL-r17.c"
  "NR_DRX-ConfigSL-r17.h"
  "NR_DRX-ConfigSecondaryGroup-r16.c"
  "NR_DRX-ConfigSecondaryGroup-r16.h"
  "NR_DRX-Info.c"
  "NR_DRX-Info.h"
  "NR_DRX-Info2.c"
  "NR_DRX-Info2.h"
  "NR_DRX-Preference-r16.c"
  "NR_DRX-Preference-r16.h"
  "NR_DRX-PreferenceConfig-r16.c"
  "NR_DRX-PreferenceConfig-r16.h"
  "NR_DataInactivityTimer.c"
  "NR_DataInactivityTimer.h"
  "NR_DeactivatedSCG-Config-r17.c"
  "NR_DeactivatedSCG-Config-r17.h"
  "NR_DedicatedInfoF1c-r17.c"
  "NR_DedicatedInfoF1c-r17.h"
  "NR_DedicatedNAS-Message.c"
  "NR_DedicatedNAS-Message.h"
  "NR_DedicatedSIBRequest-r16-IEs.c"
  "NR_DedicatedSIBRequest-r16-IEs.h"
  "NR_DedicatedSIBRequest-r16.c"
  "NR_DedicatedSIBRequest-r16.h"
  "NR_DefaultDC-Location-r17.c"
  "NR_DefaultDC-Location-r17.h"
  "NR_DelayBudgetReport.c"
  "NR_DelayBudgetReport.h"
  "NR_DiscardTimerExt-r16.c"
  "NR_DiscardTimerExt-r16.h"
  "NR_DiscardTimerExt2-r17.c"
  "NR_DiscardTimerExt2-r17.h"
  "NR_DormancyGroupID-r16.c"
  "NR_DormancyGroupID-r16.h"
  "NR_DormantBWP-Config-r16.c"
  "NR_DormantBWP-Config-r16.h"
  "NR_DownlinkConfigCommon.c"
  "NR_DownlinkConfigCommon.h"
  "NR_DownlinkConfigCommonSIB.c"
  "NR_DownlinkConfigCommonSIB.h"
  "NR_DownlinkHARQ-FeedbackDisabled-r17.c"
  "NR_DownlinkHARQ-FeedbackDisabled-r17.h"
  "NR_DownlinkPreemption.c"
  "NR_DownlinkPreemption.h"
  "NR_Dummy-TDRA-List.c"
  "NR_Dummy-TDRA-List.h"
  "NR_DummyA.c"
  "NR_DummyA.h"
  "NR_DummyB.c"
  "NR_DummyB.h"
  "NR_DummyC.c"
  "NR_DummyC.h"
  "NR_DummyD.c"
  "NR_DummyD.h"
  "NR_DummyE.c"
  "NR_DummyE.h"
  "NR_DummyF.c"
  "NR_DummyF.h"
  "NR_DummyG.c"
  "NR_DummyG.h"
  "NR_DummyH.c"
  "NR_DummyH.h"
  "NR_DummyI.c"
  "NR_DummyI.h"
  "NR_DummyJ.c"
  "NR_DummyJ.h"
  "NR_DummyPathlossReferenceRS-v1710.c"
  "NR_DummyPathlossReferenceRS-v1710.h"
  "NR_EUTRA-AllowedMeasBandwidth.c"
  "NR_EUTRA-AllowedMeasBandwidth.h"
  "NR_EUTRA-Cell.c"
  "NR_EUTRA-Cell.h"
  "NR_EUTRA-CellIndex.c"
  "NR_EUTRA-CellIndex.h"
  "NR_EUTRA-CellIndexList.c"
  "NR_EUTRA-CellIndexList.h"
  "NR_EUTRA-ExcludedCell.c"
  "NR_EUTRA-ExcludedCell.h"
  "NR_EUTRA-FreqExcludedCellList.c"
  "NR_EUTRA-FreqExcludedCellList.h"
  "NR_EUTRA-FreqNeighCellInfo.c"
  "NR_EUTRA-FreqNeighCellInfo.h"
  "NR_EUTRA-FreqNeighCellList.c"
  "NR_EUTRA-FreqNeighCellList.h"
  "NR_EUTRA-FreqNeighHSDN-CellList-r17.c"
  "NR_EUTRA-FreqNeighHSDN-CellList-r17.h"
  "NR_EUTRA-MBSFN-SubframeConfig.c"
  "NR_EUTRA-MBSFN-SubframeConfig.h"
  "NR_EUTRA-MBSFN-SubframeConfigList.c"
  "NR_EUTRA-MBSFN-SubframeConfigList.h"
  "NR_EUTRA-MultiBandInfo.c"
  "NR_EUTRA-MultiBandInfo.h"
  "NR_EUTRA-MultiBandInfoList.c"
  "NR_EUTRA-MultiBandInfoList.h"
  "NR_EUTRA-NS-PmaxList.c"
  "NR_EUTRA-NS-PmaxList.h"
  "NR_EUTRA-NS-PmaxValue.c"
  "NR_EUTRA-NS-PmaxValue.h"
  "NR_EUTRA-Parameters.c"
  "NR_EUTRA-Parameters.h"
  "NR_EUTRA-ParametersCommon.c"
  "NR_EUTRA-ParametersCommon.h"
  "NR_EUTRA-ParametersXDD-Diff.c"
  "NR_EUTRA-ParametersXDD-Diff.h"
  "NR_EUTRA-PhysCellId.c"
  "NR_EUTRA-PhysCellId.h"
  "NR_EUTRA-PhysCellIdRange.c"
  "NR_EUTRA-PhysCellIdRange.h"
  "NR_EUTRA-PresenceAntennaPort1.c"
  "NR_EUTRA-PresenceAntennaPort1.h"
  "NR_EUTRA-Q-OffsetRange.c"
  "NR_EUTRA-Q-OffsetRange.h"
  "NR_EUTRA-RSTD-Info.c"
  "NR_EUTRA-RSTD-Info.h"
  "NR_EUTRA-RSTD-InfoList.c"
  "NR_EUTRA-RSTD-InfoList.h"
  "NR_EXTERNAL.c"
  "NR_EXTERNAL.h"
  "NR_EphemerisInfo-r17.c"
  "NR_EphemerisInfo-r17.h"
  "NR_EpochTime-r17.c"
  "NR_EpochTime-r17.h"
  "NR_EstablishmentCause.c"
  "NR_EstablishmentCause.h"
  "NR_EthernetHeaderCompression-r16.c"
  "NR_EthernetHeaderCompression-r16.h"
  "NR_EventTriggerConfig.c"
  "NR_EventTriggerConfig.h"
  "NR_EventTriggerConfigInterRAT.c"
  "NR_EventTriggerConfigInterRAT.h"
  "NR_EventTriggerConfigNR-SL-r16.c"
  "NR_EventTriggerConfigNR-SL-r16.h"
  "NR_EventType-r16.c"
  "NR_EventType-r16.h"
  "NR_ExcessDelay-DRB-IdentityInfo-r17.c"
  "NR_ExcessDelay-DRB-IdentityInfo-r17.h"
  "NR_ExtendedPagingCycle-r17.c"
  "NR_ExtendedPagingCycle-r17.h"
  "NR_FDM-TDM-r16.c"
  "NR_FDM-TDM-r16.h"
  "NR_FR-Info.c"
  "NR_FR-Info.h"
  "NR_FR-InfoList.c"
  "NR_FR-InfoList.h"
  "NR_FR2-2-AccessParamsPerBand-r17.c"
  "NR_FR2-2-AccessParamsPerBand-r17.h"
  "NR_FailureInfoDAPS-r16.c"
  "NR_FailureInfoDAPS-r16.h"
  "NR_FailureInfoRLC-Bearer.c"
  "NR_FailureInfoRLC-Bearer.h"
  "NR_FailureInformation-IEs.c"
  "NR_FailureInformation-IEs.h"
  "NR_FailureInformation-v1610-IEs.c"
  "NR_FailureInformation-v1610-IEs.h"
  "NR_FailureInformation.c"
  "NR_FailureInformation.h"
  "NR_FailureReportMCG-r16.c"
  "NR_FailureReportMCG-r16.h"
  "NR_FailureReportSCG-EUTRA.c"
  "NR_FailureReportSCG-EUTRA.h"
  "NR_FailureReportSCG.c"
  "NR_FailureReportSCG.h"
  "NR_FeatureCombination-r17.c"
  "NR_FeatureCombination-r17.h"
  "NR_FeatureCombinationPreambles-r17.c"
  "NR_FeatureCombinationPreambles-r17.h"
  "NR_FeaturePriority-r17.c"
  "NR_FeaturePriority-r17.h"
  "NR_FeatureSet.c"
  "NR_FeatureSet.h"
  "NR_FeatureSetCombination.c"
  "NR_FeatureSetCombination.h"
  "NR_FeatureSetCombinationId.c"
  "NR_FeatureSetCombinationId.h"
  "NR_FeatureSetDownlink-v1540.c"
  "NR_FeatureSetDownlink-v1540.h"
  "NR_FeatureSetDownlink-v15a0.c"
  "NR_FeatureSetDownlink-v15a0.h"
  "NR_FeatureSetDownlink-v1610.c"
  "NR_FeatureSetDownlink-v1610.h"
  "NR_FeatureSetDownlink-v1700.c"
  "NR_FeatureSetDownlink-v1700.h"
  "NR_FeatureSetDownlink-v1720.c"
  "NR_FeatureSetDownlink-v1720.h"
  "NR_FeatureSetDownlink-v1730.c"
  "NR_FeatureSetDownlink-v1730.h"
  "NR_FeatureSetDownlink.c"
  "NR_FeatureSetDownlink.h"
  "NR_FeatureSetDownlinkId.c"
  "NR_FeatureSetDownlinkId.h"
  "NR_FeatureSetDownlinkPerCC-Id.c"
  "NR_FeatureSetDownlinkPerCC-Id.h"
  "NR_FeatureSetDownlinkPerCC-v1620.c"
  "NR_FeatureSetDownlinkPerCC-v1620.h"
  "NR_FeatureSetDownlinkPerCC-v1700.c"
  "NR_FeatureSetDownlinkPerCC-v1700.h"
  "NR_FeatureSetDownlinkPerCC-v1720.c"
  "NR_FeatureSetDownlinkPerCC-v1720.h"
  "NR_FeatureSetDownlinkPerCC-v1730.c"
  "NR_FeatureSetDownlinkPerCC-v1730.h"
  "NR_FeatureSetDownlinkPerCC.c"
  "NR_FeatureSetDownlinkPerCC.h"
  "NR_FeatureSetEUTRA-DownlinkId.c"
  "NR_FeatureSetEUTRA-DownlinkId.h"
  "NR_FeatureSetEUTRA-UplinkId.c"
  "NR_FeatureSetEUTRA-UplinkId.h"
  "NR_FeatureSetEntryIndex.c"
  "NR_FeatureSetEntryIndex.h"
  "NR_FeatureSetUplink-v1540.c"
  "NR_FeatureSetUplink-v1540.h"
  "NR_FeatureSetUplink-v1610.c"
  "NR_FeatureSetUplink-v1610.h"
  "NR_FeatureSetUplink-v1630.c"
  "NR_FeatureSetUplink-v1630.h"
  "NR_FeatureSetUplink-v1640.c"
  "NR_FeatureSetUplink-v1640.h"
  "NR_FeatureSetUplink-v1710.c"
  "NR_FeatureSetUplink-v1710.h"
  "NR_FeatureSetUplink-v1720.c"
  "NR_FeatureSetUplink-v1720.h"
  "NR_FeatureSetUplink.c"
  "NR_FeatureSetUplink.h"
  "NR_FeatureSetUplinkId.c"
  "NR_FeatureSetUplinkId.h"
  "NR_FeatureSetUplinkPerCC-Id.c"
  "NR_FeatureSetUplinkPerCC-Id.h"
  "NR_FeatureSetUplinkPerCC-v1540.c"
  "NR_FeatureSetUplinkPerCC-v1540.h"
  "NR_FeatureSetUplinkPerCC-v1700.c"
  "NR_FeatureSetUplinkPerCC-v1700.h"
  "NR_FeatureSetUplinkPerCC.c"
  "NR_FeatureSetUplinkPerCC.h"
  "NR_FeatureSets.c"
  "NR_FeatureSets.h"
  "NR_FeatureSetsPerBand.c"
  "NR_FeatureSetsPerBand.h"
  "NR_FilterCoefficient.c"
  "NR_FilterCoefficient.h"
  "NR_FilterConfig.c"
  "NR_FilterConfig.h"
  "NR_FilterConfigCLI-r16.c"
  "NR_FilterConfigCLI-r16.h"
  "NR_FreqBandIndicatorEUTRA.c"
  "NR_FreqBandIndicatorEUTRA.h"
  "NR_FreqBandIndicatorNR.c"
  "NR_FreqBandIndicatorNR.h"
  "NR_FreqBandInformation.c"
  "NR_FreqBandInformation.h"
  "NR_FreqBandInformationEUTRA.c"
  "NR_FreqBandInformationEUTRA.h"
  "NR_FreqBandInformationNR.c"
  "NR_FreqBandInformationNR.h"
  "NR_FreqBandList.c"
  "NR_FreqBandList.h"
  "NR_FreqPriorityDedicatedSlicing-r17.c"
  "NR_FreqPriorityDedicatedSlicing-r17.h"
  "NR_FreqPriorityEUTRA.c"
  "NR_FreqPriorityEUTRA.h"
  "NR_FreqPriorityListDedicatedSlicing-r17.c"
  "NR_FreqPriorityListDedicatedSlicing-r17.h"
  "NR_FreqPriorityListEUTRA.c"
  "NR_FreqPriorityListEUTRA.h"
  "NR_FreqPriorityListNR.c"
  "NR_FreqPriorityListNR.h"
  "NR_FreqPriorityListSlicing-r17.c"
  "NR_FreqPriorityListSlicing-r17.h"
  "NR_FreqPriorityNR.c"
  "NR_FreqPriorityNR.h"
  "NR_FreqPrioritySlicing-r17.c"
  "NR_FreqPrioritySlicing-r17.h"
  "NR_FreqSeparationClass.c"
  "NR_FreqSeparationClass.h"
  "NR_FreqSeparationClassDL-Only-r16.c"
  "NR_FreqSeparationClassDL-Only-r16.h"
  "NR_FreqSeparationClassDL-v1620.c"
  "NR_FreqSeparationClassDL-v1620.h"
  "NR_FreqSeparationClassUL-v1620.c"
  "NR_FreqSeparationClassUL-v1620.h"
  "NR_FrequencyComponent-r17.c"
  "NR_FrequencyComponent-r17.h"
  "NR_FrequencyConfig-NR-r16.c"
  "NR_FrequencyConfig-NR-r16.h"
  "NR_FrequencyHoppingOffsetListsDCI-0-2-r16.c"
  "NR_FrequencyHoppingOffsetListsDCI-0-2-r16.h"
  "NR_FrequencyInfoDL-SIB.c"
  "NR_FrequencyInfoDL-SIB.h"
  "NR_FrequencyInfoDL.c"
  "NR_FrequencyInfoDL.h"
  "NR_FrequencyInfoUL-SIB.c"
  "NR_FrequencyInfoUL-SIB.h"
  "NR_FrequencyInfoUL.c"
  "NR_FrequencyInfoUL.h"
  "NR_GIN-Element-r17.c"
  "NR_GIN-Element-r17.h"
  "NR_GINs-PerSNPN-r17.c"
  "NR_GINs-PerSNPN-r17.h"
  "NR_GNSS-ID-r16.c"
  "NR_GNSS-ID-r16.h"
  "NR_GapConfig-r17.c"
  "NR_GapConfig-r17.h"
  "NR_GapConfig.c"
  "NR_GapConfig.h"
  "NR_GapPriority-r17.c"
  "NR_GapPriority-r17.h"
  "NR_GeneralParametersMRDC-XDD-Diff.c"
  "NR_GeneralParametersMRDC-XDD-Diff.h"
  "NR_GeneralParametersMRDC-v1610.c"
  "NR_GeneralParametersMRDC-v1610.h"
  "NR_GoodServingCellEvaluation-r17.c"
  "NR_GoodServingCellEvaluation-r17.h"
  "NR_GroupB-ConfiguredTwoStepRA-r16.c"
  "NR_GroupB-ConfiguredTwoStepRA-r16.h"
  "NR_GuardBand-r16.c"
  "NR_GuardBand-r16.h"
  "NR_HRNN-List-r16.c"
  "NR_HRNN-List-r16.h"
  "NR_HRNN-r16.c"
  "NR_HRNN-r16.h"
  "NR_HandoverCommand-IEs.c"
  "NR_HandoverCommand-IEs.h"
  "NR_HandoverCommand.c"
  "NR_HandoverCommand.h"
  "NR_HandoverPreparationInformation-IEs.c"
  "NR_HandoverPreparationInformation-IEs.h"
  "NR_HandoverPreparationInformation.c"
  "NR_HandoverPreparationInformation.h"
  "NR_HighSpeedConfig-r16.c"
  "NR_HighSpeedConfig-r16.h"
  "NR_HighSpeedConfig-v1700.c"
  "NR_HighSpeedConfig-v1700.h"
  "NR_HighSpeedConfigFR2-r17.c"
  "NR_HighSpeedConfigFR2-r17.h"
  "NR_HighSpeedParameters-r16.c"
  "NR_HighSpeedParameters-r16.h"
  "NR_HighSpeedParameters-v1650.c"
  "NR_HighSpeedParameters-v1650.h"
  "NR_HighSpeedParameters-v1700.c"
  "NR_HighSpeedParameters-v1700.h"
  "NR_Hysteresis.c"
  "NR_Hysteresis.h"
  "NR_HysteresisLocation-r17.c"
  "NR_HysteresisLocation-r17.h"
  "NR_I-RNTI-Value.c"
  "NR_I-RNTI-Value.h"
  "NR_IAB-IP-Address-r16.c"
  "NR_IAB-IP-Address-r16.h"
  "NR_IAB-IP-AddressAndTraffic-r16.c"
  "NR_IAB-IP-AddressAndTraffic-r16.h"
  "NR_IAB-IP-AddressConfiguration-r16.c"
  "NR_IAB-IP-AddressConfiguration-r16.h"
  "NR_IAB-IP-AddressConfigurationList-r16.c"
  "NR_IAB-IP-AddressConfigurationList-r16.h"
  "NR_IAB-IP-AddressIndex-r16.c"
  "NR_IAB-IP-AddressIndex-r16.h"
  "NR_IAB-IP-AddressNumReq-r16.c"
  "NR_IAB-IP-AddressNumReq-r16.h"
  "NR_IAB-IP-AddressPrefixReq-r16.c"
  "NR_IAB-IP-AddressPrefixReq-r16.h"
  "NR_IAB-IP-PrefixAndTraffic-r16.c"
  "NR_IAB-IP-PrefixAndTraffic-r16.h"
  "NR_IAB-IP-Usage-r16.c"
  "NR_IAB-IP-Usage-r16.h"
  "NR_IAB-ResourceConfig-r17.c"
  "NR_IAB-ResourceConfig-r17.h"
  "NR_IAB-ResourceConfigID-r17.c"
  "NR_IAB-ResourceConfigID-r17.h"
  "NR_IABOtherInformation-r16-IEs.c"
  "NR_IABOtherInformation-r16-IEs.h"
  "NR_IABOtherInformation-r16.c"
  "NR_IABOtherInformation-r16.h"
  "NR_IDC-Assistance-r16.c"
  "NR_IDC-Assistance-r16.h"
  "NR_IDC-AssistanceConfig-r16.c"
  "NR_IDC-AssistanceConfig-r16.h"
  "NR_IMS-Parameters-v1700.c"
  "NR_IMS-Parameters-v1700.h"
  "NR_IMS-Parameters.c"
  "NR_IMS-Parameters.h"
  "NR_IMS-ParametersCommon.c"
  "NR_IMS-ParametersCommon.h"
  "NR_IMS-ParametersFR2-2-r17.c"
  "NR_IMS-ParametersFR2-2-r17.h"
  "NR_IMS-ParametersFRX-Diff.c"
  "NR_IMS-ParametersFRX-Diff.h"
  "NR_INT-ConfigurationPerServingCell.c"
  "NR_INT-ConfigurationPerServingCell.h"
  "NR_InitialUE-Identity.c"
  "NR_InitialUE-Identity.h"
  "NR_IntegrityProtAlgorithm.c"
  "NR_IntegrityProtAlgorithm.h"
  "NR_InterFreqAllowedCellList-r16.c"
  "NR_InterFreqAllowedCellList-r16.h"
  "NR_InterFreqCAG-CellListPerPLMN-r16.c"
  "NR_InterFreqCAG-CellListPerPLMN-r16.h"
  "NR_InterFreqCarrierFreqInfo-v1610.c"
  "NR_InterFreqCarrierFreqInfo-v1610.h"
  "NR_InterFreqCarrierFreqInfo-v1700.c"
  "NR_InterFreqCarrierFreqInfo-v1700.h"
  "NR_InterFreqCarrierFreqInfo-v1720.c"
  "NR_InterFreqCarrierFreqInfo-v1720.h"
  "NR_InterFreqCarrierFreqInfo-v1730.c"
  "NR_InterFreqCarrierFreqInfo-v1730.h"
  "NR_InterFreqCarrierFreqInfo.c"
  "NR_InterFreqCarrierFreqInfo.h"
  "NR_InterFreqCarrierFreqList-v1610.c"
  "NR_InterFreqCarrierFreqList-v1610.h"
  "NR_InterFreqCarrierFreqList-v1700.c"
  "NR_InterFreqCarrierFreqList-v1700.h"
  "NR_InterFreqCarrierFreqList-v1720.c"
  "NR_InterFreqCarrierFreqList-v1720.h"
  "NR_InterFreqCarrierFreqList-v1730.c"
  "NR_InterFreqCarrierFreqList-v1730.h"
  "NR_InterFreqCarrierFreqList.c"
  "NR_InterFreqCarrierFreqList.h"
  "NR_InterFreqExcludedCellList.c"
  "NR_InterFreqExcludedCellList.h"
  "NR_InterFreqNeighCellInfo-v1610.c"
  "NR_InterFreqNeighCellInfo-v1610.h"
  "NR_InterFreqNeighCellInfo-v1710.c"
  "NR_InterFreqNeighCellInfo-v1710.h"
  "NR_InterFreqNeighCellInfo.c"
  "NR_InterFreqNeighCellInfo.h"
  "NR_InterFreqNeighCellList-v1610.c"
  "NR_InterFreqNeighCellList-v1610.h"
  "NR_InterFreqNeighCellList-v1710.c"
  "NR_InterFreqNeighCellList-v1710.h"
  "NR_InterFreqNeighCellList.c"
  "NR_InterFreqNeighCellList.h"
  "NR_InterFreqNeighHSDN-CellList-r17.c"
  "NR_InterFreqNeighHSDN-CellList-r17.h"
  "NR_InterFreqTargetInfo-r16.c"
  "NR_InterFreqTargetInfo-r16.h"
  "NR_InterRAT-Parameters.c"
  "NR_InterRAT-Parameters.h"
  "NR_IntraBandCC-Combination-r17.c"
  "NR_IntraBandCC-Combination-r17.h"
  "NR_IntraBandCC-CombinationReqList-r17.c"
  "NR_IntraBandCC-CombinationReqList-r17.h"
  "NR_IntraBandPowerClass-r16.c"
  "NR_IntraBandPowerClass-r16.h"
  "NR_IntraCellGuardBandsPerSCS-r16.c"
  "NR_IntraCellGuardBandsPerSCS-r16.h"
  "NR_IntraFreqAllowedCellList-r16.c"
  "NR_IntraFreqAllowedCellList-r16.h"
  "NR_IntraFreqCAG-CellListPerPLMN-r16.c"
  "NR_IntraFreqCAG-CellListPerPLMN-r16.h"
  "NR_IntraFreqExcludedCellList.c"
  "NR_IntraFreqExcludedCellList.h"
  "NR_IntraFreqNeighCellInfo-v1610.c"
  "NR_IntraFreqNeighCellInfo-v1610.h"
  "NR_IntraFreqNeighCellInfo-v1710.c"
  "NR_IntraFreqNeighCellInfo-v1710.h"
  "NR_IntraFreqNeighCellInfo.c"
  "NR_IntraFreqNeighCellInfo.h"
  "NR_IntraFreqNeighCellList-v1610.c"
  "NR_IntraFreqNeighCellList-v1610.h"
  "NR_IntraFreqNeighCellList-v1710.c"
  "NR_IntraFreqNeighCellList-v1710.h"
  "NR_IntraFreqNeighCellList.c"
  "NR_IntraFreqNeighCellList.h"
  "NR_IntraFreqNeighHSDN-CellList-r17.c"
  "NR_IntraFreqNeighHSDN-CellList-r17.h"
  "NR_InvalidSymbolPattern-r16.c"
  "NR_InvalidSymbolPattern-r16.h"
  "NR_LBT-FailureRecoveryConfig-r16.c"
  "NR_LBT-FailureRecoveryConfig-r16.h"
  "NR_LTE-CRS-PatternList-r16.c"
  "NR_LTE-CRS-PatternList-r16.h"
  "NR_LTE-NeighCellsCRS-AssistInfo-r17.c"
  "NR_LTE-NeighCellsCRS-AssistInfo-r17.h"
  "NR_LTE-NeighCellsCRS-AssistInfoList-r17.c"
  "NR_LTE-NeighCellsCRS-AssistInfoList-r17.h"
  "NR_LocationAndBandwidthBroadcast-r17.c"
  "NR_LocationAndBandwidthBroadcast-r17.h"
  "NR_LocationInfo-r16.c"
  "NR_LocationInfo-r16.h"
  "NR_LocationMeasurementIndication-IEs.c"
  "NR_LocationMeasurementIndication-IEs.h"
  "NR_LocationMeasurementIndication.c"
  "NR_LocationMeasurementIndication.h"
  "NR_LocationMeasurementInfo.c"
  "NR_LocationMeasurementInfo.h"
  "NR_LogMeasInfo-r16.c"
  "NR_LogMeasInfo-r16.h"
  "NR_LogMeasInfoList-r16.c"
  "NR_LogMeasInfoList-r16.h"
  "NR_LogMeasReport-r16.c"
  "NR_LogMeasReport-r16.h"
  "NR_LogMeasResultBT-r16.c"
  "NR_LogMeasResultBT-r16.h"
  "NR_LogMeasResultListBT-r16.c"
  "NR_LogMeasResultListBT-r16.h"
  "NR_LogMeasResultListWLAN-r16.c"
  "NR_LogMeasResultListWLAN-r16.h"
  "NR_LogMeasResultWLAN-r16.c"
  "NR_LogMeasResultWLAN-r16.h"
  "NR_LoggedEventTriggerConfig-r16.c"
  "NR_LoggedEventTriggerConfig-r16.h"
  "NR_LoggedMeasurementConfiguration-r16-IEs.c"
  "NR_LoggedMeasurementConfiguration-r16-IEs.h"
  "NR_LoggedMeasurementConfiguration-r16.c"
  "NR_LoggedMeasurementConfiguration-r16.h"
  "NR_LoggedMeasurementConfiguration-v1700-IEs.c"
  "NR_LoggedMeasurementConfiguration-v1700-IEs.h"
  "NR_LoggedPeriodicalReportConfig-r16.c"
  "NR_LoggedPeriodicalReportConfig-r16.h"
  "NR_LoggingDuration-r16.c"
  "NR_LoggingDuration-r16.h"
  "NR_LoggingInterval-r16.c"
  "NR_LoggingInterval-r16.h"
  "NR_LogicalChannelConfig.c"
  "NR_LogicalChannelConfig.h"
  "NR_LogicalChannelIdentity.c"
  "NR_LogicalChannelIdentity.h"
  "NR_LogicalChannelIdentityExt-r17.c"
  "NR_LogicalChannelIdentityExt-r17.h"
  "NR_MAC-CellGroupConfig.c"
  "NR_MAC-CellGroupConfig.h"
  "NR_MAC-MainConfigSL-r16.c"
  "NR_MAC-MainConfigSL-r16.h"
  "NR_MAC-Parameters-v1610.c"
  "NR_MAC-Parameters-v1610.h"
  "NR_MAC-Parameters-v1700.c"
  "NR_MAC-Parameters-v1700.h"
  "NR_MAC-Parameters.c"
  "NR_MAC-Parameters.h"
  "NR_MAC-ParametersCommon.c"
  "NR_MAC-ParametersCommon.h"
  "NR_MAC-ParametersFR2-2-r17.c"
  "NR_MAC-ParametersFR2-2-r17.h"
  "NR_MAC-ParametersFRX-Diff-r16.c"
  "NR_MAC-ParametersFRX-Diff-r16.h"
  "NR_MAC-ParametersSidelink-r16.c"
  "NR_MAC-ParametersSidelink-r16.h"
  "NR_MAC-ParametersSidelink-r17.c"
  "NR_MAC-ParametersSidelink-r17.h"
  "NR_MAC-ParametersSidelinkCommon-r16.c"
  "NR_MAC-ParametersSidelinkCommon-r16.h"
  "NR_MAC-ParametersSidelinkXDD-Diff-r16.c"
  "NR_MAC-ParametersSidelinkXDD-Diff-r16.h"
  "NR_MAC-ParametersXDD-Diff.c"
  "NR_MAC-ParametersXDD-Diff.h"
  "NR_MBS-FSAI-InterFreq-r17.c"
  "NR_MBS-FSAI-InterFreq-r17.h"
  "NR_MBS-FSAI-InterFreqList-r17.c"
  "NR_MBS-FSAI-InterFreqList-r17.h"
  "NR_MBS-FSAI-List-r17.c"
  "NR_MBS-FSAI-List-r17.h"
  "NR_MBS-FSAI-r17.c"
  "NR_MBS-FSAI-r17.h"
  "NR_MBS-NeighbourCell-r17.c"
  "NR_MBS-NeighbourCell-r17.h"
  "NR_MBS-NeighbourCellList-r17.c"
  "NR_MBS-NeighbourCellList-r17.h"
  "NR_MBS-Parameters-r17.c"
  "NR_MBS-Parameters-r17.h"
  "NR_MBS-RNTI-SpecificConfig-r17.c"
  "NR_MBS-RNTI-SpecificConfig-r17.h"
  "NR_MBS-RNTI-SpecificConfigId-r17.c"
  "NR_MBS-RNTI-SpecificConfigId-r17.h"
  "NR_MBS-ServiceInfo-r17.c"
  "NR_MBS-ServiceInfo-r17.h"
  "NR_MBS-ServiceList-r17.c"
  "NR_MBS-ServiceList-r17.h"
  "NR_MBS-SessionInfo-r17.c"
  "NR_MBS-SessionInfo-r17.h"
  "NR_MBS-SessionInfoList-r17.c"
  "NR_MBS-SessionInfoList-r17.h"
  "NR_MBSBroadcastConfiguration-r17-IEs.c"
  "NR_MBSBroadcastConfiguration-r17-IEs.h"
  "NR_MBSBroadcastConfiguration-r17.c"
  "NR_MBSBroadcastConfiguration-r17.h"
  "NR_MBSInterestIndication-r17-IEs.c"
  "NR_MBSInterestIndication-r17-IEs.h"
  "NR_MBSInterestIndication-r17.c"
  "NR_MBSInterestIndication-r17.h"
  "NR_MCC-MNC-Digit.c"
  "NR_MCC-MNC-Digit.h"
  "NR_MCC.c"
  "NR_MCC.h"
  "NR_MCCH-Config-r17.c"
  "NR_MCCH-Config-r17.h"
  "NR_MCCH-Message-r17.c"
  "NR_MCCH-Message-r17.h"
  "NR_MCCH-MessageType-r17.c"
  "NR_MCCH-MessageType-r17.h"
  "NR_MCCH-RepetitionPeriodAndOffset-r17.c"
  "NR_MCCH-RepetitionPeriodAndOffset-r17.h"
  "NR_MCGFailureInformation-r16-IEs.c"
  "NR_MCGFailureInformation-r16-IEs.h"
  "NR_MCGFailureInformation-r16.c"
  "NR_MCGFailureInformation-r16.h"
  "NR_MIB.c"
  "NR_MIB.h"
  "NR_MIMO-LayersDL.c"
  "NR_MIMO-LayersDL.h"
  "NR_MIMO-LayersUL.c"
  "NR_MIMO-LayersUL.h"
  "NR_MIMO-ParametersPerBand.c"
  "NR_MIMO-ParametersPerBand.h"
  "NR_MIMOParam-r17.c"
  "NR_MIMOParam-r17.h"
  "NR_MNC.c"
  "NR_MNC.h"
  "NR_MPE-Config-FR2-r16.c"
  "NR_MPE-Config-FR2-r16.h"
  "NR_MPE-Config-FR2-r17.c"
  "NR_MPE-Config-FR2-r17.h"
  "NR_MPE-Resource-r17.c"
  "NR_MPE-Resource-r17.h"
  "NR_MPE-ResourceId-r17.c"
  "NR_MPE-ResourceId-r17.h"
  "NR_MRB-Identity-r17.c"
  "NR_MRB-Identity-r17.h"
  "NR_MRB-InfoBroadcast-r17.c"
  "NR_MRB-InfoBroadcast-r17.h"
  "NR_MRB-ListBroadcast-r17.c"
  "NR_MRB-ListBroadcast-r17.h"
  "NR_MRB-PDCP-ConfigBroadcast-r17.c"
  "NR_MRB-PDCP-ConfigBroadcast-r17.h"
  "NR_MRB-RLC-ConfigBroadcast-r17.c"
  "NR_MRB-RLC-ConfigBroadcast-r17.h"
  "NR_MRB-ToAddMod-r17.c"
  "NR_MRB-ToAddMod-r17.h"
  "NR_MRB-ToAddModList-r17.c"
  "NR_MRB-ToAddModList-r17.h"
  "NR_MRB-ToReleaseList-r17.c"
  "NR_MRB-ToReleaseList-r17.h"
  "NR_MRDC-AssistanceInfo.c"
  "NR_MRDC-AssistanceInfo.h"
  "NR_MRDC-Parameters-v1580.c"
  "NR_MRDC-Parameters-v1580.h"
  "NR_MRDC-Parameters-v1590.c"
  "NR_MRDC-Parameters-v1590.h"
  "NR_MRDC-Parameters-v15g0.c"
  "NR_MRDC-Parameters-v15g0.h"
  "NR_MRDC-Parameters-v1620.c"
  "NR_MRDC-Parameters-v1620.h"
  "NR_MRDC-Parameters-v1630.c"
  "NR_MRDC-Parameters-v1630.h"
  "NR_MRDC-Parameters-v1700.c"
  "NR_MRDC-Parameters-v1700.h"
  "NR_MRDC-Parameters.c"
  "NR_MRDC-Parameters.h"
  "NR_MRDC-SecondaryCellGroupConfig.c"
  "NR_MRDC-SecondaryCellGroupConfig.h"
  "NR_MTCH-SSB-MappingWindowCycleOffset-r17.c"
  "NR_MTCH-SSB-MappingWindowCycleOffset-r17.h"
  "NR_MTCH-SSB-MappingWindowIndex-r17.c"
  "NR_MTCH-SSB-MappingWindowIndex-r17.h"
  "NR_MTCH-SSB-MappingWindowList-r17.c"
  "NR_MTCH-SSB-MappingWindowList-r17.h"
  "NR_MUSIM-Assistance-r17.c"
  "NR_MUSIM-Assistance-r17.h"
  "NR_MUSIM-Gap-r17.c"
  "NR_MUSIM-Gap-r17.h"
  "NR_MUSIM-GapAssistanceConfig-r17.c"
  "NR_MUSIM-GapAssistanceConfig-r17.h"
  "NR_MUSIM-GapConfig-r17.c"
  "NR_MUSIM-GapConfig-r17.h"
  "NR_MUSIM-GapId-r17.c"
  "NR_MUSIM-GapId-r17.h"
  "NR_MUSIM-GapInfo-r17.c"
  "NR_MUSIM-GapInfo-r17.h"
  "NR_MUSIM-GapPreferenceList-r17.c"
  "NR_MUSIM-GapPreferenceList-r17.h"
  "NR_MUSIM-LeaveAssistanceConfig-r17.c"
  "NR_MUSIM-LeaveAssistanceConfig-r17.h"
  "NR_MUSIM-Starting-SFN-AndSubframe-r17.c"
  "NR_MUSIM-Starting-SFN-AndSubframe-r17.h"
  "NR_MasterInformationBlockSidelink.c"
  "NR_MasterInformationBlockSidelink.h"
  "NR_MasterKeyUpdate.c"
  "NR_MasterKeyUpdate.h"
  "NR_MaxBW-Preference-r16.c"
  "NR_MaxBW-Preference-r16.h"
  "NR_MaxBW-PreferenceConfig-r16.c"
  "NR_MaxBW-PreferenceConfig-r16.h"
  "NR_MaxBW-PreferenceFR2-2-r17.c"
  "NR_MaxBW-PreferenceFR2-2-r17.h"
  "NR_MaxCC-Preference-r16.c"
  "NR_MaxCC-Preference-r16.h"
  "NR_MaxCC-PreferenceConfig-r16.c"
  "NR_MaxCC-PreferenceConfig-r16.h"
  "NR_MaxMIMO-LayerPreference-r16.c"
  "NR_MaxMIMO-LayerPreference-r16.h"
  "NR_MaxMIMO-LayerPreferenceConfig-r16.c"
  "NR_MaxMIMO-LayerPreferenceConfig-r16.h"
  "NR_MaxMIMO-LayerPreferenceFR2-2-r17.c"
  "NR_MaxMIMO-LayerPreferenceFR2-2-r17.h"
  "NR_MaxMIMO-LayersDCI-0-2-r16.c"
  "NR_MaxMIMO-LayersDCI-0-2-r16.h"
  "NR_MaxMIMO-LayersDL-r16.c"
  "NR_MaxMIMO-LayersDL-r16.h"
  "NR_MeasAndMobParameters-v1700.c"
  "NR_MeasAndMobParameters-v1700.h"
  "NR_MeasAndMobParameters.c"
  "NR_MeasAndMobParameters.h"
  "NR_MeasAndMobParametersCommon.c"
  "NR_MeasAndMobParametersCommon.h"
  "NR_MeasAndMobParametersFR2-2-r17.c"
  "NR_MeasAndMobParametersFR2-2-r17.h"
  "NR_MeasAndMobParametersFRX-Diff.c"
  "NR_MeasAndMobParametersFRX-Diff.h"
  "NR_MeasAndMobParametersMRDC-Common-v1610.c"
  "NR_MeasAndMobParametersMRDC-Common-v1610.h"
  "NR_MeasAndMobParametersMRDC-Common-v1700.c"
  "NR_MeasAndMobParametersMRDC-Common-v1700.h"
  "NR_MeasAndMobParametersMRDC-Common-v1730.c"
  "NR_MeasAndMobParametersMRDC-Common-v1730.h"
  "NR_MeasAndMobParametersMRDC-Common.c"
  "NR_MeasAndMobParametersMRDC-Common.h"
  "NR_MeasAndMobParametersMRDC-FRX-Diff.c"
  "NR_MeasAndMobParametersMRDC-FRX-Diff.h"
  "NR_MeasAndMobParametersMRDC-XDD-Diff-v1560.c"
  "NR_MeasAndMobParametersMRDC-XDD-Diff-v1560.h"
  "NR_MeasAndMobParametersMRDC-XDD-Diff.c"
  "NR_MeasAndMobParametersMRDC-XDD-Diff.h"
  "NR_MeasAndMobParametersMRDC-v1560.c"
  "NR_MeasAndMobParametersMRDC-v1560.h"
  "NR_MeasAndMobParametersMRDC-v1610.c"
  "NR_MeasAndMobParametersMRDC-v1610.h"
  "NR_MeasAndMobParametersMRDC-v1700.c"
  "NR_MeasAndMobParametersMRDC-v1700.h"
  "NR_MeasAndMobParametersMRDC-v1730.c"
  "NR_MeasAndMobParametersMRDC-v1730.h"
  "NR_MeasAndMobParametersMRDC.c"
  "NR_MeasAndMobParametersMRDC.h"
  "NR_MeasAndMobParametersXDD-Diff.c"
  "NR_MeasAndMobParametersXDD-Diff.h"
  "NR_MeasConfig.c"
  "NR_MeasConfig.h"
  "NR_MeasConfigAppLayer-r17.c"
  "NR_MeasConfigAppLayer-r17.h"
  "NR_MeasConfigAppLayerId-r17.c"
  "NR_MeasConfigAppLayerId-r17.h"
  "NR_MeasConfigMN.c"
  "NR_MeasConfigMN.h"
  "NR_MeasConfigSN.c"
  "NR_MeasConfigSN.h"
  "NR_MeasGapConfig.c"
  "NR_MeasGapConfig.h"
  "NR_MeasGapId-r17.c"
  "NR_MeasGapId-r17.h"
  "NR_MeasGapSharingConfig.c"
  "NR_MeasGapSharingConfig.h"
  "NR_MeasGapSharingScheme.c"
  "NR_MeasGapSharingScheme.h"
  "NR_MeasId.c"
  "NR_MeasId.h"
  "NR_MeasIdToAddMod.c"
  "NR_MeasIdToAddMod.h"
  "NR_MeasIdToAddModList.c"
  "NR_MeasIdToAddModList.h"
  "NR_MeasIdToRemoveList.c"
  "NR_MeasIdToRemoveList.h"
  "NR_MeasIdleCarrierEUTRA-r16.c"
  "NR_MeasIdleCarrierEUTRA-r16.h"
  "NR_MeasIdleCarrierNR-r16.c"
  "NR_MeasIdleCarrierNR-r16.h"
  "NR_MeasIdleConfigDedicated-r16.c"
  "NR_MeasIdleConfigDedicated-r16.h"
  "NR_MeasIdleConfigSIB-r16.c"
  "NR_MeasIdleConfigSIB-r16.h"
  "NR_MeasObjectCLI-r16.c"
  "NR_MeasObjectCLI-r16.h"
  "NR_MeasObjectEUTRA.c"
  "NR_MeasObjectEUTRA.h"
  "NR_MeasObjectId.c"
  "NR_MeasObjectId.h"
  "NR_MeasObjectNR-SL-r16.c"
  "NR_MeasObjectNR-SL-r16.h"
  "NR_MeasObjectNR.c"
  "NR_MeasObjectNR.h"
  "NR_MeasObjectRxTxDiff-r17.c"
  "NR_MeasObjectRxTxDiff-r17.h"
  "NR_MeasObjectToAddMod.c"
  "NR_MeasObjectToAddMod.h"
  "NR_MeasObjectToAddModList.c"
  "NR_MeasObjectToAddModList.h"
  "NR_MeasObjectToRemoveList.c"
  "NR_MeasObjectToRemoveList.h"
  "NR_MeasObjectUTRA-FDD-r16.c"
  "NR_MeasObjectUTRA-FDD-r16.h"
  "NR_MeasPosPreConfigGapId-r17.c"
  "NR_MeasPosPreConfigGapId-r17.h"
  "NR_MeasQuantityResults.c"
  "NR_MeasQuantityResults.h"
  "NR_MeasQuantityResultsEUTRA.c"
  "NR_MeasQuantityResultsEUTRA.h"
  "NR_MeasRSSI-ReportConfig-r16.c"
  "NR_MeasRSSI-ReportConfig-r16.h"
  "NR_MeasReportAppLayer-r17.c"
  "NR_MeasReportAppLayer-r17.h"
  "NR_MeasReportQuantity-r16.c"
  "NR_MeasReportQuantity-r16.h"
  "NR_MeasReportQuantity.c"
  "NR_MeasReportQuantity.h"
  "NR_MeasReportQuantityCLI-r16.c"
  "NR_MeasReportQuantityCLI-r16.h"
  "NR_MeasReportQuantityUTRA-FDD-r16.c"
  "NR_MeasReportQuantityUTRA-FDD-r16.h"
  "NR_MeasResult2EUTRA-r16.c"
  "NR_MeasResult2EUTRA-r16.h"
  "NR_MeasResult2EUTRA.c"
  "NR_MeasResult2EUTRA.h"
  "NR_MeasResult2NR-r16.c"
  "NR_MeasResult2NR-r16.h"
  "NR_MeasResult2NR.c"
  "NR_MeasResult2NR.h"
  "NR_MeasResult2UTRA-FDD-r16.c"
  "NR_MeasResult2UTRA-FDD-r16.h"
  "NR_MeasResultCBR-NR-r16.c"
  "NR_MeasResultCBR-NR-r16.h"
  "NR_MeasResultCLI-RSSI-r16.c"
  "NR_MeasResultCLI-RSSI-r16.h"
  "NR_MeasResultCLI-r16.c"
  "NR_MeasResultCLI-r16.h"
  "NR_MeasResultCellListSFTD-EUTRA.c"
  "NR_MeasResultCellListSFTD-EUTRA.h"
  "NR_MeasResultCellListSFTD-NR.c"
  "NR_MeasResultCellListSFTD-NR.h"
  "NR_MeasResultCellSFTD-NR.c"
  "NR_MeasResultCellSFTD-NR.h"
  "NR_MeasResultEUTRA.c"
  "NR_MeasResultEUTRA.h"
  "NR_MeasResultFailedCell-r16.c"
  "NR_MeasResultFailedCell-r16.h"
  "NR_MeasResultForRSSI-r16.c"
  "NR_MeasResultForRSSI-r16.h"
  "NR_MeasResultFreqList.c"
  "NR_MeasResultFreqList.h"
  "NR_MeasResultFreqListFailMRDC.c"
  "NR_MeasResultFreqListFailMRDC.h"
  "NR_MeasResultIdleEUTRA-r16.c"
  "NR_MeasResultIdleEUTRA-r16.h"
  "NR_MeasResultIdleNR-r16.c"
  "NR_MeasResultIdleNR-r16.h"
  "NR_MeasResultList2EUTRA-r16.c"
  "NR_MeasResultList2EUTRA-r16.h"
  "NR_MeasResultList2EUTRA.c"
  "NR_MeasResultList2EUTRA.h"
  "NR_MeasResultList2NR-r16.c"
  "NR_MeasResultList2NR-r16.h"
  "NR_MeasResultList2NR.c"
  "NR_MeasResultList2NR.h"
  "NR_MeasResultList2UTRA.c"
  "NR_MeasResultList2UTRA.h"
  "NR_MeasResultListCLI-RSSI-r16.c"
  "NR_MeasResultListCLI-RSSI-r16.h"
  "NR_MeasResultListEUTRA.c"
  "NR_MeasResultListEUTRA.h"
  "NR_MeasResultListLogging2NR-r16.c"
  "NR_MeasResultListLogging2NR-r16.h"
  "NR_MeasResultListLoggingNR-r16.c"
  "NR_MeasResultListLoggingNR-r16.h"
  "NR_MeasResultListNR.c"
  "NR_MeasResultListNR.h"
  "NR_MeasResultListSRS-RSRP-r16.c"
  "NR_MeasResultListSRS-RSRP-r16.h"
  "NR_MeasResultListUTRA-FDD-r16.c"
  "NR_MeasResultListUTRA-FDD-r16.h"
  "NR_MeasResultLogging2NR-r16.c"
  "NR_MeasResultLogging2NR-r16.h"
  "NR_MeasResultLoggingNR-r16.c"
  "NR_MeasResultLoggingNR-r16.h"
  "NR_MeasResultNR-SL-r16.c"
  "NR_MeasResultNR-SL-r16.h"
  "NR_MeasResultNR.c"
  "NR_MeasResultNR.h"
  "NR_MeasResultRLFNR-r16.c"
  "NR_MeasResultRLFNR-r16.h"
  "NR_MeasResultRxTxTimeDiff-r17.c"
  "NR_MeasResultRxTxTimeDiff-r17.h"
  "NR_MeasResultSCG-Failure.c"
  "NR_MeasResultSCG-Failure.h"
  "NR_MeasResultSFTD-EUTRA.c"
  "NR_MeasResultSFTD-EUTRA.h"
  "NR_MeasResultSRS-RSRP-r16.c"
  "NR_MeasResultSRS-RSRP-r16.h"
  "NR_MeasResultServFreqListEUTRA-SCG.c"
  "NR_MeasResultServFreqListEUTRA-SCG.h"
  "NR_MeasResultServFreqListNR-SCG.c"
  "NR_MeasResultServFreqListNR-SCG.h"
  "NR_MeasResultServMO.c"
  "NR_MeasResultServMO.h"
  "NR_MeasResultServMOList.c"
  "NR_MeasResultServMOList.h"
  "NR_MeasResultServingCell-r16.c"
  "NR_MeasResultServingCell-r16.h"
  "NR_MeasResultSuccessHONR-r17.c"
  "NR_MeasResultSuccessHONR-r17.h"
  "NR_MeasResultUTRA-FDD-r16.c"
  "NR_MeasResultUTRA-FDD-r16.h"
  "NR_MeasResults.c"
  "NR_MeasResults.h"
  "NR_MeasResultsPerCarrierIdleEUTRA-r16.c"
  "NR_MeasResultsPerCarrierIdleEUTRA-r16.h"
  "NR_MeasResultsPerCarrierIdleNR-r16.c"
  "NR_MeasResultsPerCarrierIdleNR-r16.h"
  "NR_MeasResultsPerCellIdleEUTRA-r16.c"
  "NR_MeasResultsPerCellIdleEUTRA-r16.h"
  "NR_MeasResultsPerCellIdleNR-r16.c"
  "NR_MeasResultsPerCellIdleNR-r16.h"
  "NR_MeasResultsSL-r16.c"
  "NR_MeasResultsSL-r16.h"
  "NR_MeasTiming.c"
  "NR_MeasTiming.h"
  "NR_MeasTimingList.c"
  "NR_MeasTimingList.h"
  "NR_MeasTriggerQuantity.c"
  "NR_MeasTriggerQuantity.h"
  "NR_MeasTriggerQuantityCLI-r16.c"
  "NR_MeasTriggerQuantityCLI-r16.h"
  "NR_MeasTriggerQuantityEUTRA.c"
  "NR_MeasTriggerQuantityEUTRA.h"
  "NR_MeasTriggerQuantityOffset.c"
  "NR_MeasTriggerQuantityOffset.h"
  "NR_MeasTriggerQuantityUTRA-FDD-r16.c"
  "NR_MeasTriggerQuantityUTRA-FDD-r16.h"
  "NR_MeasurementReport-IEs.c"
  "NR_MeasurementReport-IEs.h"
  "NR_MeasurementReport.c"
  "NR_MeasurementReport.h"
  "NR_MeasurementReportAppLayer-r17-IEs.c"
  "NR_MeasurementReportAppLayer-r17-IEs.h"
  "NR_MeasurementReportAppLayer-r17.c"
  "NR_MeasurementReportAppLayer-r17.h"
  "NR_MeasurementReportAppLayerList-r17.c"
  "NR_MeasurementReportAppLayerList-r17.h"
  "NR_MeasurementReportSidelink-r16-IEs.c"
  "NR_MeasurementReportSidelink-r16-IEs.h"
  "NR_MeasurementReportSidelink.c"
  "NR_MeasurementReportSidelink.h"
  "NR_MeasurementTimingConfiguration-IEs.c"
  "NR_MeasurementTimingConfiguration-IEs.h"
  "NR_MeasurementTimingConfiguration-v1550-IEs.c"
  "NR_MeasurementTimingConfiguration-v1550-IEs.h"
  "NR_MeasurementTimingConfiguration-v1610-IEs.c"
  "NR_MeasurementTimingConfiguration-v1610-IEs.h"
  "NR_MeasurementTimingConfiguration.c"
  "NR_MeasurementTimingConfiguration.h"
  "NR_MinSchedulingOffsetK0-Values-r16.c"
  "NR_MinSchedulingOffsetK0-Values-r16.h"
  "NR_MinSchedulingOffsetK0-Values-r17.c"
  "NR_MinSchedulingOffsetK0-Values-r17.h"
  "NR_MinSchedulingOffsetK2-Values-r16.c"
  "NR_MinSchedulingOffsetK2-Values-r16.h"
  "NR_MinSchedulingOffsetK2-Values-r17.c"
  "NR_MinSchedulingOffsetK2-Values-r17.h"
  "NR_MinSchedulingOffsetPreference-r16.c"
  "NR_MinSchedulingOffsetPreference-r16.h"
  "NR_MinSchedulingOffsetPreferenceConfig-r16.c"
  "NR_MinSchedulingOffsetPreferenceConfig-r16.h"
  "NR_MinSchedulingOffsetPreferenceExt-r17.c"
  "NR_MinSchedulingOffsetPreferenceExt-r17.h"
  "NR_MinTimeGap-r16.c"
  "NR_MinTimeGap-r16.h"
  "NR_MinTimeGapFR2-2-r17.c"
  "NR_MinTimeGapFR2-2-r17.h"
  "NR_MobilityFromNRCommand-IEs.c"
  "NR_MobilityFromNRCommand-IEs.h"
  "NR_MobilityFromNRCommand-v1610-IEs.c"
  "NR_MobilityFromNRCommand-v1610-IEs.h"
  "NR_MobilityFromNRCommand.c"
  "NR_MobilityFromNRCommand.h"
  "NR_MobilityHistoryReport-r16.c"
  "NR_MobilityHistoryReport-r16.h"
  "NR_MobilityStateParameters.c"
  "NR_MobilityStateParameters.h"
  "NR_ModulationOrder.c"
  "NR_ModulationOrder.h"
  "NR_MsgA-ConfigCommon-r16.c"
  "NR_MsgA-ConfigCommon-r16.h"
  "NR_MsgA-DMRS-Config-r16.c"
  "NR_MsgA-DMRS-Config-r16.h"
  "NR_MsgA-PUSCH-Config-r16.c"
  "NR_MsgA-PUSCH-Config-r16.h"
  "NR_MsgA-PUSCH-Resource-r16.c"
  "NR_MsgA-PUSCH-Resource-r16.h"
  "NR_MultiBandInfoListEUTRA.c"
  "NR_MultiBandInfoListEUTRA.h"
  "NR_MultiDCI-MultiTRP-r16.c"
  "NR_MultiDCI-MultiTRP-r16.h"
  "NR_MultiFrequencyBandListNR-SIB.c"
  "NR_MultiFrequencyBandListNR-SIB.h"
  "NR_MultiFrequencyBandListNR.c"
  "NR_MultiFrequencyBandListNR.h"
  "NR_MultiPDSCH-TDRA-List-r17.c"
  "NR_MultiPDSCH-TDRA-List-r17.h"
  "NR_MultiPDSCH-TDRA-r17.c"
  "NR_MultiPDSCH-TDRA-r17.h"
  "NR_MulticastConfig-r17.c"
  "NR_MulticastConfig-r17.h"
  "NR_MulticastRLC-BearerConfig-r17.c"
  "NR_MulticastRLC-BearerConfig-r17.h"
  "NR_NAICS-Capability-Entry.c"
  "NR_NAICS-Capability-Entry.h"
  "NR_NG-5G-S-TMSI.c"
  "NR_NG-5G-S-TMSI.h"
  "NR_NID-r16.c"
  "NR_NID-r16.h"
  "NR_NPN-Identity-r16.c"
  "NR_NPN-Identity-r16.h"
  "NR_NPN-IdentityInfo-r16.c"
  "NR_NPN-IdentityInfo-r16.h"
  "NR_NPN-IdentityInfoList-r16.c"
  "NR_NPN-IdentityInfoList-r16.h"
  "NR_NR-DL-PRS-PDC-Info-r17.c"
  "NR_NR-DL-PRS-PDC-Info-r17.h"
  "NR_NR-DL-PRS-PDC-ResourceSet-r17.c"
  "NR_NR-DL-PRS-PDC-ResourceSet-r17.h"
  "NR_NR-DL-PRS-Periodicity-and-ResourceSetSlotOffset-r17.c"
  "NR_NR-DL-PRS-Periodicity-and-ResourceSetSlotOffset-r17.h"
  "NR_NR-DL-PRS-Resource-r17.c"
  "NR_NR-DL-PRS-Resource-r17.h"
  "NR_NR-DL-PRS-ResourceID-r17.c"
  "NR_NR-DL-PRS-ResourceID-r17.h"
  "NR_NR-FreqInfo.c"
  "NR_NR-FreqInfo.h"
  "NR_NR-MultiBandInfo.c"
  "NR_NR-MultiBandInfo.h"
  "NR_NR-NS-PmaxList.c"
  "NR_NR-NS-PmaxList.h"
  "NR_NR-NS-PmaxValue.c"
  "NR_NR-NS-PmaxValue.h"
  "NR_NR-PRS-MeasurementInfo-r16.c"
  "NR_NR-PRS-MeasurementInfo-r16.h"
  "NR_NR-PRS-MeasurementInfoList-r16.c"
  "NR_NR-PRS-MeasurementInfoList-r16.h"
  "NR_NR-RS-Type.c"
  "NR_NR-RS-Type.h"
  "NR_NR-TimeStamp-r17.c"
  "NR_NR-TimeStamp-r17.h"
  "NR_NRDC-Parameters-v1570.c"
  "NR_NRDC-Parameters-v1570.h"
  "NR_NRDC-Parameters-v15c0.c"
  "NR_NRDC-Parameters-v15c0.h"
  "NR_NRDC-Parameters-v1610.c"
  "NR_NRDC-Parameters-v1610.h"
  "NR_NRDC-Parameters-v1700.c"
  "NR_NRDC-Parameters-v1700.h"
  "NR_NRDC-Parameters.c"
  "NR_NRDC-Parameters.h"
  "NR_NSAG-ID-r17.c"
  "NR_NSAG-ID-r17.h"
  "NR_NSAG-IdentityInfo-r17.c"
  "NR_NSAG-IdentityInfo-r17.h"
  "NR_NSAG-List-r17.c"
  "NR_NSAG-List-r17.h"
  "NR_NTN-Config-r17.c"
  "NR_NTN-Config-r17.h"
  "NR_NTN-NeighCellConfig-r17.c"
  "NR_NTN-NeighCellConfig-r17.h"
  "NR_NTN-NeighCellConfigList-r17.c"
  "NR_NTN-NeighCellConfigList-r17.h"
  "NR_NTN-Parameters-r17.c"
  "NR_NTN-Parameters-r17.h"
  "NR_NZP-CSI-RS-Pairing-r17.c"
  "NR_NZP-CSI-RS-Pairing-r17.h"
  "NR_NZP-CSI-RS-Resource.c"
  "NR_NZP-CSI-RS-Resource.h"
  "NR_NZP-CSI-RS-ResourceId.c"
  "NR_NZP-CSI-RS-ResourceId.h"
  "NR_NZP-CSI-RS-ResourceSet.c"
  "NR_NZP-CSI-RS-ResourceSet.h"
  "NR_NZP-CSI-RS-ResourceSetId.c"
  "NR_NZP-CSI-RS-ResourceSetId.h"
  "NR_NeedForGapNCSG-ConfigEUTRA-r17.c"
  "NR_NeedForGapNCSG-ConfigEUTRA-r17.h"
  "NR_NeedForGapNCSG-ConfigNR-r17.c"
  "NR_NeedForGapNCSG-ConfigNR-r17.h"
  "NR_NeedForGapNCSG-InfoEUTRA-r17.c"
  "NR_NeedForGapNCSG-InfoEUTRA-r17.h"
  "NR_NeedForGapNCSG-InfoNR-r17.c"
  "NR_NeedForGapNCSG-InfoNR-r17.h"
  "NR_NeedForGapsBandListNR-r16.c"
  "NR_NeedForGapsBandListNR-r16.h"
  "NR_NeedForGapsConfigNR-r16.c"
  "NR_NeedForGapsConfigNR-r16.h"
  "NR_NeedForGapsInfoNR-r16.c"
  "NR_NeedForGapsInfoNR-r16.h"
  "NR_NeedForGapsIntraFreq-r16.c"
  "NR_NeedForGapsIntraFreq-r16.h"
  "NR_NeedForGapsIntraFreqList-r16.c"
  "NR_NeedForGapsIntraFreqList-r16.h"
  "NR_NeedForGapsNR-r16.c"
  "NR_NeedForGapsNR-r16.h"
  "NR_NeedForNCSG-BandListNR-r17.c"
  "NR_NeedForNCSG-BandListNR-r17.h"
  "NR_NeedForNCSG-EUTRA-r17.c"
  "NR_NeedForNCSG-EUTRA-r17.h"
  "NR_NeedForNCSG-IntraFreq-r17.c"
  "NR_NeedForNCSG-IntraFreq-r17.h"
  "NR_NeedForNCSG-IntraFreqList-r17.c"
  "NR_NeedForNCSG-IntraFreqList-r17.h"
  "NR_NeedForNCSG-NR-r17.c"
  "NR_NeedForNCSG-NR-r17.h"
  "NR_NeighbourCellInfo-r17.c"
  "NR_NeighbourCellInfo-r17.h"
  "NR_NextHopChainingCount.c"
  "NR_NextHopChainingCount.h"
  "NR_NonCellDefiningSSB-r17.c"
  "NR_NonCellDefiningSSB-r17.h"
  "NR_NotificationMessageSidelink-r17-IEs.c"
  "NR_NotificationMessageSidelink-r17-IEs.h"
  "NR_NotificationMessageSidelink-r17.c"
  "NR_NotificationMessageSidelink-r17.h"
  "NR_NumberOfCarriers.c"
  "NR_NumberOfCarriers.h"
  "NR_NumberOfMsg3-Repetitions-r17.c"
  "NR_NumberOfMsg3-Repetitions-r17.h"
  "NR_OLPC-SRS-Pos-r16.c"
  "NR_OLPC-SRS-Pos-r16.h"
  "NR_OffsetValue-r17.c"
  "NR_OffsetValue-r17.h"
  "NR_OnDemandSIB-Request-r16.c"
  "NR_OnDemandSIB-Request-r16.h"
  "NR_Orbital-r17.c"
  "NR_Orbital-r17.h"
  "NR_OtherConfig-v1540.c"
  "NR_OtherConfig-v1540.h"
  "NR_OtherConfig-v1610.c"
  "NR_OtherConfig-v1610.h"
  "NR_OtherConfig-v1700.c"
  "NR_OtherConfig-v1700.h"
  "NR_OtherConfig.c"
  "NR_OtherConfig.h"
  "NR_OutsideActiveTimeConfig-r16.c"
  "NR_OutsideActiveTimeConfig-r16.h"
  "NR_OverheatingAssistance-r17.c"
  "NR_OverheatingAssistance-r17.h"
  "NR_OverheatingAssistance.c"
  "NR_OverheatingAssistance.h"
  "NR_OverheatingAssistanceConfig.c"
  "NR_OverheatingAssistanceConfig.h"
  "NR_P-Max.c"
  "NR_P-Max.h"
  "NR_P0-PUCCH-Id.c"
  "NR_P0-PUCCH-Id.h"
  "NR_P0-PUCCH.c"
  "NR_P0-PUCCH.h"
  "NR_P0-PUSCH-AlphaSet.c"
  "NR_P0-PUSCH-AlphaSet.h"
  "NR_P0-PUSCH-AlphaSetId.c"
  "NR_P0-PUSCH-AlphaSetId.h"
  "NR_P0-PUSCH-Set-r16.c"
  "NR_P0-PUSCH-Set-r16.h"
  "NR_P0-PUSCH-SetId-r16.c"
  "NR_P0-PUSCH-SetId-r16.h"
  "NR_P0-PUSCH-r16.c"
  "NR_P0-PUSCH-r16.h"
  "NR_P0AlphaSet-r17.c"
  "NR_P0AlphaSet-r17.h"
  "NR_PCCH-Config.c"
  "NR_PCCH-Config.h"
  "NR_PCCH-Message.c"
  "NR_PCCH-Message.h"
  "NR_PCCH-MessageType.c"
  "NR_PCCH-MessageType.h"
  "NR_PCI-ARFCN-EUTRA-r16.c"
  "NR_PCI-ARFCN-EUTRA-r16.h"
  "NR_PCI-ARFCN-NR-r16.c"
  "NR_PCI-ARFCN-NR-r16.h"
  "NR_PCI-List.c"
  "NR_PCI-List.h"
  "NR_PCI-Range.c"
  "NR_PCI-Range.h"
  "NR_PCI-RangeElement.c"
  "NR_PCI-RangeElement.h"
  "NR_PCI-RangeIndex.c"
  "NR_PCI-RangeIndex.h"
  "NR_PCI-RangeIndexList.c"
  "NR_PCI-RangeIndexList.h"
  "NR_PDCCH-BlindDetection.c"
  "NR_PDCCH-BlindDetection.h"
  "NR_PDCCH-BlindDetection2-r16.c"
  "NR_PDCCH-BlindDetection2-r16.h"
  "NR_PDCCH-BlindDetection3-r16.c"
  "NR_PDCCH-BlindDetection3-r16.h"
  "NR_PDCCH-BlindDetectionCA-CombIndicator-r16.c"
  "NR_PDCCH-BlindDetectionCA-CombIndicator-r16.h"
  "NR_PDCCH-BlindDetectionCA-CombIndicator-r17.c"
  "NR_PDCCH-BlindDetectionCA-CombIndicator-r17.h"
  "NR_PDCCH-BlindDetectionCA-Mixed-r17.c"
  "NR_PDCCH-BlindDetectionCA-Mixed-r17.h"
  "NR_PDCCH-BlindDetectionCA-Mixed1-r17.c"
  "NR_PDCCH-BlindDetectionCA-Mixed1-r17.h"
  "NR_PDCCH-BlindDetectionCA-MixedExt-r16.c"
  "NR_PDCCH-BlindDetectionCA-MixedExt-r16.h"
  "NR_PDCCH-BlindDetectionCG-UE-Mixed-r17.c"
  "NR_PDCCH-BlindDetectionCG-UE-Mixed-r17.h"
  "NR_PDCCH-BlindDetectionCG-UE-Mixed1-r17.c"
  "NR_PDCCH-BlindDetectionCG-UE-Mixed1-r17.h"
  "NR_PDCCH-BlindDetectionCG-UE-MixedExt-r16.c"
  "NR_PDCCH-BlindDetectionCG-UE-MixedExt-r16.h"
  "NR_PDCCH-BlindDetectionMCG-SCG-r17.c"
  "NR_PDCCH-BlindDetectionMCG-SCG-r17.h"
  "NR_PDCCH-BlindDetectionMixed-r17.c"
  "NR_PDCCH-BlindDetectionMixed-r17.h"
  "NR_PDCCH-BlindDetectionMixed1-r17.c"
  "NR_PDCCH-BlindDetectionMixed1-r17.h"
  "NR_PDCCH-BlindDetectionMixedList-r16.c"
  "NR_PDCCH-BlindDetectionMixedList-r16.h"
  "NR_PDCCH-Config.c"
  "NR_PDCCH-Config.h"
  "NR_PDCCH-ConfigCommon.c"
  "NR_PDCCH-ConfigCommon.h"
  "NR_PDCCH-ConfigSIB1.c"
  "NR_PDCCH-ConfigSIB1.h"
  "NR_PDCCH-MonitoringOccasions-r16.c"
  "NR_PDCCH-MonitoringOccasions-r16.h"
  "NR_PDCCH-RepetitionParameters-r17.c"
  "NR_PDCCH-RepetitionParameters-r17.h"
  "NR_PDCCH-ServingCellConfig.c"
  "NR_PDCCH-ServingCellConfig.h"
  "NR_PDCP-Config.c"
  "NR_PDCP-Config.h"
  "NR_PDCP-Parameters.c"
  "NR_PDCP-Parameters.h"
  "NR_PDCP-ParametersMRDC-v1610.c"
  "NR_PDCP-ParametersMRDC-v1610.h"
  "NR_PDCP-ParametersMRDC.c"
  "NR_PDCP-ParametersMRDC.h"
  "NR_PDCP-ParametersSidelink-r16.c"
  "NR_PDCP-ParametersSidelink-r16.h"
  "NR_PDSCH-CodeBlockGroupTransmission.c"
  "NR_PDSCH-CodeBlockGroupTransmission.h"
  "NR_PDSCH-CodeBlockGroupTransmissionList-r16.c"
  "NR_PDSCH-CodeBlockGroupTransmissionList-r16.h"
  "NR_PDSCH-Config.c"
  "NR_PDSCH-Config.h"
  "NR_PDSCH-ConfigBroadcast-r17.c"
  "NR_PDSCH-ConfigBroadcast-r17.h"
  "NR_PDSCH-ConfigCommon.c"
  "NR_PDSCH-ConfigCommon.h"
  "NR_PDSCH-ConfigIndex-r17.c"
  "NR_PDSCH-ConfigIndex-r17.h"
  "NR_PDSCH-ConfigPTM-r17.c"
  "NR_PDSCH-ConfigPTM-r17.h"
  "NR_PDSCH-HARQ-ACK-CodebookList-r16.c"
  "NR_PDSCH-HARQ-ACK-CodebookList-r16.h"
  "NR_PDSCH-HARQ-ACK-EnhType3-r17.c"
  "NR_PDSCH-HARQ-ACK-EnhType3-r17.h"
  "NR_PDSCH-HARQ-ACK-EnhType3Index-r17.c"
  "NR_PDSCH-HARQ-ACK-EnhType3Index-r17.h"
  "NR_PDSCH-ServingCellConfig.c"
  "NR_PDSCH-ServingCellConfig.h"
  "NR_PDSCH-TimeDomainResourceAllocation-r16.c"
  "NR_PDSCH-TimeDomainResourceAllocation-r16.h"
  "NR_PDSCH-TimeDomainResourceAllocation.c"
  "NR_PDSCH-TimeDomainResourceAllocation.h"
  "NR_PDSCH-TimeDomainResourceAllocationList-r16.c"
  "NR_PDSCH-TimeDomainResourceAllocationList-r16.h"
  "NR_PDSCH-TimeDomainResourceAllocationList.c"
  "NR_PDSCH-TimeDomainResourceAllocationList.h"
  "NR_PDU-SessionID.c"
  "NR_PDU-SessionID.h"
  "NR_PEI-Config-r17.c"
  "NR_PEI-Config-r17.h"
  "NR_PH-InfoMCG.c"
  "NR_PH-InfoMCG.h"
  "NR_PH-InfoSCG.c"
  "NR_PH-InfoSCG.h"
  "NR_PH-TypeListMCG.c"
  "NR_PH-TypeListMCG.h"
  "NR_PH-TypeListSCG.c"
  "NR_PH-TypeListSCG.h"
  "NR_PH-UplinkCarrierMCG.c"
  "NR_PH-UplinkCarrierMCG.h"
  "NR_PH-UplinkCarrierSCG.c"
  "NR_PH-UplinkCarrierSCG.h"
  "NR_PHR-Config.c"
  "NR_PHR-Config.h"
  "NR_PLMN-Identity-EUTRA-5GC.c"
  "NR_PLMN-Identity-EUTRA-5GC.h"
  "NR_PLMN-Identity.c"
  "NR_PLMN-Identity.h"
  "NR_PLMN-IdentityInfo.c"
  "NR_PLMN-IdentityInfo.h"
  "NR_PLMN-IdentityInfoList.c"
  "NR_PLMN-IdentityInfoList.h"
  "NR_PLMN-IdentityList-EUTRA-5GC.c"
  "NR_PLMN-IdentityList-EUTRA-5GC.h"
  "NR_PLMN-IdentityList-EUTRA-EPC.c"
  "NR_PLMN-IdentityList-EUTRA-EPC.h"
  "NR_PLMN-IdentityList-r16.c"
  "NR_PLMN-IdentityList-r16.h"
  "NR_PLMN-IdentityList2-r16.c"
  "NR_PLMN-IdentityList2-r16.h"
  "NR_PLMN-RAN-AreaCell.c"
  "NR_PLMN-RAN-AreaCell.h"
  "NR_PLMN-RAN-AreaCellList.c"
  "NR_PLMN-RAN-AreaCellList.h"
  "NR_PLMN-RAN-AreaConfig.c"
  "NR_PLMN-RAN-AreaConfig.h"
  "NR_PLMN-RAN-AreaConfigList.c"
  "NR_PLMN-RAN-AreaConfigList.h"
  "NR_PRACH-ResourceDedicatedBFR.c"
  "NR_PRACH-ResourceDedicatedBFR.h"
  "NR_PRB-Id.c"
  "NR_PRB-Id.h"
  "NR_PRS-ProcessingCapabilityOutsideMGinPPWperType-r17.c"
  "NR_PRS-ProcessingCapabilityOutsideMGinPPWperType-r17.h"
  "NR_PTRS-DensityRecommendationDL.c"
  "NR_PTRS-DensityRecommendationDL.h"
  "NR_PTRS-DensityRecommendationUL.c"
  "NR_PTRS-DensityRecommendationUL.h"
  "NR_PTRS-DownlinkConfig.c"
  "NR_PTRS-DownlinkConfig.h"
  "NR_PTRS-UplinkConfig.c"
  "NR_PTRS-UplinkConfig.h"
  "NR_PUCCH-CSI-Resource.c"
  "NR_PUCCH-CSI-Resource.h"
  "NR_PUCCH-Config.c"
  "NR_PUCCH-Config.h"
  "NR_PUCCH-ConfigCommon.c"
  "NR_PUCCH-ConfigCommon.h"
  "NR_PUCCH-ConfigurationList-r16.c"
  "NR_PUCCH-ConfigurationList-r16.h"
  "NR_PUCCH-FormatConfig.c"
  "NR_PUCCH-FormatConfig.h"
  "NR_PUCCH-FormatConfigExt-r17.c"
  "NR_PUCCH-FormatConfigExt-r17.h"
  "NR_PUCCH-Group-Config-r17.c"
  "NR_PUCCH-Group-Config-r17.h"
  "NR_PUCCH-Grp-CarrierTypes-r16.c"
  "NR_PUCCH-Grp-CarrierTypes-r16.h"
  "NR_PUCCH-MaxCodeRate.c"
  "NR_PUCCH-MaxCodeRate.h"
  "NR_PUCCH-PathlossReferenceRS-Id-r17.c"
  "NR_PUCCH-PathlossReferenceRS-Id-r17.h"
  "NR_PUCCH-PathlossReferenceRS-Id-v1610.c"
  "NR_PUCCH-PathlossReferenceRS-Id-v1610.h"
  "NR_PUCCH-PathlossReferenceRS-Id.c"
  "NR_PUCCH-PathlossReferenceRS-Id.h"
  "NR_PUCCH-PathlossReferenceRS-r16.c"
  "NR_PUCCH-PathlossReferenceRS-r16.h"
  "NR_PUCCH-PathlossReferenceRS.c"
  "NR_PUCCH-PathlossReferenceRS.h"
  "NR_PUCCH-PowerControl.c"
  "NR_PUCCH-PowerControl.h"
  "NR_PUCCH-PowerControlSetInfo-r17.c"
  "NR_PUCCH-PowerControlSetInfo-r17.h"
  "NR_PUCCH-PowerControlSetInfoId-r17.c"
  "NR_PUCCH-PowerControlSetInfoId-r17.h"
  "NR_PUCCH-Resource.c"
  "NR_PUCCH-Resource.h"
  "NR_PUCCH-ResourceExt-v1610.c"
  "NR_PUCCH-ResourceExt-v1610.h"
  "NR_PUCCH-ResourceGroup-r16.c"
  "NR_PUCCH-ResourceGroup-r16.h"
  "NR_PUCCH-ResourceGroupId-r16.c"
  "NR_PUCCH-ResourceGroupId-r16.h"
  "NR_PUCCH-ResourceId.c"
  "NR_PUCCH-ResourceId.h"
  "NR_PUCCH-ResourceSet.c"
  "NR_PUCCH-ResourceSet.h"
  "NR_PUCCH-ResourceSetId.c"
  "NR_PUCCH-ResourceSetId.h"
  "NR_PUCCH-SRS.c"
  "NR_PUCCH-SRS.h"
  "NR_PUCCH-SpatialRelationInfo.c"
  "NR_PUCCH-SpatialRelationInfo.h"
  "NR_PUCCH-SpatialRelationInfoExt-r16.c"
  "NR_PUCCH-SpatialRelationInfoExt-r16.h"
  "NR_PUCCH-SpatialRelationInfoId-r16.c"
  "NR_PUCCH-SpatialRelationInfoId-r16.h"
  "NR_PUCCH-SpatialRelationInfoId-v1610.c"
  "NR_PUCCH-SpatialRelationInfoId-v1610.h"
  "NR_PUCCH-SpatialRelationInfoId.c"
  "NR_PUCCH-SpatialRelationInfoId.h"
  "NR_PUCCH-TPC-CommandConfig.c"
  "NR_PUCCH-TPC-CommandConfig.h"
  "NR_PUCCH-format0.c"
  "NR_PUCCH-format0.h"
  "NR_PUCCH-format1.c"
  "NR_PUCCH-format1.h"
  "NR_PUCCH-format2.c"
  "NR_PUCCH-format2.h"
  "NR_PUCCH-format3.c"
  "NR_PUCCH-format3.h"
  "NR_PUCCH-format4.c"
  "NR_PUCCH-format4.h"
  "NR_PUSCH-Allocation-r16.c"
  "NR_PUSCH-Allocation-r16.h"
  "NR_PUSCH-CodeBlockGroupTransmission.c"
  "NR_PUSCH-CodeBlockGroupTransmission.h"
  "NR_PUSCH-Config.c"
  "NR_PUSCH-Config.h"
  "NR_PUSCH-ConfigCommon.c"
  "NR_PUSCH-ConfigCommon.h"
  "NR_PUSCH-PathlossReferenceRS-Id-r17.c"
  "NR_PUSCH-PathlossReferenceRS-Id-r17.h"
  "NR_PUSCH-PathlossReferenceRS-Id-v1610.c"
  "NR_PUSCH-PathlossReferenceRS-Id-v1610.h"
  "NR_PUSCH-PathlossReferenceRS-Id.c"
  "NR_PUSCH-PathlossReferenceRS-Id.h"
  "NR_PUSCH-PathlossReferenceRS-r16.c"
  "NR_PUSCH-PathlossReferenceRS-r16.h"
  "NR_PUSCH-PathlossReferenceRS.c"
  "NR_PUSCH-PathlossReferenceRS.h"
  "NR_PUSCH-PowerControl-v1610.c"
  "NR_PUSCH-PowerControl-v1610.h"
  "NR_PUSCH-PowerControl.c"
  "NR_PUSCH-PowerControl.h"
  "NR_PUSCH-ServingCellConfig.c"
  "NR_PUSCH-ServingCellConfig.h"
  "NR_PUSCH-TPC-CommandConfig.c"
  "NR_PUSCH-TPC-CommandConfig.h"
  "NR_PUSCH-TimeDomainResourceAllocation-r16.c"
  "NR_PUSCH-TimeDomainResourceAllocation-r16.h"
  "NR_PUSCH-TimeDomainResourceAllocation.c"
  "NR_PUSCH-TimeDomainResourceAllocation.h"
  "NR_PUSCH-TimeDomainResourceAllocationList-r16.c"
  "NR_PUSCH-TimeDomainResourceAllocationList-r16.h"
  "NR_PUSCH-TimeDomainResourceAllocationList.c"
  "NR_PUSCH-TimeDomainResourceAllocationList.h"
  "NR_Paging-v1700-IEs.c"
  "NR_Paging-v1700-IEs.h"
  "NR_Paging.c"
  "NR_Paging.h"
  "NR_PagingCycle.c"
  "NR_PagingCycle.h"
  "NR_PagingGroupList-r17.c"
  "NR_PagingGroupList-r17.h"
  "NR_PagingRecord-v1700.c"
  "NR_PagingRecord-v1700.h"
  "NR_PagingRecord.c"
  "NR_PagingRecord.h"
  "NR_PagingRecordList-v1700.c"
  "NR_PagingRecordList-v1700.h"
  "NR_PagingRecordList.c"
  "NR_PagingRecordList.h"
  "NR_PagingUE-Identity.c"
  "NR_PagingUE-Identity.h"
  "NR_PathlossReferenceRS-Config.c"
  "NR_PathlossReferenceRS-Config.h"
  "NR_PathlossReferenceRS-Id-r17.c"
  "NR_PathlossReferenceRS-Id-r17.h"
  "NR_PathlossReferenceRS-r16.c"
  "NR_PathlossReferenceRS-r16.h"
  "NR_PathlossReferenceRS-r17.c"
  "NR_PathlossReferenceRS-r17.h"
  "NR_PathlossReferenceRSList-r16.c"
  "NR_PathlossReferenceRSList-r16.h"
  "NR_PathlossReferenceRSs-v1610.c"
  "NR_PathlossReferenceRSs-v1610.h"
  "NR_PerRAAttemptInfo-r16.c"
  "NR_PerRAAttemptInfo-r16.h"
  "NR_PerRAAttemptInfoList-r16.c"
  "NR_PerRAAttemptInfoList-r16.h"
  "NR_PerRACSI-RSInfo-r16.c"
  "NR_PerRACSI-RSInfo-r16.h"
  "NR_PerRACSI-RSInfo-v1660.c"
  "NR_PerRACSI-RSInfo-v1660.h"
  "NR_PerRAInfo-r16.c"
  "NR_PerRAInfo-r16.h"
  "NR_PerRAInfoList-r16.c"
  "NR_PerRAInfoList-r16.h"
  "NR_PerRAInfoList-v1660.c"
  "NR_PerRAInfoList-v1660.h"
  "NR_PerRASSBInfo-r16.c"
  "NR_PerRASSBInfo-r16.h"
  "NR_PeriodicRNAU-TimerValue.c"
  "NR_PeriodicRNAU-TimerValue.h"
  "NR_PeriodicalReportConfig.c"
  "NR_PeriodicalReportConfig.h"
  "NR_PeriodicalReportConfigInterRAT.c"
  "NR_PeriodicalReportConfigInterRAT.h"
  "NR_PeriodicalReportConfigNR-SL-r16.c"
  "NR_PeriodicalReportConfigNR-SL-r16.h"
  "NR_Phy-Parameters-v16a0.c"
  "NR_Phy-Parameters-v16a0.h"
  "NR_Phy-Parameters.c"
  "NR_Phy-Parameters.h"
  "NR_Phy-ParametersCommon-v16a0.c"
  "NR_Phy-ParametersCommon-v16a0.h"
  "NR_Phy-ParametersCommon.c"
  "NR_Phy-ParametersCommon.h"
  "NR_Phy-ParametersFR1.c"
  "NR_Phy-ParametersFR1.h"
  "NR_Phy-ParametersFR2.c"
  "NR_Phy-ParametersFR2.h"
  "NR_Phy-ParametersFRX-Diff.c"
  "NR_Phy-ParametersFRX-Diff.h"
  "NR_Phy-ParametersMRDC.c"
  "NR_Phy-ParametersMRDC.h"
  "NR_Phy-ParametersSharedSpectrumChAccess-r16.c"
  "NR_Phy-ParametersSharedSpectrumChAccess-r16.h"
  "NR_Phy-ParametersXDD-Diff.c"
  "NR_Phy-ParametersXDD-Diff.h"
  "NR_PhysCellId.c"
  "NR_PhysCellId.h"
  "NR_PhysCellIdUTRA-FDD-r16.c"
  "NR_PhysCellIdUTRA-FDD-r16.h"
  "NR_PhysicalCellGroupConfig.c"
  "NR_PhysicalCellGroupConfig.h"
  "NR_PollByte.c"
  "NR_PollByte.h"
  "NR_PollPDU.c"
  "NR_PollPDU.h"
  "NR_PortIndex2.c"
  "NR_PortIndex2.h"
  "NR_PortIndex4.c"
  "NR_PortIndex4.h"
  "NR_PortIndex8.c"
  "NR_PortIndex8.h"
  "NR_PortIndexFor8Ranks.c"
  "NR_PortIndexFor8Ranks.h"
  "NR_PosGapConfig-r17.c"
  "NR_PosGapConfig-r17.h"
  "NR_PosMeasGapPreConfigToAddModList-r17.c"
  "NR_PosMeasGapPreConfigToAddModList-r17.h"
  "NR_PosMeasGapPreConfigToReleaseList-r17.c"
  "NR_PosMeasGapPreConfigToReleaseList-r17.h"
  "NR_PosSI-SchedulingInfo-r16.c"
  "NR_PosSI-SchedulingInfo-r16.h"
  "NR_PosSIB-MappingInfo-r16.c"
  "NR_PosSIB-MappingInfo-r16.h"
  "NR_PosSIB-ReqInfo-r16.c"
  "NR_PosSIB-ReqInfo-r16.h"
  "NR_PosSIB-Type-r16.c"
  "NR_PosSIB-Type-r16.h"
  "NR_PosSRS-RRC-Inactive-OutsideInitialUL-BWP-r17.c"
  "NR_PosSRS-RRC-Inactive-OutsideInitialUL-BWP-r17.h"
  "NR_PosSchedulingInfo-r16.c"
  "NR_PosSchedulingInfo-r16.h"
  "NR_PosSystemInformation-r16-IEs.c"
  "NR_PosSystemInformation-r16-IEs.h"
  "NR_PositionStateVector-r17.c"
  "NR_PositionStateVector-r17.h"
  "NR_PositionVelocity-r17.c"
  "NR_PositionVelocity-r17.h"
  "NR_PowSav-Parameters-r16.c"
  "NR_PowSav-Parameters-r16.h"
  "NR_PowSav-Parameters-v1700.c"
  "NR_PowSav-Parameters-v1700.h"
  "NR_PowSav-ParametersCommon-r16.c"
  "NR_PowSav-ParametersCommon-r16.h"
  "NR_PowSav-ParametersFR2-2-r17.c"
  "NR_PowSav-ParametersFR2-2-r17.h"
  "NR_PowSav-ParametersFRX-Diff-r16.c"
  "NR_PowSav-ParametersFRX-Diff-r16.h"
  "NR_ProcessingParameters.c"
  "NR_ProcessingParameters.h"
  "NR_PropDelayDiffReportConfig-r17.c"
  "NR_PropDelayDiffReportConfig-r17.h"
  "NR_PropagationDelayDifference-r17.c"
  "NR_PropagationDelayDifference-r17.h"
  "NR_Q-OffsetRange.c"
  "NR_Q-OffsetRange.h"
  "NR_Q-OffsetRangeList.c"
  "NR_Q-OffsetRangeList.h"
  "NR_Q-QualMin.c"
  "NR_Q-QualMin.h"
  "NR_Q-RxLevMin.c"
  "NR_Q-RxLevMin.h"
  "NR_QCL-Info.c"
  "NR_QCL-Info.h"
  "NR_QFI.c"
  "NR_QFI.h"
  "NR_QuantityConfig.c"
  "NR_QuantityConfig.h"
  "NR_QuantityConfigNR.c"
  "NR_QuantityConfigNR.h"
  "NR_QuantityConfigRS.c"
  "NR_QuantityConfigRS.h"
  "NR_QuantityConfigUTRA-FDD-r16.c"
  "NR_QuantityConfigUTRA-FDD-r16.h"
  "NR_RA-InformationCommon-r16.c"
  "NR_RA-InformationCommon-r16.h"
  "NR_RA-Prioritization.c"
  "NR_RA-Prioritization.h"
  "NR_RA-PrioritizationForSlicing-r17.c"
  "NR_RA-PrioritizationForSlicing-r17.h"
  "NR_RA-PrioritizationSliceInfo-r17.c"
  "NR_RA-PrioritizationSliceInfo-r17.h"
  "NR_RA-PrioritizationSliceInfoList-r17.c"
  "NR_RA-PrioritizationSliceInfoList-r17.h"
  "NR_RA-Report-r16.c"
  "NR_RA-Report-r16.h"
  "NR_RA-ReportList-r16.c"
  "NR_RA-ReportList-r16.h"
  "NR_RACH-ConfigCommon.c"
  "NR_RACH-ConfigCommon.h"
  "NR_RACH-ConfigCommonTwoStepRA-r16.c"
  "NR_RACH-ConfigCommonTwoStepRA-r16.h"
  "NR_RACH-ConfigDedicated.c"
  "NR_RACH-ConfigDedicated.h"
  "NR_RACH-ConfigGeneric.c"
  "NR_RACH-ConfigGeneric.h"
  "NR_RACH-ConfigGenericTwoStepRA-r16.c"
  "NR_RACH-ConfigGenericTwoStepRA-r16.h"
  "NR_RAN-AreaCode.c"
  "NR_RAN-AreaCode.h"
  "NR_RAN-AreaConfig.c"
  "NR_RAN-AreaConfig.h"
  "NR_RAN-NotificationAreaInfo.c"
  "NR_RAN-NotificationAreaInfo.h"
  "NR_RAN-VisibleMeasurements-r17.c"
  "NR_RAN-VisibleMeasurements-r17.h"
  "NR_RAN-VisibleParameters-r17.c"
  "NR_RAN-VisibleParameters-r17.h"
  "NR_RAT-Type.c"
  "NR_RAT-Type.h"
  "NR_RB-SetGroup-r17.c"
  "NR_RB-SetGroup-r17.h"
  "NR_RF-Parameters-v15g0.c"
  "NR_RF-Parameters-v15g0.h"
  "NR_RF-Parameters-v16a0.c"
  "NR_RF-Parameters-v16a0.h"
  "NR_RF-Parameters.c"
  "NR_RF-Parameters.h"
  "NR_RF-ParametersMRDC-v15g0.c"
  "NR_RF-ParametersMRDC-v15g0.h"
  "NR_RF-ParametersMRDC.c"
  "NR_RF-ParametersMRDC.h"
  "NR_RLC-BearerConfig.c"
  "NR_RLC-BearerConfig.h"
  "NR_RLC-Config-v1610.c"
  "NR_RLC-Config-v1610.h"
  "NR_RLC-Config-v1700.c"
  "NR_RLC-Config-v1700.h"
  "NR_RLC-Config.c"
  "NR_RLC-Config.h"
  "NR_RLC-Parameters.c"
  "NR_RLC-Parameters.h"
  "NR_RLC-ParametersSidelink-r16.c"
  "NR_RLC-ParametersSidelink-r16.h"
  "NR_RLF-Report-r16.c"
  "NR_RLF-Report-r16.h"
  "NR_RLF-TimersAndConstants.c"
  "NR_RLF-TimersAndConstants.h"
  "NR_RLM-RelaxationReportingConfig-r17.c"
  "NR_RLM-RelaxationReportingConfig-r17.h"
  "NR_RMTC-Config-r16.c"
  "NR_RMTC-Config-r16.h"
  "NR_RNTI-Value.c"
  "NR_RNTI-Value.h"
  "NR_RRC-PosSystemInfoRequest-r16-IEs.c"
  "NR_RRC-PosSystemInfoRequest-r16-IEs.h"
  "NR_RRC-TransactionIdentifier.c"
  "NR_RRC-TransactionIdentifier.h"
  "NR_RRCReconfiguration-IEs.c"
  "NR_RRCReconfiguration-IEs.h"
  "NR_RRCReconfiguration-v1530-IEs.c"
  "NR_RRCReconfiguration-v1530-IEs.h"
  "NR_RRCReconfiguration-v1540-IEs.c"
  "NR_RRCReconfiguration-v1540-IEs.h"
  "NR_RRCReconfiguration-v1560-IEs.c"
  "NR_RRCReconfiguration-v1560-IEs.h"
  "NR_RRCReconfiguration-v1610-IEs.c"
  "NR_RRCReconfiguration-v1610-IEs.h"
  "NR_RRCReconfiguration-v1700-IEs.c"
  "NR_RRCReconfiguration-v1700-IEs.h"
  "NR_RRCReconfiguration.c"
  "NR_RRCReconfiguration.h"
  "NR_RRCReconfigurationComplete-IEs.c"
  "NR_RRCReconfigurationComplete-IEs.h"
  "NR_RRCReconfigurationComplete-v1530-IEs.c"
  "NR_RRCReconfigurationComplete-v1530-IEs.h"
  "NR_RRCReconfigurationComplete-v1560-IEs.c"
  "NR_RRCReconfigurationComplete-v1560-IEs.h"
  "NR_RRCReconfigurationComplete-v1610-IEs.c"
  "NR_RRCReconfigurationComplete-v1610-IEs.h"
  "NR_RRCReconfigurationComplete-v1640-IEs.c"
  "NR_RRCReconfigurationComplete-v1640-IEs.h"
  "NR_RRCReconfigurationComplete-v1700-IEs.c"
  "NR_RRCReconfigurationComplete-v1700-IEs.h"
  "NR_RRCReconfigurationComplete-v1720-IEs.c"
  "NR_RRCReconfigurationComplete-v1720-IEs.h"
  "NR_RRCReconfigurationComplete.c"
  "NR_RRCReconfigurationComplete.h"
  "NR_RRCReconfigurationCompleteSidelink-r16-IEs.c"
  "NR_RRCReconfigurationCompleteSidelink-r16-IEs.h"
  "NR_RRCReconfigurationCompleteSidelink-v1710-IEs.c"
  "NR_RRCReconfigurationCompleteSidelink-v1710-IEs.h"
  "NR_RRCReconfigurationCompleteSidelink-v1720-IEs.c"
  "NR_RRCReconfigurationCompleteSidelink-v1720-IEs.h"
  "NR_RRCReconfigurationCompleteSidelink.c"
  "NR_RRCReconfigurationCompleteSidelink.h"
  "NR_RRCReconfigurationFailureSidelink-r16-IEs.c"
  "NR_RRCReconfigurationFailureSidelink-r16-IEs.h"
  "NR_RRCReconfigurationFailureSidelink.c"
  "NR_RRCReconfigurationFailureSidelink.h"
  "NR_RRCReconfigurationSidelink-r16-IEs.c"
  "NR_RRCReconfigurationSidelink-r16-IEs.h"
  "NR_RRCReconfigurationSidelink-v1700-IEs.c"
  "NR_RRCReconfigurationSidelink-v1700-IEs.h"
  "NR_RRCReconfigurationSidelink.c"
  "NR_RRCReconfigurationSidelink.h"
  "NR_RRCReestablishment-IEs.c"
  "NR_RRCReestablishment-IEs.h"
  "NR_RRCReestablishment-v1700-IEs.c"
  "NR_RRCReestablishment-v1700-IEs.h"
  "NR_RRCReestablishment.c"
  "NR_RRCReestablishment.h"
  "NR_RRCReestablishmentComplete-IEs.c"
  "NR_RRCReestablishmentComplete-IEs.h"
  "NR_RRCReestablishmentComplete-v1610-IEs.c"
  "NR_RRCReestablishmentComplete-v1610-IEs.h"
  "NR_RRCReestablishmentComplete.c"
  "NR_RRCReestablishmentComplete.h"
  "NR_RRCReestablishmentRequest-IEs.c"
  "NR_RRCReestablishmentRequest-IEs.h"
  "NR_RRCReestablishmentRequest.c"
  "NR_RRCReestablishmentRequest.h"
  "NR_RRCReject-IEs.c"
  "NR_RRCReject-IEs.h"
  "NR_RRCReject.c"
  "NR_RRCReject.h"
  "NR_RRCRelease-IEs.c"
  "NR_RRCRelease-IEs.h"
  "NR_RRCRelease-v1540-IEs.c"
  "NR_RRCRelease-v1540-IEs.h"
  "NR_RRCRelease-v1610-IEs.c"
  "NR_RRCRelease-v1610-IEs.h"
  "NR_RRCRelease-v1650-IEs.c"
  "NR_RRCRelease-v1650-IEs.h"
  "NR_RRCRelease-v1710-IEs.c"
  "NR_RRCRelease-v1710-IEs.h"
  "NR_RRCRelease.c"
  "NR_RRCRelease.h"
  "NR_RRCResume-IEs.c"
  "NR_RRCResume-IEs.h"
  "NR_RRCResume-v1560-IEs.c"
  "NR_RRCResume-v1560-IEs.h"
  "NR_RRCResume-v1610-IEs.c"
  "NR_RRCResume-v1610-IEs.h"
  "NR_RRCResume-v1700-IEs.c"
  "NR_RRCResume-v1700-IEs.h"
  "NR_RRCResume.c"
  "NR_RRCResume.h"
  "NR_RRCResumeComplete-IEs.c"
  "NR_RRCResumeComplete-IEs.h"
  "NR_RRCResumeComplete-v1610-IEs.c"
  "NR_RRCResumeComplete-v1610-IEs.h"
  "NR_RRCResumeComplete-v1640-IEs.c"
  "NR_RRCResumeComplete-v1640-IEs.h"
  "NR_RRCResumeComplete-v1700-IEs.c"
  "NR_RRCResumeComplete-v1700-IEs.h"
  "NR_RRCResumeComplete-v1720-IEs.c"
  "NR_RRCResumeComplete-v1720-IEs.h"
  "NR_RRCResumeComplete.c"
  "NR_RRCResumeComplete.h"
  "NR_RRCResumeRequest-IEs.c"
  "NR_RRCResumeRequest-IEs.h"
  "NR_RRCResumeRequest.c"
  "NR_RRCResumeRequest.h"
  "NR_RRCResumeRequest1-IEs.c"
  "NR_RRCResumeRequest1-IEs.h"
  "NR_RRCResumeRequest1.c"
  "NR_RRCResumeRequest1.h"
  "NR_RRCSetup-IEs.c"
  "NR_RRCSetup-IEs.h"
  "NR_RRCSetup-v1700-IEs.c"
  "NR_RRCSetup-v1700-IEs.h"
  "NR_RRCSetup.c"
  "NR_RRCSetup.h"
  "NR_RRCSetupComplete-IEs.c"
  "NR_RRCSetupComplete-IEs.h"
  "NR_RRCSetupComplete-v1610-IEs.c"
  "NR_RRCSetupComplete-v1610-IEs.h"
  "NR_RRCSetupComplete-v1690-IEs.c"
  "NR_RRCSetupComplete-v1690-IEs.h"
  "NR_RRCSetupComplete-v1700-IEs.c"
  "NR_RRCSetupComplete-v1700-IEs.h"
  "NR_RRCSetupComplete.c"
  "NR_RRCSetupComplete.h"
  "NR_RRCSetupRequest-IEs.c"
  "NR_RRCSetupRequest-IEs.h"
  "NR_RRCSetupRequest.c"
  "NR_RRCSetupRequest.h"
  "NR_RRCSystemInfoRequest-IEs.c"
  "NR_RRCSystemInfoRequest-IEs.h"
  "NR_RRCSystemInfoRequest.c"
  "NR_RRCSystemInfoRequest.h"
  "NR_RRM-Config.c"
  "NR_RRM-Config.h"
  "NR_RRM-MeasRelaxationReportingConfig-r17.c"
  "NR_RRM-MeasRelaxationReportingConfig-r17.h"
  "NR_RSRP-ChangeThreshold-r17.c"
  "NR_RSRP-ChangeThreshold-r17.h"
  "NR_RSRP-Range.c"
  "NR_RSRP-Range.h"
  "NR_RSRP-RangeEUTRA.c"
  "NR_RSRP-RangeEUTRA.h"
  "NR_RSRQ-Range.c"
  "NR_RSRQ-Range.h"
  "NR_RSRQ-RangeEUTRA-r16.c"
  "NR_RSRQ-RangeEUTRA-r16.h"
  "NR_RSRQ-RangeEUTRA.c"
  "NR_RSRQ-RangeEUTRA.h"
  "NR_RSSI-PeriodicityAndOffset-r16.c"
  "NR_RSSI-PeriodicityAndOffset-r16.h"
  "NR_RSSI-Range-r16.c"
  "NR_RSSI-Range-r16.h"
  "NR_RSSI-ResourceConfigCLI-r16.c"
  "NR_RSSI-ResourceConfigCLI-r16.h"
  "NR_RSSI-ResourceId-r16.c"
  "NR_RSSI-ResourceId-r16.h"
  "NR_RSSI-ResourceListConfigCLI-r16.c"
  "NR_RSSI-ResourceListConfigCLI-r16.h"
  "NR_RadioBearerConfig.c"
  "NR_RadioBearerConfig.h"
  "NR_RadioLinkMonitoringConfig.c"
  "NR_RadioLinkMonitoringConfig.h"
  "NR_RadioLinkMonitoringRS-Id.c"
  "NR_RadioLinkMonitoringRS-Id.h"
  "NR_RadioLinkMonitoringRS.c"
  "NR_RadioLinkMonitoringRS.h"
  "NR_RangeToBestCell.c"
  "NR_RangeToBestCell.h"
  "NR_RateMatchPattern.c"
  "NR_RateMatchPattern.h"
  "NR_RateMatchPatternGroup.c"
  "NR_RateMatchPatternGroup.h"
  "NR_RateMatchPatternId.c"
  "NR_RateMatchPatternId.h"
  "NR_RateMatchPatternLTE-CRS.c"
  "NR_RateMatchPatternLTE-CRS.h"
  "NR_ReconfigurationWithSync.c"
  "NR_ReconfigurationWithSync.h"
  "NR_RedCap-ConfigCommonSIB-r17.c"
  "NR_RedCap-ConfigCommonSIB-r17.h"
  "NR_RedCapParameters-r17.c"
  "NR_RedCapParameters-r17.h"
  "NR_RedirectedCarrierInfo-EUTRA.c"
  "NR_RedirectedCarrierInfo-EUTRA.h"
  "NR_RedirectedCarrierInfo.c"
  "NR_RedirectedCarrierInfo.h"
  "NR_ReducedAggregatedBandwidth-r17.c"
  "NR_ReducedAggregatedBandwidth-r17.h"
  "NR_ReducedAggregatedBandwidth.c"
  "NR_ReducedAggregatedBandwidth.h"
  "NR_ReducedMaxBW-FRx-r16.c"
  "NR_ReducedMaxBW-FRx-r16.h"
  "NR_ReducedMaxCCs-r16.c"
  "NR_ReducedMaxCCs-r16.h"
  "NR_ReestabNCellInfo.c"
  "NR_ReestabNCellInfo.h"
  "NR_ReestabNCellInfoList.c"
  "NR_ReestabNCellInfoList.h"
  "NR_ReestabUE-Identity.c"
  "NR_ReestabUE-Identity.h"
  "NR_ReestablishmentCause.c"
  "NR_ReestablishmentCause.h"
  "NR_ReestablishmentInfo.c"
  "NR_ReestablishmentInfo.h"
  "NR_ReferenceLocation-r17.c"
  "NR_ReferenceLocation-r17.h"
  "NR_ReferenceSignalConfig.c"
  "NR_ReferenceSignalConfig.h"
  "NR_ReferenceTime-r16.c"
  "NR_ReferenceTime-r16.h"
  "NR_ReferenceTimeInfo-r16.c"
  "NR_ReferenceTimeInfo-r16.h"
  "NR_RegisteredAMF.c"
  "NR_RegisteredAMF.h"
  "NR_RejectWaitTime.c"
  "NR_RejectWaitTime.h"
  "NR_RelayParameters-r17.c"
  "NR_RelayParameters-r17.h"
  "NR_RelaysTriggeredList-r17.c"
  "NR_RelaysTriggeredList-r17.h"
  "NR_ReleasePreference-r16.c"
  "NR_ReleasePreference-r16.h"
  "NR_ReleasePreferenceConfig-r16.c"
  "NR_ReleasePreferenceConfig-r16.h"
  "NR_RemoteUEInformationSidelink-r17-IEs.c"
  "NR_RemoteUEInformationSidelink-r17-IEs.h"
  "NR_RemoteUEInformationSidelink-r17.c"
  "NR_RemoteUEInformationSidelink-r17.h"
  "NR_RepFactorAndTimeGap-r17.c"
  "NR_RepFactorAndTimeGap-r17.h"
  "NR_RepetitionSchemeConfig-r16.c"
  "NR_RepetitionSchemeConfig-r16.h"
  "NR_RepetitionSchemeConfig-v1630.c"
  "NR_RepetitionSchemeConfig-v1630.h"
  "NR_ReportCGI-EUTRA.c"
  "NR_ReportCGI-EUTRA.h"
  "NR_ReportCGI.c"
  "NR_ReportCGI.h"
  "NR_ReportConfigId.c"
  "NR_ReportConfigId.h"
  "NR_ReportConfigInterRAT.c"
  "NR_ReportConfigInterRAT.h"
  "NR_ReportConfigNR-SL-r16.c"
  "NR_ReportConfigNR-SL-r16.h"
  "NR_ReportConfigNR.c"
  "NR_ReportConfigNR.h"
  "NR_ReportConfigToAddMod.c"
  "NR_ReportConfigToAddMod.h"
  "NR_ReportConfigToAddModList.c"
  "NR_ReportConfigToAddModList.h"
  "NR_ReportConfigToRemoveList.c"
  "NR_ReportConfigToRemoveList.h"
  "NR_ReportInterval.c"
  "NR_ReportInterval.h"
  "NR_ReportSFTD-EUTRA.c"
  "NR_ReportSFTD-EUTRA.h"
  "NR_ReportSFTD-NR.c"
  "NR_ReportSFTD-NR.h"
  "NR_ReportUplinkTxDirectCurrentMoreCarrier-r17.c"
  "NR_ReportUplinkTxDirectCurrentMoreCarrier-r17.h"
  "NR_ReselectionThreshold.c"
  "NR_ReselectionThreshold.h"
  "NR_ReselectionThresholdQ.c"
  "NR_ReselectionThresholdQ.h"
  "NR_ResultsPerCSI-RS-Index.c"
  "NR_ResultsPerCSI-RS-Index.h"
  "NR_ResultsPerCSI-RS-IndexList.c"
  "NR_ResultsPerCSI-RS-IndexList.h"
  "NR_ResultsPerSSB-Index.c"
  "NR_ResultsPerSSB-Index.h"
  "NR_ResultsPerSSB-IndexIdle-r16.c"
  "NR_ResultsPerSSB-IndexIdle-r16.h"
  "NR_ResultsPerSSB-IndexList-r16.c"
  "NR_ResultsPerSSB-IndexList-r16.h"
  "NR_ResultsPerSSB-IndexList.c"
  "NR_ResultsPerSSB-IndexList.h"
  "NR_ResumeCause.c"
  "NR_ResumeCause.h"
  "NR_RxTxPeriodical-r17.c"
  "NR_RxTxPeriodical-r17.h"
  "NR_RxTxReportInterval-r17.c"
  "NR_RxTxReportInterval-r17.h"
  "NR_RxTxTimeDiff-r17.c"
  "NR_RxTxTimeDiff-r17.h"
  "NR_S-NSSAI.c"
  "NR_S-NSSAI.h"
  "NR_SBAS-ID-r16.c"
  "NR_SBAS-ID-r16.h"
  "NR_SBCCH-SL-BCH-Message.c"
  "NR_SBCCH-SL-BCH-Message.h"
  "NR_SBCCH-SL-BCH-MessageType.c"
  "NR_SBCCH-SL-BCH-MessageType.h"
  "NR_SCCH-Message.c"
  "NR_SCCH-Message.h"
  "NR_SCCH-MessageType.c"
  "NR_SCCH-MessageType.h"
  "NR_SCG-DeactivationPreferenceConfig-r17.c"
  "NR_SCG-DeactivationPreferenceConfig-r17.h"
  "NR_SCGFailureInformation-IEs.c"
  "NR_SCGFailureInformation-IEs.h"
  "NR_SCGFailureInformation-v1590-IEs.c"
  "NR_SCGFailureInformation-v1590-IEs.h"
  "NR_SCGFailureInformation.c"
  "NR_SCGFailureInformation.h"
  "NR_SCGFailureInformationEUTRA-IEs.c"
  "NR_SCGFailureInformationEUTRA-IEs.h"
  "NR_SCGFailureInformationEUTRA-v1590-IEs.c"
  "NR_SCGFailureInformationEUTRA-v1590-IEs.h"
  "NR_SCGFailureInformationEUTRA.c"
  "NR_SCGFailureInformationEUTRA.h"
  "NR_SCS-SpecificCarrier.c"
  "NR_SCS-SpecificCarrier.h"
  "NR_SCS-SpecificDuration-r17.c"
  "NR_SCS-SpecificDuration-r17.h"
  "NR_SCellActivationRS-Config-r17.c"
  "NR_SCellActivationRS-Config-r17.h"
  "NR_SCellActivationRS-ConfigId-r17.c"
  "NR_SCellActivationRS-ConfigId-r17.h"
  "NR_SCellConfig.c"
  "NR_SCellConfig.h"
  "NR_SCellIndex.c"
  "NR_SCellIndex.h"
  "NR_SCellSIB20-r17.c"
  "NR_SCellSIB20-r17.h"
  "NR_SDAP-Config.c"
  "NR_SDAP-Config.h"
  "NR_SDAP-Parameters.c"
  "NR_SDAP-Parameters.h"
  "NR_SDT-CG-Config-r17.c"
  "NR_SDT-CG-Config-r17.h"
  "NR_SDT-Config-r17.c"
  "NR_SDT-Config-r17.h"
  "NR_SDT-ConfigCommonSIB-r17.c"
  "NR_SDT-ConfigCommonSIB-r17.h"
  "NR_SDT-MAC-PHY-CG-Config-r17.c"
  "NR_SDT-MAC-PHY-CG-Config-r17.h"
  "NR_SFTD-FrequencyList-EUTRA.c"
  "NR_SFTD-FrequencyList-EUTRA.h"
  "NR_SFTD-FrequencyList-NR.c"
  "NR_SFTD-FrequencyList-NR.h"
  "NR_SHR-Cause-r17.c"
  "NR_SHR-Cause-r17.h"
  "NR_SI-RequestConfig.c"
  "NR_SI-RequestConfig.h"
  "NR_SI-RequestResources.c"
  "NR_SI-RequestResources.h"
  "NR_SI-SchedulingInfo-v1700.c"
  "NR_SI-SchedulingInfo-v1700.h"
  "NR_SI-SchedulingInfo.c"
  "NR_SI-SchedulingInfo.h"
  "NR_SIB-Mapping-v1700.c"
  "NR_SIB-Mapping-v1700.h"
  "NR_SIB-Mapping.c"
  "NR_SIB-Mapping.h"
  "NR_SIB-ReqInfo-r16.c"
  "NR_SIB-ReqInfo-r16.h"
  "NR_SIB-Type-r17.c"
  "NR_SIB-Type-r17.h"
  "NR_SIB-TypeInfo-v1700.c"
  "NR_SIB-TypeInfo-v1700.h"
  "NR_SIB-TypeInfo.c"
  "NR_SIB-TypeInfo.h"
  "NR_SIB1-v1610-IEs.c"
  "NR_SIB1-v1610-IEs.h"
  "NR_SIB1-v1630-IEs.c"
  "NR_SIB1-v1630-IEs.h"
  "NR_SIB1-v1700-IEs.c"
  "NR_SIB1-v1700-IEs.h"
  "NR_SIB1.c"
  "NR_SIB1.h"
  "NR_SIB10-r16.c"
  "NR_SIB10-r16.h"
  "NR_SIB11-r16.c"
  "NR_SIB11-r16.h"
  "NR_SIB12-IEs-r16.c"
  "NR_SIB12-IEs-r16.h"
  "NR_SIB12-r16.c"
  "NR_SIB12-r16.h"
  "NR_SIB13-r16.c"
  "NR_SIB13-r16.h"
  "NR_SIB14-r16.c"
  "NR_SIB14-r16.h"
  "NR_SIB15-r17.c"
  "NR_SIB15-r17.h"
  "NR_SIB16-r17.c"
  "NR_SIB16-r17.h"
  "NR_SIB17-IEs-r17.c"
  "NR_SIB17-IEs-r17.h"
  "NR_SIB17-r17.c"
  "NR_SIB17-r17.h"
  "NR_SIB18-r17.c"
  "NR_SIB18-r17.h"
  "NR_SIB19-r17.c"
  "NR_SIB19-r17.h"
  "NR_SIB2.c"
  "NR_SIB2.h"
  "NR_SIB20-r17.c"
  "NR_SIB20-r17.h"
  "NR_SIB21-r17.c"
  "NR_SIB21-r17.h"
  "NR_SIB3.c"
  "NR_SIB3.h"
  "NR_SIB4.c"
  "NR_SIB4.h"
  "NR_SIB5.c"
  "NR_SIB5.h"
  "NR_SIB6.c"
  "NR_SIB6.h"
  "NR_SIB7.c"
  "NR_SIB7.h"
  "NR_SIB8.c"
  "NR_SIB8.h"
  "NR_SIB9.c"
  "NR_SIB9.h"
  "NR_SIBpos-r16.c"
  "NR_SIBpos-r16.h"
  "NR_SINR-Range.c"
  "NR_SINR-Range.h"
  "NR_SINR-RangeEUTRA.c"
  "NR_SINR-RangeEUTRA.h"
  "NR_SK-Counter.c"
  "NR_SK-Counter.h"
  "NR_SL-AccessInfo-L2U2N-r17.c"
  "NR_SL-AccessInfo-L2U2N-r17.h"
  "NR_SL-BWP-Config-r16.c"
  "NR_SL-BWP-Config-r16.h"
  "NR_SL-BWP-ConfigCommon-r16.c"
  "NR_SL-BWP-ConfigCommon-r16.h"
  "NR_SL-BWP-DiscPoolConfig-r17.c"
  "NR_SL-BWP-DiscPoolConfig-r17.h"
  "NR_SL-BWP-DiscPoolConfigCommon-r17.c"
  "NR_SL-BWP-DiscPoolConfigCommon-r17.h"
  "NR_SL-BWP-Generic-r16.c"
  "NR_SL-BWP-Generic-r16.h"
  "NR_SL-BWP-PoolConfig-r16.c"
  "NR_SL-BWP-PoolConfig-r16.h"
  "NR_SL-BWP-PoolConfigCommon-r16.c"
  "NR_SL-BWP-PoolConfigCommon-r16.h"
  "NR_SL-BetaOffsets-r16.c"
  "NR_SL-BetaOffsets-r16.h"
  "NR_SL-CBR-CommonTxConfigList-r16.c"
  "NR_SL-CBR-CommonTxConfigList-r16.h"
  "NR_SL-CBR-LevelsConfig-r16.c"
  "NR_SL-CBR-LevelsConfig-r16.h"
  "NR_SL-CBR-PSSCH-TxConfig-r16.c"
  "NR_SL-CBR-PSSCH-TxConfig-r16.h"
  "NR_SL-CBR-PriorityTxConfigList-r16.c"
  "NR_SL-CBR-PriorityTxConfigList-r16.h"
  "NR_SL-CBR-PriorityTxConfigList-v1650.c"
  "NR_SL-CBR-PriorityTxConfigList-v1650.h"
  "NR_SL-CBR-r16.c"
  "NR_SL-CBR-r16.h"
  "NR_SL-CG-MaxTransNum-r16.c"
  "NR_SL-CG-MaxTransNum-r16.h"
  "NR_SL-CG-MaxTransNumList-r16.c"
  "NR_SL-CG-MaxTransNumList-r16.h"
  "NR_SL-CSI-RS-Config-r16.c"
  "NR_SL-CSI-RS-Config-r16.h"
  "NR_SL-ConfigCommonNR-r16.c"
  "NR_SL-ConfigCommonNR-r16.h"
  "NR_SL-ConfigDedicatedEUTRA-Info-r16.c"
  "NR_SL-ConfigDedicatedEUTRA-Info-r16.h"
  "NR_SL-ConfigDedicatedNR-r16.c"
  "NR_SL-ConfigDedicatedNR-r16.h"
  "NR_SL-ConfigIndexCG-r16.c"
  "NR_SL-ConfigIndexCG-r16.h"
  "NR_SL-ConfiguredGrantConfig-r16.c"
  "NR_SL-ConfiguredGrantConfig-r16.h"
  "NR_SL-ConfiguredGrantConfigList-r16.c"
  "NR_SL-ConfiguredGrantConfigList-r16.h"
  "NR_SL-DRX-Config-r17.c"
  "NR_SL-DRX-Config-r17.h"
  "NR_SL-DRX-ConfigGC-BC-r17.c"
  "NR_SL-DRX-ConfigGC-BC-r17.h"
  "NR_SL-DRX-ConfigUC-Info-r17.c"
  "NR_SL-DRX-ConfigUC-Info-r17.h"
  "NR_SL-DRX-ConfigUC-SemiStatic-r17.c"
  "NR_SL-DRX-ConfigUC-SemiStatic-r17.h"
  "NR_SL-DRX-ConfigUC-r17.c"
  "NR_SL-DRX-ConfigUC-r17.h"
  "NR_SL-DRX-GC-BC-QoS-r17.c"
  "NR_SL-DRX-GC-BC-QoS-r17.h"
  "NR_SL-DRX-GC-Generic-r17.c"
  "NR_SL-DRX-GC-Generic-r17.h"
  "NR_SL-DestinationIdentity-r16.c"
  "NR_SL-DestinationIdentity-r16.h"
  "NR_SL-DestinationIndex-r16.c"
  "NR_SL-DestinationIndex-r16.h"
  "NR_SL-DiscConfig-r17.c"
  "NR_SL-DiscConfig-r17.h"
  "NR_SL-DiscConfigCommon-r17.c"
  "NR_SL-DiscConfigCommon-r17.h"
  "NR_SL-EUTRA-AnchorCarrierFreqList-r16.c"
  "NR_SL-EUTRA-AnchorCarrierFreqList-r16.h"
  "NR_SL-EventTriggerConfig-r16.c"
  "NR_SL-EventTriggerConfig-r16.h"
  "NR_SL-Failure-r16.c"
  "NR_SL-Failure-r16.h"
  "NR_SL-FailureList-r16.c"
  "NR_SL-FailureList-r16.h"
  "NR_SL-Freq-Id-r16.c"
  "NR_SL-Freq-Id-r16.h"
  "NR_SL-FreqConfig-r16.c"
  "NR_SL-FreqConfig-r16.h"
  "NR_SL-FreqConfigCommon-r16.c"
  "NR_SL-FreqConfigCommon-r16.h"
  "NR_SL-InterUE-CoordinationConfig-r17.c"
  "NR_SL-InterUE-CoordinationConfig-r17.h"
  "NR_SL-InterUE-CoordinationScheme1-r17.c"
  "NR_SL-InterUE-CoordinationScheme1-r17.h"
  "NR_SL-InterUE-CoordinationScheme2-r17.c"
  "NR_SL-InterUE-CoordinationScheme2-r17.h"
  "NR_SL-InterestedFreqList-r16.c"
  "NR_SL-InterestedFreqList-r16.h"
  "NR_SL-L2RelayUE-Config-r17.c"
  "NR_SL-L2RelayUE-Config-r17.h"
  "NR_SL-L2RemoteUE-Config-r17.c"
  "NR_SL-L2RemoteUE-Config-r17.h"
  "NR_SL-LatencyBoundIUC-Report-r17.c"
  "NR_SL-LatencyBoundIUC-Report-r17.h"
  "NR_SL-LogicalChannelConfig-r16.c"
  "NR_SL-LogicalChannelConfig-r16.h"
  "NR_SL-LogicalChannelConfigPC5-r16.c"
  "NR_SL-LogicalChannelConfigPC5-r16.h"
  "NR_SL-MappedQoS-FlowsListDedicated-r16.c"
  "NR_SL-MappedQoS-FlowsListDedicated-r16.h"
  "NR_SL-MappingToAddMod-r17.c"
  "NR_SL-MappingToAddMod-r17.h"
  "NR_SL-MeasConfig-r16.c"
  "NR_SL-MeasConfig-r16.h"
  "NR_SL-MeasConfigCommon-r16.c"
  "NR_SL-MeasConfigCommon-r16.h"
  "NR_SL-MeasConfigInfo-r16.c"
  "NR_SL-MeasConfigInfo-r16.h"
  "NR_SL-MeasId-r16.c"
  "NR_SL-MeasId-r16.h"
  "NR_SL-MeasIdInfo-r16.c"
  "NR_SL-MeasIdInfo-r16.h"
  "NR_SL-MeasIdList-r16.c"
  "NR_SL-MeasIdList-r16.h"
  "NR_SL-MeasIdToRemoveList-r16.c"
  "NR_SL-MeasIdToRemoveList-r16.h"
  "NR_SL-MeasObject-r16.c"
  "NR_SL-MeasObject-r16.h"
  "NR_SL-MeasObjectId-r16.c"
  "NR_SL-MeasObjectId-r16.h"
  "NR_SL-MeasObjectInfo-r16.c"
  "NR_SL-MeasObjectInfo-r16.h"
  "NR_SL-MeasObjectList-r16.c"
  "NR_SL-MeasObjectList-r16.h"
  "NR_SL-MeasObjectToRemoveList-r16.c"
  "NR_SL-MeasObjectToRemoveList-r16.h"
  "NR_SL-MeasQuantityResult-r16.c"
  "NR_SL-MeasQuantityResult-r16.h"
  "NR_SL-MeasReportQuantity-r16.c"
  "NR_SL-MeasReportQuantity-r16.h"
  "NR_SL-MeasResult-r16.c"
  "NR_SL-MeasResult-r16.h"
  "NR_SL-MeasResultListRelay-r17.c"
  "NR_SL-MeasResultListRelay-r17.h"
  "NR_SL-MeasResultRelay-r17.c"
  "NR_SL-MeasResultRelay-r17.h"
  "NR_SL-MeasResults-r16.c"
  "NR_SL-MeasResults-r16.h"
  "NR_SL-MeasTriggerQuantity-r16.c"
  "NR_SL-MeasTriggerQuantity-r16.h"
  "NR_SL-MinMaxMCS-Config-r16.c"
  "NR_SL-MinMaxMCS-Config-r16.h"
  "NR_SL-MinMaxMCS-List-r16.c"
  "NR_SL-MinMaxMCS-List-r16.h"
  "NR_SL-NR-AnchorCarrierFreqList-r16.c"
  "NR_SL-NR-AnchorCarrierFreqList-r16.h"
  "NR_SL-PBPS-CPS-Config-r17.c"
  "NR_SL-PBPS-CPS-Config-r17.h"
  "NR_SL-PDCP-Config-r16.c"
  "NR_SL-PDCP-Config-r16.h"
  "NR_SL-PDCP-ConfigPC5-r16.c"
  "NR_SL-PDCP-ConfigPC5-r16.h"
  "NR_SL-PHY-MAC-RLC-Config-r16.c"
  "NR_SL-PHY-MAC-RLC-Config-r16.h"
  "NR_SL-PHY-MAC-RLC-Config-v1700.c"
  "NR_SL-PHY-MAC-RLC-Config-v1700.h"
  "NR_SL-PQFI-r16.c"
  "NR_SL-PQFI-r16.h"
  "NR_SL-PQI-r16.c"
  "NR_SL-PQI-r16.h"
  "NR_SL-PSBCH-Config-r16.c"
  "NR_SL-PSBCH-Config-r16.h"
  "NR_SL-PSCCH-Config-r16.c"
  "NR_SL-PSCCH-Config-r16.h"
  "NR_SL-PSFCH-Config-r16.c"
  "NR_SL-PSFCH-Config-r16.h"
  "NR_SL-PSSCH-Config-r16.c"
  "NR_SL-PSSCH-Config-r16.h"
  "NR_SL-PSSCH-TxConfig-r16.c"
  "NR_SL-PSSCH-TxConfig-r16.h"
  "NR_SL-PSSCH-TxConfigList-r16.c"
  "NR_SL-PSSCH-TxConfigList-r16.h"
  "NR_SL-PSSCH-TxParameters-r16.c"
  "NR_SL-PSSCH-TxParameters-r16.h"
  "NR_SL-PTRS-Config-r16.c"
  "NR_SL-PTRS-Config-r16.h"
  "NR_SL-PagingIdentityRemoteUE-r17.c"
  "NR_SL-PagingIdentityRemoteUE-r17.h"
  "NR_SL-PagingInfo-RemoteUE-r17.c"
  "NR_SL-PagingInfo-RemoteUE-r17.h"
  "NR_SL-PathSwitchConfig-r17.c"
  "NR_SL-PathSwitchConfig-r17.h"
  "NR_SL-PeriodCG-r16.c"
  "NR_SL-PeriodCG-r16.h"
  "NR_SL-PeriodicalReportConfig-r16.c"
  "NR_SL-PeriodicalReportConfig-r16.h"
  "NR_SL-PowerControl-r16.c"
  "NR_SL-PowerControl-r16.h"
  "NR_SL-PreconfigGeneral-r16.c"
  "NR_SL-PreconfigGeneral-r16.h"
  "NR_SL-PreconfigurationNR-r16.c"
  "NR_SL-PreconfigurationNR-r16.h"
  "NR_SL-PriorityTxConfigIndex-r16.c"
  "NR_SL-PriorityTxConfigIndex-r16.h"
  "NR_SL-PriorityTxConfigIndex-v1650.c"
  "NR_SL-PriorityTxConfigIndex-v1650.h"
  "NR_SL-QoS-FlowIdentity-r16.c"
  "NR_SL-QoS-FlowIdentity-r16.h"
  "NR_SL-QoS-Info-r16.c"
  "NR_SL-QoS-Info-r16.h"
  "NR_SL-QoS-Profile-r16.c"
  "NR_SL-QoS-Profile-r16.h"
  "NR_SL-QuantityConfig-r16.c"
  "NR_SL-QuantityConfig-r16.h"
  "NR_SL-RLC-BearerConfig-r16.c"
  "NR_SL-RLC-BearerConfig-r16.h"
  "NR_SL-RLC-BearerConfigIndex-r16.c"
  "NR_SL-RLC-BearerConfigIndex-r16.h"
  "NR_SL-RLC-ChannelConfig-r17.c"
  "NR_SL-RLC-ChannelConfig-r17.h"
  "NR_SL-RLC-ChannelConfigPC5-r17.c"
  "NR_SL-RLC-ChannelConfigPC5-r17.h"
  "NR_SL-RLC-ChannelID-r17.c"
  "NR_SL-RLC-ChannelID-r17.h"
  "NR_SL-RLC-Config-r16.c"
  "NR_SL-RLC-Config-r16.h"
  "NR_SL-RLC-ConfigPC5-r16.c"
  "NR_SL-RLC-ConfigPC5-r16.h"
  "NR_SL-RLC-ModeIndication-r16.c"
  "NR_SL-RLC-ModeIndication-r16.h"
  "NR_SL-RS-Type-r16.c"
  "NR_SL-RS-Type-r16.h"
  "NR_SL-RSRP-Range-r16.c"
  "NR_SL-RSRP-Range-r16.h"
  "NR_SL-RadioBearerConfig-r16.c"
  "NR_SL-RadioBearerConfig-r16.h"
  "NR_SL-RelayUE-Config-r17.c"
  "NR_SL-RelayUE-Config-r17.h"
  "NR_SL-RemoteUE-Config-r17.c"
  "NR_SL-RemoteUE-Config-r17.h"
  "NR_SL-RemoteUE-RB-Identity-r17.c"
  "NR_SL-RemoteUE-RB-Identity-r17.h"
  "NR_SL-RemoteUE-ToAddMod-r17.c"
  "NR_SL-RemoteUE-ToAddMod-r17.h"
  "NR_SL-ReportConfig-r16.c"
  "NR_SL-ReportConfig-r16.h"
  "NR_SL-ReportConfigId-r16.c"
  "NR_SL-ReportConfigId-r16.h"
  "NR_SL-ReportConfigInfo-r16.c"
  "NR_SL-ReportConfigInfo-r16.h"
  "NR_SL-ReportConfigList-r16.c"
  "NR_SL-ReportConfigList-r16.h"
  "NR_SL-ReportConfigToRemoveList-r16.c"
  "NR_SL-ReportConfigToRemoveList-r16.h"
  "NR_SL-RequestedSIB-List-r17.c"
  "NR_SL-RequestedSIB-List-r17.h"
  "NR_SL-ReselectionConfig-r17.c"
  "NR_SL-ReselectionConfig-r17.h"
  "NR_SL-ResourcePool-r16.c"
  "NR_SL-ResourcePool-r16.h"
  "NR_SL-ResourcePoolConfig-r16.c"
  "NR_SL-ResourcePoolConfig-r16.h"
  "NR_SL-ResourcePoolID-r16.c"
  "NR_SL-ResourcePoolID-r16.h"
  "NR_SL-ResourceReservePeriod-r16.c"
  "NR_SL-ResourceReservePeriod-r16.h"
  "NR_SL-RoHC-Profiles-r16.c"
  "NR_SL-RoHC-Profiles-r16.h"
  "NR_SL-RxDRX-Report-v1700.c"
  "NR_SL-RxDRX-Report-v1700.h"
  "NR_SL-RxDRX-ReportList-v1700.c"
  "NR_SL-RxDRX-ReportList-v1700.h"
  "NR_SL-RxInterestedGC-BC-Dest-r17.c"
  "NR_SL-RxInterestedGC-BC-Dest-r17.h"
  "NR_SL-RxInterestedGC-BC-DestList-r17.c"
  "NR_SL-RxInterestedGC-BC-DestList-r17.h"
  "NR_SL-SDAP-Config-r16.c"
  "NR_SL-SDAP-Config-r16.h"
  "NR_SL-SDAP-ConfigPC5-r16.c"
  "NR_SL-SDAP-ConfigPC5-r16.h"
  "NR_SL-SIB-ReqInfo-r17.c"
  "NR_SL-SIB-ReqInfo-r17.h"
  "NR_SL-SRAP-Config-r17.c"
  "NR_SL-SRAP-Config-r17.h"
  "NR_SL-SSB-TimeAllocation-r16.c"
  "NR_SL-SSB-TimeAllocation-r16.h"
  "NR_SL-ScheduledConfig-r16.c"
  "NR_SL-ScheduledConfig-r16.h"
  "NR_SL-SelectionWindowConfig-r16.c"
  "NR_SL-SelectionWindowConfig-r16.h"
  "NR_SL-SelectionWindowList-r16.c"
  "NR_SL-SelectionWindowList-r16.h"
  "NR_SL-ServingCellInfo-r17.c"
  "NR_SL-ServingCellInfo-r17.h"
  "NR_SL-SourceIdentity-r17.c"
  "NR_SL-SourceIdentity-r17.h"
  "NR_SL-SyncAllowed-r16.c"
  "NR_SL-SyncAllowed-r16.h"
  "NR_SL-SyncConfig-r16.c"
  "NR_SL-SyncConfig-r16.h"
  "NR_SL-SyncConfigList-r16.c"
  "NR_SL-SyncConfigList-r16.h"
  "NR_SL-Thres-RSRP-List-r16.c"
  "NR_SL-Thres-RSRP-List-r16.h"
  "NR_SL-Thres-RSRP-r16.c"
  "NR_SL-Thres-RSRP-r16.h"
  "NR_SL-ThresholdRSRP-Condition1-B-1-r17.c"
  "NR_SL-ThresholdRSRP-Condition1-B-1-r17.h"
  "NR_SL-TimeOffsetEUTRA-r16.c"
  "NR_SL-TimeOffsetEUTRA-r16.h"
  "NR_SL-TrafficPatternInfo-r16.c"
  "NR_SL-TrafficPatternInfo-r16.h"
  "NR_SL-TxConfigIndex-r16.c"
  "NR_SL-TxConfigIndex-r16.h"
  "NR_SL-TxInterestedFreqList-r16.c"
  "NR_SL-TxInterestedFreqList-r16.h"
  "NR_SL-TxPercentageConfig-r16.c"
  "NR_SL-TxPercentageConfig-r16.h"
  "NR_SL-TxPercentageList-r16.c"
  "NR_SL-TxPercentageList-r16.h"
  "NR_SL-TxPoolDedicated-r16.c"
  "NR_SL-TxPoolDedicated-r16.h"
  "NR_SL-TxPower-r16.c"
  "NR_SL-TxPower-r16.h"
  "NR_SL-TxProfile-r17.c"
  "NR_SL-TxProfile-r17.h"
  "NR_SL-TxProfileList-r17.c"
  "NR_SL-TxProfileList-r17.h"
  "NR_SL-TxResourceReq-r16.c"
  "NR_SL-TxResourceReq-r16.h"
  "NR_SL-TxResourceReq-v1700.c"
  "NR_SL-TxResourceReq-v1700.h"
  "NR_SL-TxResourceReqCommRelay-r17.c"
  "NR_SL-TxResourceReqCommRelay-r17.h"
  "NR_SL-TxResourceReqCommRelayInfo-r17.c"
  "NR_SL-TxResourceReqCommRelayInfo-r17.h"
  "NR_SL-TxResourceReqDisc-r17.c"
  "NR_SL-TxResourceReqDisc-r17.h"
  "NR_SL-TxResourceReqL2U2N-Relay-r17.c"
  "NR_SL-TxResourceReqL2U2N-Relay-r17.h"
  "NR_SL-TxResourceReqList-r16.c"
  "NR_SL-TxResourceReqList-r16.h"
  "NR_SL-TxResourceReqList-v1700.c"
  "NR_SL-TxResourceReqList-v1700.h"
  "NR_SL-TxResourceReqListCommRelay-r17.c"
  "NR_SL-TxResourceReqListCommRelay-r17.h"
  "NR_SL-TxResourceReqListDisc-r17.c"
  "NR_SL-TxResourceReqListDisc-r17.h"
  "NR_SL-TypeTxSync-r16.c"
  "NR_SL-TypeTxSync-r16.h"
  "NR_SL-UE-AssistanceInformationNR-r16.c"
  "NR_SL-UE-AssistanceInformationNR-r16.h"
  "NR_SL-UE-SelectedConfig-r16.c"
  "NR_SL-UE-SelectedConfig-r16.h"
  "NR_SL-UE-SelectedConfigRP-r16.c"
  "NR_SL-UE-SelectedConfigRP-r16.h"
  "NR_SL-ZoneConfig-r16.c"
  "NR_SL-ZoneConfig-r16.h"
  "NR_SL-ZoneConfigMCR-r16.c"
  "NR_SL-ZoneConfigMCR-r16.h"
  "NR_SLRB-Config-r16.c"
  "NR_SLRB-Config-r16.h"
  "NR_SLRB-PC5-ConfigIndex-r16.c"
  "NR_SLRB-PC5-ConfigIndex-r16.h"
  "NR_SLRB-Uu-ConfigIndex-r16.c"
  "NR_SLRB-Uu-ConfigIndex-r16.h"
  "NR_SN-FieldLengthAM.c"
  "NR_SN-FieldLengthAM.h"
  "NR_SN-FieldLengthUM.c"
  "NR_SN-FieldLengthUM.h"
  "NR_SNPN-AccessInfo-r17.c"
  "NR_SNPN-AccessInfo-r17.h"
  "NR_SON-Parameters-r16.c"
  "NR_SON-Parameters-r16.h"
  "NR_SPS-Config.c"
  "NR_SPS-Config.h"
  "NR_SPS-ConfigDeactivationState-r16.c"
  "NR_SPS-ConfigDeactivationState-r16.h"
  "NR_SPS-ConfigDeactivationStateList-r16.c"
  "NR_SPS-ConfigDeactivationStateList-r16.h"
  "NR_SPS-ConfigIndex-r16.c"
  "NR_SPS-ConfigIndex-r16.h"
  "NR_SPS-ConfigMulticastToAddModList-r17.c"
  "NR_SPS-ConfigMulticastToAddModList-r17.h"
  "NR_SPS-ConfigMulticastToReleaseList-r17.c"
  "NR_SPS-ConfigMulticastToReleaseList-r17.h"
  "NR_SPS-ConfigToAddModList-r16.c"
  "NR_SPS-ConfigToAddModList-r16.h"
  "NR_SPS-ConfigToReleaseList-r16.c"
  "NR_SPS-ConfigToReleaseList-r16.h"
  "NR_SPS-PUCCH-AN-List-r16.c"
  "NR_SPS-PUCCH-AN-List-r16.h"
  "NR_SPS-PUCCH-AN-r16.c"
  "NR_SPS-PUCCH-AN-r16.h"
  "NR_SRB-Identity-v1700.c"
  "NR_SRB-Identity-v1700.h"
  "NR_SRB-Identity.c"
  "NR_SRB-Identity.h"
  "NR_SRB-ToAddMod.c"
  "NR_SRB-ToAddMod.h"
  "NR_SRB-ToAddModList.c"
  "NR_SRB-ToAddModList.h"
  "NR_SRI-PUSCH-PowerControl.c"
  "NR_SRI-PUSCH-PowerControl.h"
  "NR_SRI-PUSCH-PowerControlId.c"
  "NR_SRI-PUSCH-PowerControlId.h"
  "NR_SRS-AllPosResources-r16.c"
  "NR_SRS-AllPosResources-r16.h"
  "NR_SRS-AllPosResourcesRRC-Inactive-r17.c"
  "NR_SRS-AllPosResourcesRRC-Inactive-r17.h"
  "NR_SRS-CC-SetIndex.c"
  "NR_SRS-CC-SetIndex.h"
  "NR_SRS-CarrierSwitching.c"
  "NR_SRS-CarrierSwitching.h"
  "NR_SRS-Config.c"
  "NR_SRS-Config.h"
  "NR_SRS-PathlossReferenceRS-Id-r16.c"
  "NR_SRS-PathlossReferenceRS-Id-r16.h"
  "NR_SRS-PeriodicityAndOffset-r16.c"
  "NR_SRS-PeriodicityAndOffset-r16.h"
  "NR_SRS-PeriodicityAndOffset.c"
  "NR_SRS-PeriodicityAndOffset.h"
  "NR_SRS-PeriodicityAndOffsetExt-r16.c"
  "NR_SRS-PeriodicityAndOffsetExt-r16.h"
  "NR_SRS-PosConfig-r17.c"
  "NR_SRS-PosConfig-r17.h"
  "NR_SRS-PosRRC-Inactive-r17.c"
  "NR_SRS-PosRRC-Inactive-r17.h"
  "NR_SRS-PosRRC-InactiveConfig-r17.c"
  "NR_SRS-PosRRC-InactiveConfig-r17.h"
  "NR_SRS-PosResource-r16.c"
  "NR_SRS-PosResource-r16.h"
  "NR_SRS-PosResourceAP-r16.c"
  "NR_SRS-PosResourceAP-r16.h"
  "NR_SRS-PosResourceId-r16.c"
  "NR_SRS-PosResourceId-r16.h"
  "NR_SRS-PosResourceSP-r16.c"
  "NR_SRS-PosResourceSP-r16.h"
  "NR_SRS-PosResourceSet-r16.c"
  "NR_SRS-PosResourceSet-r16.h"
  "NR_SRS-PosResourceSetId-r16.c"
  "NR_SRS-PosResourceSetId-r16.h"
  "NR_SRS-PosResources-r16.c"
  "NR_SRS-PosResources-r16.h"
  "NR_SRS-RSRP-Range-r16.c"
  "NR_SRS-RSRP-Range-r16.h"
  "NR_SRS-RSRP-TriggeredList-r16.c"
  "NR_SRS-RSRP-TriggeredList-r16.h"
  "NR_SRS-Resource.c"
  "NR_SRS-Resource.h"
  "NR_SRS-ResourceConfigCLI-r16.c"
  "NR_SRS-ResourceConfigCLI-r16.h"
  "NR_SRS-ResourceId.c"
  "NR_SRS-ResourceId.h"
  "NR_SRS-ResourceListConfigCLI-r16.c"
  "NR_SRS-ResourceListConfigCLI-r16.h"
  "NR_SRS-ResourceSet.c"
  "NR_SRS-ResourceSet.h"
  "NR_SRS-ResourceSetId.c"
  "NR_SRS-ResourceSetId.h"
  "NR_SRS-Resources.c"
  "NR_SRS-Resources.h"
  "NR_SRS-SpatialRelationInfo.c"
  "NR_SRS-SpatialRelationInfo.h"
  "NR_SRS-SpatialRelationInfoPos-r16.c"
  "NR_SRS-SpatialRelationInfoPos-r16.h"
  "NR_SRS-SwitchingAffectedBandsNR-r17.c"
  "NR_SRS-SwitchingAffectedBandsNR-r17.h"
  "NR_SRS-SwitchingTimeEUTRA.c"
  "NR_SRS-SwitchingTimeEUTRA.h"
  "NR_SRS-SwitchingTimeNR.c"
  "NR_SRS-SwitchingTimeNR.h"
  "NR_SRS-TPC-CommandConfig.c"
  "NR_SRS-TPC-CommandConfig.h"
  "NR_SRS-TPC-PDCCH-Config.c"
  "NR_SRS-TPC-PDCCH-Config.h"
  "NR_SS-RSSI-Measurement.c"
  "NR_SS-RSSI-Measurement.h"
  "NR_SSB-ConfigMobility.c"
  "NR_SSB-ConfigMobility.h"
  "NR_SSB-Configuration-r16.c"
  "NR_SSB-Configuration-r16.h"
  "NR_SSB-Index.c"
  "NR_SSB-Index.h"
  "NR_SSB-InfoNcell-r16.c"
  "NR_SSB-InfoNcell-r16.h"
  "NR_SSB-MTC-AdditionalPCI-r17.c"
  "NR_SSB-MTC-AdditionalPCI-r17.h"
  "NR_SSB-MTC.c"
  "NR_SSB-MTC.h"
  "NR_SSB-MTC2-LP-r16.c"
  "NR_SSB-MTC2-LP-r16.h"
  "NR_SSB-MTC2.c"
  "NR_SSB-MTC2.h"
  "NR_SSB-MTC3-r16.c"
  "NR_SSB-MTC3-r16.h"
  "NR_SSB-MTC3List-r16.c"
  "NR_SSB-MTC3List-r16.h"
  "NR_SSB-MTC4-r17.c"
  "NR_SSB-MTC4-r17.h"
  "NR_SSB-MTC4List-r17.c"
  "NR_SSB-MTC4List-r17.h"
  "NR_SSB-PositionQCL-Cell-r17.c"
  "NR_SSB-PositionQCL-Cell-r17.h"
  "NR_SSB-PositionQCL-CellList-r17.c"
  "NR_SSB-PositionQCL-CellList-r17.h"
  "NR_SSB-PositionQCL-CellsToAddMod-r16.c"
  "NR_SSB-PositionQCL-CellsToAddMod-r16.h"
  "NR_SSB-PositionQCL-CellsToAddModList-r16.c"
  "NR_SSB-PositionQCL-CellsToAddModList-r16.h"
  "NR_SSB-PositionQCL-Relation-r16.c"
  "NR_SSB-PositionQCL-Relation-r16.h"
  "NR_SSB-PositionQCL-Relation-r17.c"
  "NR_SSB-PositionQCL-Relation-r17.h"
  "NR_SSB-ToMeasure.c"
  "NR_SSB-ToMeasure.h"
  "NR_ScalingFactorSidelink-r16.c"
  "NR_ScalingFactorSidelink-r16.h"
  "NR_SchedulingInfo.c"
  "NR_SchedulingInfo.h"
  "NR_SchedulingInfo2-r17.c"
  "NR_SchedulingInfo2-r17.h"
  "NR_SchedulingRequestConfig-v1700.c"
  "NR_SchedulingRequestConfig-v1700.h"
  "NR_SchedulingRequestConfig.c"
  "NR_SchedulingRequestConfig.h"
  "NR_SchedulingRequestId.c"
  "NR_SchedulingRequestId.h"
  "NR_SchedulingRequestResourceConfig.c"
  "NR_SchedulingRequestResourceConfig.h"
  "NR_SchedulingRequestResourceConfigExt-v1610.c"
  "NR_SchedulingRequestResourceConfigExt-v1610.h"
  "NR_SchedulingRequestResourceConfigExt-v1700.c"
  "NR_SchedulingRequestResourceConfigExt-v1700.h"
  "NR_SchedulingRequestResourceId.c"
  "NR_SchedulingRequestResourceId.h"
  "NR_SchedulingRequestToAddMod.c"
  "NR_SchedulingRequestToAddMod.h"
  "NR_SchedulingRequestToAddModExt-v1700.c"
  "NR_SchedulingRequestToAddModExt-v1700.h"
  "NR_ScramblingId.c"
  "NR_ScramblingId.h"
  "NR_SearchSpace.c"
  "NR_SearchSpace.h"
  "NR_SearchSpaceExt-r16.c"
  "NR_SearchSpaceExt-r16.h"
  "NR_SearchSpaceExt-v1700.c"
  "NR_SearchSpaceExt-v1700.h"
  "NR_SearchSpaceId.c"
  "NR_SearchSpaceId.h"
  "NR_SearchSpaceSwitchConfig-r16.c"
  "NR_SearchSpaceSwitchConfig-r16.h"
  "NR_SearchSpaceSwitchConfig-r17.c"
  "NR_SearchSpaceSwitchConfig-r17.h"
  "NR_SearchSpaceSwitchTrigger-r16.c"
  "NR_SearchSpaceSwitchTrigger-r16.h"
  "NR_SearchSpaceZero.c"
  "NR_SearchSpaceZero.h"
  "NR_SecurityAlgorithmConfig.c"
  "NR_SecurityAlgorithmConfig.h"
  "NR_SecurityConfig.c"
  "NR_SecurityConfig.h"
  "NR_SecurityConfigSMC.c"
  "NR_SecurityConfigSMC.h"
  "NR_SecurityModeCommand-IEs.c"
  "NR_SecurityModeCommand-IEs.h"
  "NR_SecurityModeCommand.c"
  "NR_SecurityModeCommand.h"
  "NR_SecurityModeComplete-IEs.c"
  "NR_SecurityModeComplete-IEs.h"
  "NR_SecurityModeComplete.c"
  "NR_SecurityModeComplete.h"
  "NR_SecurityModeFailure-IEs.c"
  "NR_SecurityModeFailure-IEs.h"
  "NR_SecurityModeFailure.c"
  "NR_SecurityModeFailure.h"
  "NR_SelectedBandEntriesMN.c"
  "NR_SelectedBandEntriesMN.h"
  "NR_SemiStaticChannelAccessConfig-r16.c"
  "NR_SemiStaticChannelAccessConfig-r16.h"
  "NR_SemiStaticChannelAccessConfigUE-r17.c"
  "NR_SemiStaticChannelAccessConfigUE-r17.h"
  "NR_Sensor-LocationInfo-r16.c"
  "NR_Sensor-LocationInfo-r16.h"
  "NR_Sensor-NameList-r16.c"
  "NR_Sensor-NameList-r16.h"
  "NR_ServCellIndex.c"
  "NR_ServCellIndex.h"
  "NR_ServCellInfoListMCG-EUTRA-r16.c"
  "NR_ServCellInfoListMCG-EUTRA-r16.h"
  "NR_ServCellInfoListMCG-NR-r16.c"
  "NR_ServCellInfoListMCG-NR-r16.h"
  "NR_ServCellInfoListSCG-EUTRA-r16.c"
  "NR_ServCellInfoListSCG-EUTRA-r16.h"
  "NR_ServCellInfoListSCG-NR-r16.c"
  "NR_ServCellInfoListSCG-NR-r16.h"
  "NR_ServCellInfoXCG-EUTRA-r16.c"
  "NR_ServCellInfoXCG-EUTRA-r16.h"
  "NR_ServCellInfoXCG-NR-r16.c"
  "NR_ServCellInfoXCG-NR-r16.h"
  "NR_ServingAdditionalPCIIndex-r17.c"
  "NR_ServingAdditionalPCIIndex-r17.h"
  "NR_ServingCellAndBWP-Id-r17.c"
  "NR_ServingCellAndBWP-Id-r17.h"
  "NR_ServingCellConfig.c"
  "NR_ServingCellConfig.h"
  "NR_ServingCellConfigCommon.c"
  "NR_ServingCellConfigCommon.h"
  "NR_ServingCellConfigCommonSIB.c"
  "NR_ServingCellConfigCommonSIB.h"
  "NR_SetupRelease.c"
  "NR_SetupRelease.h"
  "NR_SharedSpectrumChAccessParamsPerBand-r16.c"
  "NR_SharedSpectrumChAccessParamsPerBand-r16.h"
  "NR_SharedSpectrumChAccessParamsPerBand-v1630.c"
  "NR_SharedSpectrumChAccessParamsPerBand-v1630.h"
  "NR_SharedSpectrumChAccessParamsPerBand-v1640.c"
  "NR_SharedSpectrumChAccessParamsPerBand-v1640.h"
  "NR_SharedSpectrumChAccessParamsPerBand-v1650.c"
  "NR_SharedSpectrumChAccessParamsPerBand-v1650.h"
  "NR_SharedSpectrumChAccessParamsPerBand-v1710.c"
  "NR_SharedSpectrumChAccessParamsPerBand-v1710.h"
  "NR_ShortI-RNTI-Value.c"
  "NR_ShortI-RNTI-Value.h"
  "NR_ShortMAC-I.c"
  "NR_ShortMAC-I.h"
  "NR_SidelinkParameters-r16.c"
  "NR_SidelinkParameters-r16.h"
  "NR_SidelinkParametersEUTRA-r16.c"
  "NR_SidelinkParametersEUTRA-r16.h"
  "NR_SidelinkParametersNR-r16.c"
  "NR_SidelinkParametersNR-r16.h"
  "NR_SidelinkPreconfigNR-r16.c"
  "NR_SidelinkPreconfigNR-r16.h"
  "NR_SidelinkUEInformationNR-r16-IEs.c"
  "NR_SidelinkUEInformationNR-r16-IEs.h"
  "NR_SidelinkUEInformationNR-r16.c"
  "NR_SidelinkUEInformationNR-r16.h"
  "NR_SidelinkUEInformationNR-v1700-IEs.c"
  "NR_SidelinkUEInformationNR-v1700-IEs.h"
  "NR_SimulSRS-ForAntennaSwitching-r16.c"
  "NR_SimulSRS-ForAntennaSwitching-r16.h"
  "NR_SimultaneousRxTxPerBandPair.c"
  "NR_SimultaneousRxTxPerBandPair.h"
  "NR_SliceCellListNR-r17.c"
  "NR_SliceCellListNR-r17.h"
  "NR_SliceInfo-r17.c"
  "NR_SliceInfo-r17.h"
  "NR_SliceInfoDedicated-r17.c"
  "NR_SliceInfoDedicated-r17.h"
  "NR_SliceInfoList-r17.c"
  "NR_SliceInfoList-r17.h"
  "NR_SliceInfoListDedicated-r17.c"
  "NR_SliceInfoListDedicated-r17.h"
  "NR_SlotBased-r16.c"
  "NR_SlotBased-r16.h"
  "NR_SlotBased-v1630.c"
  "NR_SlotBased-v1630.h"
  "NR_SlotFormatCombination.c"
  "NR_SlotFormatCombination.h"
  "NR_SlotFormatCombinationId.c"
  "NR_SlotFormatCombinationId.h"
  "NR_SlotFormatCombinationsPerCell.c"
  "NR_SlotFormatCombinationsPerCell.h"
  "NR_SlotFormatIndicator.c"
  "NR_SlotFormatIndicator.h"
  "NR_SpCellConfig.c"
  "NR_SpCellConfig.h"
  "NR_SpatialRelationInfo-PDC-r17.c"
  "NR_SpatialRelationInfo-PDC-r17.h"
  "NR_SpatialRelations.c"
  "NR_SpatialRelations.h"
  "NR_SpatialRelationsSRS-Pos-r16.c"
  "NR_SpatialRelationsSRS-Pos-r16.h"
  "NR_SpeedStateScaleFactors.c"
  "NR_SpeedStateScaleFactors.h"
  "NR_SubSlot-Config-r16.c"
  "NR_SubSlot-Config-r16.h"
  "NR_SubcarrierSpacing.c"
  "NR_SubcarrierSpacing.h"
  "NR_SubgroupConfig-r17.c"
  "NR_SubgroupConfig-r17.h"
  "NR_SuccessHO-Config-r17.c"
  "NR_SuccessHO-Config-r17.h"
  "NR_SuccessHO-Report-r17.c"
  "NR_SuccessHO-Report-r17.h"
  "NR_SupportedBandUTRA-FDD-r16.c"
  "NR_SupportedBandUTRA-FDD-r16.h"
  "NR_SupportedBandwidth-v1700.c"
  "NR_SupportedBandwidth-v1700.h"
  "NR_SupportedBandwidth.c"
  "NR_SupportedBandwidth.h"
  "NR_SupportedCSI-RS-Resource.c"
  "NR_SupportedCSI-RS-Resource.h"
  "NR_SuspendConfig.c"
  "NR_SuspendConfig.h"
  "NR_SystemInformation-IEs.c"
  "NR_SystemInformation-IEs.h"
  "NR_SystemInformation.c"
  "NR_SystemInformation.h"
  "NR_T-Offset-r16.c"
  "NR_T-Offset-r16.h"
  "NR_T-PollRetransmit.c"
  "NR_T-PollRetransmit.h"
  "NR_T-Reassembly.c"
  "NR_T-Reassembly.h"
  "NR_T-ReassemblyExt-r17.c"
  "NR_T-ReassemblyExt-r17.h"
  "NR_T-Reselection.c"
  "NR_T-Reselection.h"
  "NR_T-StatusProhibit-v1610.c"
  "NR_T-StatusProhibit-v1610.h"
  "NR_T-StatusProhibit.c"
  "NR_T-StatusProhibit.h"
  "NR_T312-r16.c"
  "NR_T312-r16.h"
  "NR_T316-r16.c"
  "NR_T316-r16.h"
  "NR_TA-Info-r17.c"
  "NR_TA-Info-r17.h"
  "NR_TAG-Config.c"
  "NR_TAG-Config.h"
  "NR_TAG-Id.c"
  "NR_TAG-Id.h"
  "NR_TAG.c"
  "NR_TAG.h"
  "NR_TAR-Config-r17.c"
  "NR_TAR-Config-r17.h"
  "NR_TCI-ActivatedConfig-r17.c"
  "NR_TCI-ActivatedConfig-r17.h"
  "NR_TCI-State.c"
  "NR_TCI-State.h"
  "NR_TCI-StateId.c"
  "NR_TCI-StateId.h"
  "NR_TCI-UL-State-Id-r17.c"
  "NR_TCI-UL-State-Id-r17.h"
  "NR_TCI-UL-State-r17.c"
  "NR_TCI-UL-State-r17.h"
  "NR_TDD-UL-DL-ConfigCommon.c"
  "NR_TDD-UL-DL-ConfigCommon.h"
  "NR_TDD-UL-DL-ConfigDedicated-IAB-MT-r16.c"
  "NR_TDD-UL-DL-ConfigDedicated-IAB-MT-r16.h"
  "NR_TDD-UL-DL-ConfigDedicated.c"
  "NR_TDD-UL-DL-ConfigDedicated.h"
  "NR_TDD-UL-DL-Pattern.c"
  "NR_TDD-UL-DL-Pattern.h"
  "NR_TDD-UL-DL-SlotConfig-IAB-MT-r16.c"
  "NR_TDD-UL-DL-SlotConfig-IAB-MT-r16.h"
  "NR_TDD-UL-DL-SlotConfig.c"
  "NR_TDD-UL-DL-SlotConfig.h"
  "NR_TDD-UL-DL-SlotIndex.c"
  "NR_TDD-UL-DL-SlotIndex.h"
  "NR_TMGI-r17.c"
  "NR_TMGI-r17.h"
  "NR_TRS-ResourceSet-r17.c"
  "NR_TRS-ResourceSet-r17.h"
  "NR_ThresholdNR.c"
  "NR_ThresholdNR.h"
  "NR_TimeAlignmentTimer.c"
  "NR_TimeAlignmentTimer.h"
  "NR_TimeBetweenEvent-r17.c"
  "NR_TimeBetweenEvent-r17.h"
  "NR_TimeConnSourceDAPS-Failure-r17.c"
  "NR_TimeConnSourceDAPS-Failure-r17.h"
  "NR_TimeSinceCHO-Reconfig-r17.c"
  "NR_TimeSinceCHO-Reconfig-r17.h"
  "NR_TimeSinceFailure-r16.c"
  "NR_TimeSinceFailure-r16.h"
  "NR_TimeToTrigger.c"
  "NR_TimeToTrigger.h"
  "NR_TimeUntilReconnection-r16.c"
  "NR_TimeUntilReconnection-r16.h"
  "NR_TraceReference-r16.c"
  "NR_TraceReference-r16.h"
  "NR_TrackingAreaCode.c"
  "NR_TrackingAreaCode.h"
  "NR_TrackingAreaCodeList-r16.c"
  "NR_TrackingAreaCodeList-r16.h"
  "NR_TrackingAreaIdentity-r16.c"
  "NR_TrackingAreaIdentity-r16.h"
  "NR_TrackingAreaIdentityList-r16.c"
  "NR_TrackingAreaIdentityList-r16.h"
  "NR_TransmissionBandwidth-EUTRA-r16.c"
  "NR_TransmissionBandwidth-EUTRA-r16.h"
  "NR_TwoPUCCH-Grp-ConfigParams-r16.c"
  "NR_TwoPUCCH-Grp-ConfigParams-r16.h"
  "NR_TwoPUCCH-Grp-Configurations-r16.c"
  "NR_TwoPUCCH-Grp-Configurations-r16.h"
  "NR_TwoPUCCH-Grp-Configurations-r17.c"
  "NR_TwoPUCCH-Grp-Configurations-r17.h"
  "NR_Tx-PoolMeasList-r16.c"
  "NR_Tx-PoolMeasList-r16.h"
  "NR_UAC-AC1-SelectAssistInfo-r16.c"
  "NR_UAC-AC1-SelectAssistInfo-r16.h"
  "NR_UAC-AccessCategory1-SelectionAssistanceInfo.c"
  "NR_UAC-AccessCategory1-SelectionAssistanceInfo.h"
  "NR_UAC-BarringInfoSet-v1700.c"
  "NR_UAC-BarringInfoSet-v1700.h"
  "NR_UAC-BarringInfoSet.c"
  "NR_UAC-BarringInfoSet.h"
  "NR_UAC-BarringInfoSetIndex.c"
  "NR_UAC-BarringInfoSetIndex.h"
  "NR_UAC-BarringInfoSetList-v1700.c"
  "NR_UAC-BarringInfoSetList-v1700.h"
  "NR_UAC-BarringInfoSetList.c"
  "NR_UAC-BarringInfoSetList.h"
  "NR_UAC-BarringPerCat.c"
  "NR_UAC-BarringPerCat.h"
  "NR_UAC-BarringPerCatList.c"
  "NR_UAC-BarringPerCatList.h"
  "NR_UAC-BarringPerPLMN-List.c"
  "NR_UAC-BarringPerPLMN-List.h"
  "NR_UAC-BarringPerPLMN.c"
  "NR_UAC-BarringPerPLMN.h"
  "NR_UCI-OnPUSCH-DCI-0-2-r16.c"
  "NR_UCI-OnPUSCH-DCI-0-2-r16.h"
  "NR_UCI-OnPUSCH-ListDCI-0-1-r16.c"
  "NR_UCI-OnPUSCH-ListDCI-0-1-r16.h"
  "NR_UCI-OnPUSCH-ListDCI-0-2-r16.c"
  "NR_UCI-OnPUSCH-ListDCI-0-2-r16.h"
  "NR_UCI-OnPUSCH.c"
  "NR_UCI-OnPUSCH.h"
  "NR_UE-BasedPerfMeas-Parameters-r16.c"
  "NR_UE-BasedPerfMeas-Parameters-r16.h"
  "NR_UE-CapabilityRAT-Container.c"
  "NR_UE-CapabilityRAT-Container.h"
  "NR_UE-CapabilityRAT-ContainerList.c"
  "NR_UE-CapabilityRAT-ContainerList.h"
  "NR_UE-CapabilityRAT-Request.c"
  "NR_UE-CapabilityRAT-Request.h"
  "NR_UE-CapabilityRAT-RequestList.c"
  "NR_UE-CapabilityRAT-RequestList.h"
  "NR_UE-CapabilityRequestFilterCommon.c"
  "NR_UE-CapabilityRequestFilterCommon.h"
  "NR_UE-CapabilityRequestFilterNR-v1540.c"
  "NR_UE-CapabilityRequestFilterNR-v1540.h"
  "NR_UE-CapabilityRequestFilterNR-v1710.c"
  "NR_UE-CapabilityRequestFilterNR-v1710.h"
  "NR_UE-CapabilityRequestFilterNR.c"
  "NR_UE-CapabilityRequestFilterNR.h"
  "NR_UE-MRDC-Capability-v1560.c"
  "NR_UE-MRDC-Capability-v1560.h"
  "NR_UE-MRDC-Capability-v15g0.c"
  "NR_UE-MRDC-Capability-v15g0.h"
  "NR_UE-MRDC-Capability-v1610.c"
  "NR_UE-MRDC-Capability-v1610.h"
  "NR_UE-MRDC-Capability-v1700.c"
  "NR_UE-MRDC-Capability-v1700.h"
  "NR_UE-MRDC-Capability-v1730.c"
  "NR_UE-MRDC-Capability-v1730.h"
  "NR_UE-MRDC-Capability.c"
  "NR_UE-MRDC-Capability.h"
  "NR_UE-MRDC-CapabilityAddFRX-Mode.c"
  "NR_UE-MRDC-CapabilityAddFRX-Mode.h"
  "NR_UE-MRDC-CapabilityAddXDD-Mode-v1560.c"
  "NR_UE-MRDC-CapabilityAddXDD-Mode-v1560.h"
  "NR_UE-MRDC-CapabilityAddXDD-Mode.c"
  "NR_UE-MRDC-CapabilityAddXDD-Mode.h"
  "NR_UE-MeasurementsAvailable-r16.c"
  "NR_UE-MeasurementsAvailable-r16.h"
  "NR_UE-NR-Capability-v1530.c"
  "NR_UE-NR-Capability-v1530.h"
  "NR_UE-NR-Capability-v1540.c"
  "NR_UE-NR-Capability-v1540.h"
  "NR_UE-NR-Capability-v1550.c"
  "NR_UE-NR-Capability-v1550.h"
  "NR_UE-NR-Capability-v1560.c"
  "NR_UE-NR-Capability-v1560.h"
  "NR_UE-NR-Capability-v1570.c"
  "NR_UE-NR-Capability-v1570.h"
  "NR_UE-NR-Capability-v15c0.c"
  "NR_UE-NR-Capability-v15c0.h"
  "NR_UE-NR-Capability-v15g0.c"
  "NR_UE-NR-Capability-v15g0.h"
  "NR_UE-NR-Capability-v15j0.c"
  "NR_UE-NR-Capability-v15j0.h"
  "NR_UE-NR-Capability-v1610.c"
  "NR_UE-NR-Capability-v1610.h"
  "NR_UE-NR-Capability-v1640.c"
  "NR_UE-NR-Capability-v1640.h"
  "NR_UE-NR-Capability-v1650.c"
  "NR_UE-NR-Capability-v1650.h"
  "NR_UE-NR-Capability-v1690.c"
  "NR_UE-NR-Capability-v1690.h"
  "NR_UE-NR-Capability-v16a0.c"
  "NR_UE-NR-Capability-v16a0.h"
  "NR_UE-NR-Capability-v1700.c"
  "NR_UE-NR-Capability-v1700.h"
  "NR_UE-NR-Capability.c"
  "NR_UE-NR-Capability.h"
  "NR_UE-NR-CapabilityAddFRX-Mode-v1540.c"
  "NR_UE-NR-CapabilityAddFRX-Mode-v1540.h"
  "NR_UE-NR-CapabilityAddFRX-Mode-v1610.c"
  "NR_UE-NR-CapabilityAddFRX-Mode-v1610.h"
  "NR_UE-NR-CapabilityAddFRX-Mode.c"
  "NR_UE-NR-CapabilityAddFRX-Mode.h"
  "NR_UE-NR-CapabilityAddXDD-Mode-v1530.c"
  "NR_UE-NR-CapabilityAddXDD-Mode-v1530.h"
  "NR_UE-NR-CapabilityAddXDD-Mode.c"
  "NR_UE-NR-CapabilityAddXDD-Mode.h"
  "NR_UE-RadioPagingInfo-r17.c"
  "NR_UE-RadioPagingInfo-r17.h"
  "NR_UE-SidelinkCapabilityAddXDD-Mode-r16.c"
  "NR_UE-SidelinkCapabilityAddXDD-Mode-r16.h"
  "NR_UE-TimersAndConstants.c"
  "NR_UE-TimersAndConstants.h"
  "NR_UE-TimersAndConstantsRemoteUE-r17.c"
  "NR_UE-TimersAndConstantsRemoteUE-r17.h"
  "NR_UE-TxTEG-Association-r17.c"
  "NR_UE-TxTEG-Association-r17.h"
  "NR_UE-TxTEG-AssociationList-r17.c"
  "NR_UE-TxTEG-AssociationList-r17.h"
  "NR_UE-TxTEG-RequestUL-TDOA-Config-r17.c"
  "NR_UE-TxTEG-RequestUL-TDOA-Config-r17.h"
  "NR_UEAssistanceInformation-IEs.c"
  "NR_UEAssistanceInformation-IEs.h"
  "NR_UEAssistanceInformation-v1540-IEs.c"
  "NR_UEAssistanceInformation-v1540-IEs.h"
  "NR_UEAssistanceInformation-v1610-IEs.c"
  "NR_UEAssistanceInformation-v1610-IEs.h"
  "NR_UEAssistanceInformation-v1700-IEs.c"
  "NR_UEAssistanceInformation-v1700-IEs.h"
  "NR_UEAssistanceInformation.c"
  "NR_UEAssistanceInformation.h"
  "NR_UEAssistanceInformationSidelink-r17-IEs.c"
  "NR_UEAssistanceInformationSidelink-r17-IEs.h"
  "NR_UEAssistanceInformationSidelink-r17.c"
  "NR_UEAssistanceInformationSidelink-r17.h"
  "NR_UECapabilityEnquiry-IEs.c"
  "NR_UECapabilityEnquiry-IEs.h"
  "NR_UECapabilityEnquiry-v1560-IEs.c"
  "NR_UECapabilityEnquiry-v1560-IEs.h"
  "NR_UECapabilityEnquiry-v1610-IEs.c"
  "NR_UECapabilityEnquiry-v1610-IEs.h"
  "NR_UECapabilityEnquiry.c"
  "NR_UECapabilityEnquiry.h"
  "NR_UECapabilityEnquirySidelink-r16-IEs.c"
  "NR_UECapabilityEnquirySidelink-r16-IEs.h"
  "NR_UECapabilityEnquirySidelink.c"
  "NR_UECapabilityEnquirySidelink.h"
  "NR_UECapabilityInformation-IEs.c"
  "NR_UECapabilityInformation-IEs.h"
  "NR_UECapabilityInformation.c"
  "NR_UECapabilityInformation.h"
  "NR_UECapabilityInformationSidelink-r16-IEs.c"
  "NR_UECapabilityInformationSidelink-r16-IEs.h"
  "NR_UECapabilityInformationSidelink-v1700-IEs.c"
  "NR_UECapabilityInformationSidelink-v1700-IEs.h"
  "NR_UECapabilityInformationSidelink.c"
  "NR_UECapabilityInformationSidelink.h"
  "NR_UEInformationRequest-r16-IEs.c"
  "NR_UEInformationRequest-r16-IEs.h"
  "NR_UEInformationRequest-r16.c"
  "NR_UEInformationRequest-r16.h"
  "NR_UEInformationRequest-v1700-IEs.c"
  "NR_UEInformationRequest-v1700-IEs.h"
  "NR_UEInformationResponse-r16-IEs.c"
  "NR_UEInformationResponse-r16-IEs.h"
  "NR_UEInformationResponse-r16.c"
  "NR_UEInformationResponse-r16.h"
  "NR_UEInformationResponse-v1700-IEs.c"
  "NR_UEInformationResponse-v1700-IEs.h"
  "NR_UEPositioningAssistanceInfo-r17-IEs.c"
  "NR_UEPositioningAssistanceInfo-r17-IEs.h"
  "NR_UEPositioningAssistanceInfo-r17.c"
  "NR_UEPositioningAssistanceInfo-r17.h"
  "NR_UEPositioningAssistanceInfo-v1720-IEs.c"
  "NR_UEPositioningAssistanceInfo-v1720-IEs.h"
  "NR_UERadioAccessCapabilityInformation-IEs.c"
  "NR_UERadioAccessCapabilityInformation-IEs.h"
  "NR_UERadioAccessCapabilityInformation.c"
  "NR_UERadioAccessCapabilityInformation.h"
  "NR_UERadioPagingInformation-IEs.c"
  "NR_UERadioPagingInformation-IEs.h"
  "NR_UERadioPagingInformation-v15e0-IEs.c"
  "NR_UERadioPagingInformation-v15e0-IEs.h"
  "NR_UERadioPagingInformation-v1700-IEs.c"
  "NR_UERadioPagingInformation-v1700-IEs.h"
  "NR_UERadioPagingInformation.c"
  "NR_UERadioPagingInformation.h"
  "NR_UL-AM-RLC.c"
  "NR_UL-AM-RLC.h"
  "NR_UL-AccessConfigListDCI-0-1-r16.c"
  "NR_UL-AccessConfigListDCI-0-1-r16.h"
  "NR_UL-AccessConfigListDCI-0-1-r17.c"
  "NR_UL-AccessConfigListDCI-0-1-r17.h"
  "NR_UL-AccessConfigListDCI-0-2-r17.c"
  "NR_UL-AccessConfigListDCI-0-2-r17.h"
  "NR_UL-AccessConfigListDCI-1-1-r16.c"
  "NR_UL-AccessConfigListDCI-1-1-r16.h"
  "NR_UL-AccessConfigListDCI-1-1-r17.c"
  "NR_UL-AccessConfigListDCI-1-1-r17.h"
  "NR_UL-AccessConfigListDCI-1-2-r17.c"
  "NR_UL-AccessConfigListDCI-1-2-r17.h"
  "NR_UL-CCCH-Message.c"
  "NR_UL-CCCH-Message.h"
  "NR_UL-CCCH-MessageType.c"
  "NR_UL-CCCH-MessageType.h"
  "NR_UL-CCCH1-Message.c"
  "NR_UL-CCCH1-Message.h"
  "NR_UL-CCCH1-MessageType.c"
  "NR_UL-CCCH1-MessageType.h"
  "NR_UL-DCCH-Message.c"
  "NR_UL-DCCH-Message.h"
  "NR_UL-DCCH-MessageType.c"
  "NR_UL-DCCH-MessageType.h"
  "NR_UL-DataSplitThreshold.c"
  "NR_UL-DataSplitThreshold.h"
  "NR_UL-DelayValueConfig-r16.c"
  "NR_UL-DelayValueConfig-r16.h"
  "NR_UL-ExcessDelayConfig-r17.c"
  "NR_UL-ExcessDelayConfig-r17.h"
  "NR_UL-GapFR2-Config-r17.c"
  "NR_UL-GapFR2-Config-r17.h"
  "NR_UL-GapFR2-Preference-r17.c"
  "NR_UL-GapFR2-Preference-r17.h"
  "NR_UL-PDCP-DelayValueResult-r16.c"
  "NR_UL-PDCP-DelayValueResult-r16.h"
  "NR_UL-PDCP-DelayValueResultList-r16.c"
  "NR_UL-PDCP-DelayValueResultList-r16.h"
  "NR_UL-PDCP-ExcessDelayResult-r17.c"
  "NR_UL-PDCP-ExcessDelayResult-r17.h"
  "NR_UL-PDCP-ExcessDelayResultList-r17.c"
  "NR_UL-PDCP-ExcessDelayResultList-r17.h"
  "NR_UL-UM-RLC.c"
  "NR_UL-UM-RLC.h"
  "NR_ULDedicatedMessageSegment-r16-IEs.c"
  "NR_ULDedicatedMessageSegment-r16-IEs.h"
  "NR_ULDedicatedMessageSegment-r16.c"
  "NR_ULDedicatedMessageSegment-r16.h"
  "NR_ULInformationTransfer-IEs.c"
  "NR_ULInformationTransfer-IEs.h"
  "NR_ULInformationTransfer-v1700-IEs.c"
  "NR_ULInformationTransfer-v1700-IEs.h"
  "NR_ULInformationTransfer.c"
  "NR_ULInformationTransfer.h"
  "NR_ULInformationTransferIRAT-r16-IEs.c"
  "NR_ULInformationTransferIRAT-r16-IEs.h"
  "NR_ULInformationTransferIRAT-r16.c"
  "NR_ULInformationTransferIRAT-r16.h"
  "NR_ULInformationTransferMRDC-IEs.c"
  "NR_ULInformationTransferMRDC-IEs.h"
  "NR_ULInformationTransferMRDC.c"
  "NR_ULInformationTransferMRDC.h"
  "NR_ULTxSwitchingBandPair-r16.c"
  "NR_ULTxSwitchingBandPair-r16.h"
  "NR_ULTxSwitchingBandPair-v1700.c"
  "NR_ULTxSwitchingBandPair-v1700.h"
  "NR_UPInterruptionTimeAtHO-r17.c"
  "NR_UPInterruptionTimeAtHO-r17.h"
  "NR_UTRA-FDD-CellIndex-r16.c"
  "NR_UTRA-FDD-CellIndex-r16.h"
  "NR_UTRA-FDD-CellIndexList-r16.c"
  "NR_UTRA-FDD-CellIndexList-r16.h"
  "NR_UTRA-FDD-Parameters-r16.c"
  "NR_UTRA-FDD-Parameters-r16.h"
  "NR_UTRA-FDD-Q-OffsetRange-r16.c"
  "NR_UTRA-FDD-Q-OffsetRange-r16.h"
  "NR_Uplink-powerControl-r17.c"
  "NR_Uplink-powerControl-r17.h"
  "NR_Uplink-powerControlId-r17.c"
  "NR_Uplink-powerControlId-r17.h"
  "NR_UplinkCancellation-r16.c"
  "NR_UplinkCancellation-r16.h"
  "NR_UplinkConfig.c"
  "NR_UplinkConfig.h"
  "NR_UplinkConfigCommon-v1700.c"
  "NR_UplinkConfigCommon-v1700.h"
  "NR_UplinkConfigCommon.c"
  "NR_UplinkConfigCommon.h"
  "NR_UplinkConfigCommonSIB-v1700.c"
  "NR_UplinkConfigCommonSIB-v1700.h"
  "NR_UplinkConfigCommonSIB.c"
  "NR_UplinkConfigCommonSIB.h"
  "NR_UplinkDataCompression-r17.c"
  "NR_UplinkDataCompression-r17.h"
  "NR_UplinkHARQ-mode-r17.c"
  "NR_UplinkHARQ-mode-r17.h"
  "NR_UplinkTxDirectCurrentBWP.c"
  "NR_UplinkTxDirectCurrentBWP.h"
  "NR_UplinkTxDirectCurrentCarrierInfo-r16.c"
  "NR_UplinkTxDirectCurrentCarrierInfo-r16.h"
  "NR_UplinkTxDirectCurrentCell.c"
  "NR_UplinkTxDirectCurrentCell.h"
  "NR_UplinkTxDirectCurrentList.c"
  "NR_UplinkTxDirectCurrentList.h"
  "NR_UplinkTxDirectCurrentMoreCarrierList-r17.c"
  "NR_UplinkTxDirectCurrentMoreCarrierList-r17.h"
  "NR_UplinkTxDirectCurrentTwoCarrier-r16.c"
  "NR_UplinkTxDirectCurrentTwoCarrier-r16.h"
  "NR_UplinkTxDirectCurrentTwoCarrierInfo-r16.c"
  "NR_UplinkTxDirectCurrentTwoCarrierInfo-r16.h"
  "NR_UplinkTxDirectCurrentTwoCarrierList-r16.c"
  "NR_UplinkTxDirectCurrentTwoCarrierList-r16.h"
  "NR_UplinkTxSwitching-r16.c"
  "NR_UplinkTxSwitching-r16.h"
  "NR_UplinkTxSwitchingBandParameters-v1700.c"
  "NR_UplinkTxSwitchingBandParameters-v1700.h"
  "NR_Uu-RelayRLC-ChannelConfig-r17.c"
  "NR_Uu-RelayRLC-ChannelConfig-r17.h"
  "NR_Uu-RelayRLC-ChannelID-r17.c"
  "NR_Uu-RelayRLC-ChannelID-r17.h"
  "NR_UuMessageTransferSidelink-r17-IEs.c"
  "NR_UuMessageTransferSidelink-r17-IEs.h"
  "NR_UuMessageTransferSidelink-r17.c"
  "NR_UuMessageTransferSidelink-r17.h"
  "NR_ValidityArea-r16.c"
  "NR_ValidityArea-r16.h"
  "NR_ValidityAreaList-r16.c"
  "NR_ValidityAreaList-r16.h"
  "NR_ValidityCellList.c"
  "NR_ValidityCellList.h"
  "NR_VarConditionalReconfig.c"
  "NR_VarConditionalReconfig.h"
  "NR_VarConnEstFailReport-r16.c"
  "NR_VarConnEstFailReport-r16.h"
  "NR_VarConnEstFailReportList-r17.c"
  "NR_VarConnEstFailReportList-r17.h"
  "NR_VarLogMeasConfig-r16-IEs.c"
  "NR_VarLogMeasConfig-r16-IEs.h"
  "NR_VarLogMeasReport-r16.c"
  "NR_VarLogMeasReport-r16.h"
  "NR_VarMeasConfig.c"
  "NR_VarMeasConfig.h"
  "NR_VarMeasConfigSL-r16.c"
  "NR_VarMeasConfigSL-r16.h"
  "NR_VarMeasIdleConfig-r16.c"
  "NR_VarMeasIdleConfig-r16.h"
  "NR_VarMeasIdleReport-r16.c"
  "NR_VarMeasIdleReport-r16.h"
  "NR_VarMeasReport.c"
  "NR_VarMeasReport.h"
  "NR_VarMeasReportList.c"
  "NR_VarMeasReportList.h"
  "NR_VarMeasReportListSL-r16.c"
  "NR_VarMeasReportListSL-r16.h"
  "NR_VarMeasReportSL-r16.c"
  "NR_VarMeasReportSL-r16.h"
  "NR_VarMobilityHistoryReport-r16.c"
  "NR_VarMobilityHistoryReport-r16.h"
  "NR_VarMobilityHistoryReport-r17.c"
  "NR_VarMobilityHistoryReport-r17.h"
  "NR_VarPendingRNA-Update.c"
  "NR_VarPendingRNA-Update.h"
  "NR_VarRA-Report-r16.c"
  "NR_VarRA-Report-r16.h"
  "NR_VarRLF-Report-r16.c"
  "NR_VarRLF-Report-r16.h"
  "NR_VarResumeMAC-Input.c"
  "NR_VarResumeMAC-Input.h"
  "NR_VarShortMAC-Input.c"
  "NR_VarShortMAC-Input.h"
  "NR_VarSuccessHO-Report-r17-IEs.c"
  "NR_VarSuccessHO-Report-r17-IEs.h"
  "NR_VelocityStateVector-r17.c"
  "NR_VelocityStateVector-r17.h"
  "NR_VictimSystemType-r16.c"
  "NR_VictimSystemType-r16.h"
  "NR_VictimSystemType.c"
  "NR_VictimSystemType.h"
  "NR_VisitedCellInfo-r16.c"
  "NR_VisitedCellInfo-r16.h"
  "NR_VisitedCellInfoList-r16.c"
  "NR_VisitedCellInfoList-r16.h"
  "NR_VisitedPSCellInfo-r17.c"
  "NR_VisitedPSCellInfo-r17.h"
  "NR_VisitedPSCellInfoList-r17.c"
  "NR_VisitedPSCellInfoList-r17.h"
  "NR_WLAN-Identifiers-r16.c"
  "NR_WLAN-Identifiers-r16.h"
  "NR_WLAN-Name-r16.c"
  "NR_WLAN-Name-r16.h"
  "NR_WLAN-NameList-r16.c"
  "NR_WLAN-NameList-r16.h"
  "NR_WLAN-RSSI-Range-r16.c"
  "NR_WLAN-RSSI-Range-r16.h"
  "NR_WLAN-RTT-r16.c"
  "NR_WLAN-RTT-r16.h"
  "NR_WithinActiveTimeConfig-r16.c"
  "NR_WithinActiveTimeConfig-r16.h"
  "NR_ZP-CSI-RS-Resource.c"
  "NR_ZP-CSI-RS-Resource.h"
  "NR_ZP-CSI-RS-ResourceId.c"
  "NR_ZP-CSI-RS-ResourceId.h"
  "NR_ZP-CSI-RS-ResourceSet.c"
  "NR_ZP-CSI-RS-ResourceSet.h"
  "NR_ZP-CSI-RS-ResourceSetId.c"
  "NR_ZP-CSI-RS-ResourceSetId.h"
  "NR_asn_constant.h"
  "NULL.c"
  "NULL.h"
  "NULL_aper.c"
  "NULL_print.c"
  "NULL_rfill.c"
  "NULL_uper.c"
  "NULL_xer.c"
  "NativeEnumerated.c"
  "NativeEnumerated.h"
  "NativeEnumerated_aper.c"
  "NativeEnumerated_uper.c"
  "NativeEnumerated_xer.c"
  "NativeInteger.c"
  "NativeInteger.h"
  "NativeInteger_aper.c"
  "NativeInteger_print.c"
  "NativeInteger_rfill.c"
  "NativeInteger_uper.c"
  "NativeInteger_xer.c"
  "OBJECT_IDENTIFIER.c"
  "OBJECT_IDENTIFIER.h"
  "OBJECT_IDENTIFIER_print.c"
  "OBJECT_IDENTIFIER_rfill.c"
  "OBJECT_IDENTIFIER_xer.c"
  "OCTET_STRING.c"
  "OCTET_STRING.h"
  "OCTET_STRING_aper.c"
  "OCTET_STRING_print.c"
  "OCTET_STRING_rfill.c"
  "OCTET_STRING_uper.c"
  "OCTET_STRING_xer.c"
  "OPEN_TYPE.c"
  "OPEN_TYPE.h"
  "OPEN_TYPE_aper.c"
  "OPEN_TYPE_uper.c"
  "OPEN_TYPE_xer.c"
  "ObjectDescriptor.c"
  "ObjectDescriptor.h"
  "aper_decoder.c"
  "aper_decoder.h"
  "aper_encoder.c"
  "aper_encoder.h"
  "aper_opentype.c"
  "aper_opentype.h"
  "aper_support.c"
  "aper_support.h"
  "asn_SEQUENCE_OF.c"
  "asn_SEQUENCE_OF.h"
  "asn_SET_OF.c"
  "asn_SET_OF.h"
  "asn_application.c"
  "asn_application.h"
  "asn_bit_data.c"
  "asn_bit_data.h"
  "asn_codecs.h"
  "asn_codecs_prim.c"
  "asn_codecs_prim.h"
  "asn_codecs_prim_xer.c"
  "asn_config.h"
  "asn_internal.c"
  "asn_internal.h"
  "asn_ioc.h"
  "asn_random_fill.c"
  "asn_random_fill.h"
  "asn_system.h"
  "ber_tlv_length.c"
  "ber_tlv_length.h"
  "ber_tlv_tag.c"
  "ber_tlv_tag.h"
  "constr_CHOICE.c"
  "constr_CHOICE.h"
  "constr_CHOICE_aper.c"
  "constr_CHOICE_print.c"
  "constr_CHOICE_rfill.c"
  "constr_CHOICE_uper.c"
  "constr_CHOICE_xer.c"
  "constr_SEQUENCE.c"
  "constr_SEQUENCE.h"
  "constr_SEQUENCE_OF.c"
  "constr_SEQUENCE_OF.h"
  "constr_SEQUENCE_OF_aper.c"
  "constr_SEQUENCE_OF_uper.c"
  "constr_SEQUENCE_OF_xer.c"
  "constr_SEQUENCE_aper.c"
  "constr_SEQUENCE_print.c"
  "constr_SEQUENCE_rfill.c"
  "constr_SEQUENCE_uper.c"
  "constr_SEQUENCE_xer.c"
  "constr_SET_OF.c"
  "constr_SET_OF.h"
  "constr_SET_OF_aper.c"
  "constr_SET_OF_print.c"
  "constr_SET_OF_rfill.c"
  "constr_SET_OF_uper.c"
  "constr_SET_OF_xer.c"
  "constr_TYPE.c"
  "constr_TYPE.h"
  "constraints.c"
  "constraints.h"
  "per_decoder.c"
  "per_decoder.h"
  "per_encoder.c"
  "per_encoder.h"
  "per_opentype.c"
  "per_opentype.h"
  "per_support.c"
  "per_support.h"
  "uper_decoder.c"
  "uper_decoder.h"
  "uper_encoder.c"
  "uper_encoder.h"
  "uper_opentype.c"
  "uper_opentype.h"
  "uper_support.c"
  "uper_support.h"
  "xer_decoder.c"
  "xer_decoder.h"
  "xer_encoder.c"
  "xer_encoder.h"
  "xer_support.c"
  "xer_support.h"
)

# Per-language clean rules from dependency scanning.
foreach(lang )
  include(CMakeFiles/gen_nr_rrc_hdrs.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
