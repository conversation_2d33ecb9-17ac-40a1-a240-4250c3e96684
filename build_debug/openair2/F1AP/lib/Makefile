# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/oaiseu

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/oaiseu/build_debug

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/oaiseu/build_debug/CMakeFiles /home/<USER>/oaiseu/build_debug/openair2/F1AP/lib//CMakeFiles/progress.marks
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 openair2/F1AP/lib/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/oaiseu/build_debug/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 openair2/F1AP/lib/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 openair2/F1AP/lib/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 openair2/F1AP/lib/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/rule:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/rule
.PHONY : openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/rule

# Convenience name for target.
f1ap_lib: openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/rule
.PHONY : f1ap_lib

# fast build rule for target.
f1ap_lib/fast:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/build.make openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/build
.PHONY : f1ap_lib/fast

f1ap_interface_management.o: f1ap_interface_management.c.o
.PHONY : f1ap_interface_management.o

# target to build an object file
f1ap_interface_management.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/build.make openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/f1ap_interface_management.c.o
.PHONY : f1ap_interface_management.c.o

f1ap_interface_management.i: f1ap_interface_management.c.i
.PHONY : f1ap_interface_management.i

# target to preprocess a source file
f1ap_interface_management.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/build.make openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/f1ap_interface_management.c.i
.PHONY : f1ap_interface_management.c.i

f1ap_interface_management.s: f1ap_interface_management.c.s
.PHONY : f1ap_interface_management.s

# target to generate assembly for a file
f1ap_interface_management.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/build.make openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/f1ap_interface_management.c.s
.PHONY : f1ap_interface_management.c.s

f1ap_lib_common.o: f1ap_lib_common.c.o
.PHONY : f1ap_lib_common.o

# target to build an object file
f1ap_lib_common.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/build.make openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/f1ap_lib_common.c.o
.PHONY : f1ap_lib_common.c.o

f1ap_lib_common.i: f1ap_lib_common.c.i
.PHONY : f1ap_lib_common.i

# target to preprocess a source file
f1ap_lib_common.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/build.make openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/f1ap_lib_common.c.i
.PHONY : f1ap_lib_common.c.i

f1ap_lib_common.s: f1ap_lib_common.c.s
.PHONY : f1ap_lib_common.s

# target to generate assembly for a file
f1ap_lib_common.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/build.make openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/f1ap_lib_common.c.s
.PHONY : f1ap_lib_common.c.s

f1ap_rrc_message_transfer.o: f1ap_rrc_message_transfer.c.o
.PHONY : f1ap_rrc_message_transfer.o

# target to build an object file
f1ap_rrc_message_transfer.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/build.make openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/f1ap_rrc_message_transfer.c.o
.PHONY : f1ap_rrc_message_transfer.c.o

f1ap_rrc_message_transfer.i: f1ap_rrc_message_transfer.c.i
.PHONY : f1ap_rrc_message_transfer.i

# target to preprocess a source file
f1ap_rrc_message_transfer.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/build.make openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/f1ap_rrc_message_transfer.c.i
.PHONY : f1ap_rrc_message_transfer.c.i

f1ap_rrc_message_transfer.s: f1ap_rrc_message_transfer.c.s
.PHONY : f1ap_rrc_message_transfer.s

# target to generate assembly for a file
f1ap_rrc_message_transfer.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/build.make openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/f1ap_rrc_message_transfer.c.s
.PHONY : f1ap_rrc_message_transfer.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... f1ap_lib"
	@echo "... f1ap_interface_management.o"
	@echo "... f1ap_interface_management.i"
	@echo "... f1ap_interface_management.s"
	@echo "... f1ap_lib_common.o"
	@echo "... f1ap_lib_common.i"
	@echo "... f1ap_lib_common.s"
	@echo "... f1ap_rrc_message_transfer.o"
	@echo "... f1ap_rrc_message_transfer.i"
	@echo "... f1ap_rrc_message_transfer.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

