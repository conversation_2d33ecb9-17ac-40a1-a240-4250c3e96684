# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/oaiseu

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/oaiseu/build_debug

# Include any dependencies generated for this target.
include openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/compiler_depend.make

# Include the progress variables for this target.
include openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/progress.make

# Include the compile flags for this target's objects.
include openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/flags.make

openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/f1ap_lib_common.c.o: openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/flags.make
openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/f1ap_lib_common.c.o: ../openair2/F1AP/lib/f1ap_lib_common.c
openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/f1ap_lib_common.c.o: openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/f1ap_lib_common.c.o"
	cd /home/<USER>/oaiseu/build_debug/openair2/F1AP/lib && /usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/f1ap_lib_common.c.o -MF CMakeFiles/f1ap_lib.dir/f1ap_lib_common.c.o.d -o CMakeFiles/f1ap_lib.dir/f1ap_lib_common.c.o -c /home/<USER>/oaiseu/openair2/F1AP/lib/f1ap_lib_common.c

openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/f1ap_lib_common.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/f1ap_lib.dir/f1ap_lib_common.c.i"
	cd /home/<USER>/oaiseu/build_debug/openair2/F1AP/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/oaiseu/openair2/F1AP/lib/f1ap_lib_common.c > CMakeFiles/f1ap_lib.dir/f1ap_lib_common.c.i

openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/f1ap_lib_common.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/f1ap_lib.dir/f1ap_lib_common.c.s"
	cd /home/<USER>/oaiseu/build_debug/openair2/F1AP/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/oaiseu/openair2/F1AP/lib/f1ap_lib_common.c -o CMakeFiles/f1ap_lib.dir/f1ap_lib_common.c.s

openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/f1ap_rrc_message_transfer.c.o: openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/flags.make
openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/f1ap_rrc_message_transfer.c.o: ../openair2/F1AP/lib/f1ap_rrc_message_transfer.c
openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/f1ap_rrc_message_transfer.c.o: openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/f1ap_rrc_message_transfer.c.o"
	cd /home/<USER>/oaiseu/build_debug/openair2/F1AP/lib && /usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/f1ap_rrc_message_transfer.c.o -MF CMakeFiles/f1ap_lib.dir/f1ap_rrc_message_transfer.c.o.d -o CMakeFiles/f1ap_lib.dir/f1ap_rrc_message_transfer.c.o -c /home/<USER>/oaiseu/openair2/F1AP/lib/f1ap_rrc_message_transfer.c

openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/f1ap_rrc_message_transfer.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/f1ap_lib.dir/f1ap_rrc_message_transfer.c.i"
	cd /home/<USER>/oaiseu/build_debug/openair2/F1AP/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/oaiseu/openair2/F1AP/lib/f1ap_rrc_message_transfer.c > CMakeFiles/f1ap_lib.dir/f1ap_rrc_message_transfer.c.i

openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/f1ap_rrc_message_transfer.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/f1ap_lib.dir/f1ap_rrc_message_transfer.c.s"
	cd /home/<USER>/oaiseu/build_debug/openair2/F1AP/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/oaiseu/openair2/F1AP/lib/f1ap_rrc_message_transfer.c -o CMakeFiles/f1ap_lib.dir/f1ap_rrc_message_transfer.c.s

openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/f1ap_interface_management.c.o: openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/flags.make
openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/f1ap_interface_management.c.o: ../openair2/F1AP/lib/f1ap_interface_management.c
openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/f1ap_interface_management.c.o: openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/oaiseu/build_debug/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/f1ap_interface_management.c.o"
	cd /home/<USER>/oaiseu/build_debug/openair2/F1AP/lib && /usr/bin/ccache /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/f1ap_interface_management.c.o -MF CMakeFiles/f1ap_lib.dir/f1ap_interface_management.c.o.d -o CMakeFiles/f1ap_lib.dir/f1ap_interface_management.c.o -c /home/<USER>/oaiseu/openair2/F1AP/lib/f1ap_interface_management.c

openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/f1ap_interface_management.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/f1ap_lib.dir/f1ap_interface_management.c.i"
	cd /home/<USER>/oaiseu/build_debug/openair2/F1AP/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/oaiseu/openair2/F1AP/lib/f1ap_interface_management.c > CMakeFiles/f1ap_lib.dir/f1ap_interface_management.c.i

openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/f1ap_interface_management.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/f1ap_lib.dir/f1ap_interface_management.c.s"
	cd /home/<USER>/oaiseu/build_debug/openair2/F1AP/lib && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/oaiseu/openair2/F1AP/lib/f1ap_interface_management.c -o CMakeFiles/f1ap_lib.dir/f1ap_interface_management.c.s

f1ap_lib: openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/f1ap_lib_common.c.o
f1ap_lib: openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/f1ap_rrc_message_transfer.c.o
f1ap_lib: openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/f1ap_interface_management.c.o
f1ap_lib: openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/build.make
.PHONY : f1ap_lib

# Rule to build all files generated by this target.
openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/build: f1ap_lib
.PHONY : openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/build

openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/clean:
	cd /home/<USER>/oaiseu/build_debug/openair2/F1AP/lib && $(CMAKE_COMMAND) -P CMakeFiles/f1ap_lib.dir/cmake_clean.cmake
.PHONY : openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/clean

openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/depend:
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/oaiseu /home/<USER>/oaiseu/openair2/F1AP/lib /home/<USER>/oaiseu/build_debug /home/<USER>/oaiseu/build_debug/openair2/F1AP/lib /home/<USER>/oaiseu/build_debug/openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : openair2/F1AP/lib/CMakeFiles/f1ap_lib.dir/depend

