# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/oaiseu

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/oaiseu/build_debug

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/oaiseu/build_debug/CMakeFiles /home/<USER>/oaiseu/build_debug/nfapi/open-nFAPI/fapi//CMakeFiles/progress.marks
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 nfapi/open-nFAPI/fapi/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/oaiseu/build_debug/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 nfapi/open-nFAPI/fapi/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 nfapi/open-nFAPI/fapi/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 nfapi/open-nFAPI/fapi/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_common.dir/rule:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_common.dir/rule
.PHONY : nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_common.dir/rule

# Convenience name for target.
nr_fapi_common: nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_common.dir/rule
.PHONY : nr_fapi_common

# fast build rule for target.
nr_fapi_common/fast:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_common.dir/build.make nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_common.dir/build
.PHONY : nr_fapi_common/fast

# Convenience name for target.
nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_p5.dir/rule:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_p5.dir/rule
.PHONY : nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_p5.dir/rule

# Convenience name for target.
nr_fapi_p5: nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_p5.dir/rule
.PHONY : nr_fapi_p5

# fast build rule for target.
nr_fapi_p5/fast:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_p5.dir/build.make nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_p5.dir/build
.PHONY : nr_fapi_p5/fast

# Convenience name for target.
nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_p7.dir/rule:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_p7.dir/rule
.PHONY : nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_p7.dir/rule

# Convenience name for target.
nr_fapi_p7: nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_p7.dir/rule
.PHONY : nr_fapi_p7

# fast build rule for target.
nr_fapi_p7/fast:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_p7.dir/build.make nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_p7.dir/build
.PHONY : nr_fapi_p7/fast

src/nr_fapi.o: src/nr_fapi.c.o
.PHONY : src/nr_fapi.o

# target to build an object file
src/nr_fapi.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_common.dir/build.make nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_common.dir/src/nr_fapi.c.o
.PHONY : src/nr_fapi.c.o

src/nr_fapi.i: src/nr_fapi.c.i
.PHONY : src/nr_fapi.i

# target to preprocess a source file
src/nr_fapi.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_common.dir/build.make nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_common.dir/src/nr_fapi.c.i
.PHONY : src/nr_fapi.c.i

src/nr_fapi.s: src/nr_fapi.c.s
.PHONY : src/nr_fapi.s

# target to generate assembly for a file
src/nr_fapi.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_common.dir/build.make nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_common.dir/src/nr_fapi.c.s
.PHONY : src/nr_fapi.c.s

src/nr_fapi_p5.o: src/nr_fapi_p5.c.o
.PHONY : src/nr_fapi_p5.o

# target to build an object file
src/nr_fapi_p5.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_p5.dir/build.make nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_p5.dir/src/nr_fapi_p5.c.o
.PHONY : src/nr_fapi_p5.c.o

src/nr_fapi_p5.i: src/nr_fapi_p5.c.i
.PHONY : src/nr_fapi_p5.i

# target to preprocess a source file
src/nr_fapi_p5.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_p5.dir/build.make nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_p5.dir/src/nr_fapi_p5.c.i
.PHONY : src/nr_fapi_p5.c.i

src/nr_fapi_p5.s: src/nr_fapi_p5.c.s
.PHONY : src/nr_fapi_p5.s

# target to generate assembly for a file
src/nr_fapi_p5.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_p5.dir/build.make nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_p5.dir/src/nr_fapi_p5.c.s
.PHONY : src/nr_fapi_p5.c.s

src/nr_fapi_p5_utils.o: src/nr_fapi_p5_utils.c.o
.PHONY : src/nr_fapi_p5_utils.o

# target to build an object file
src/nr_fapi_p5_utils.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_p5.dir/build.make nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_p5.dir/src/nr_fapi_p5_utils.c.o
.PHONY : src/nr_fapi_p5_utils.c.o

src/nr_fapi_p5_utils.i: src/nr_fapi_p5_utils.c.i
.PHONY : src/nr_fapi_p5_utils.i

# target to preprocess a source file
src/nr_fapi_p5_utils.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_p5.dir/build.make nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_p5.dir/src/nr_fapi_p5_utils.c.i
.PHONY : src/nr_fapi_p5_utils.c.i

src/nr_fapi_p5_utils.s: src/nr_fapi_p5_utils.c.s
.PHONY : src/nr_fapi_p5_utils.s

# target to generate assembly for a file
src/nr_fapi_p5_utils.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_p5.dir/build.make nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_p5.dir/src/nr_fapi_p5_utils.c.s
.PHONY : src/nr_fapi_p5_utils.c.s

src/nr_fapi_p7.o: src/nr_fapi_p7.c.o
.PHONY : src/nr_fapi_p7.o

# target to build an object file
src/nr_fapi_p7.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_p7.dir/build.make nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_p7.dir/src/nr_fapi_p7.c.o
.PHONY : src/nr_fapi_p7.c.o

src/nr_fapi_p7.i: src/nr_fapi_p7.c.i
.PHONY : src/nr_fapi_p7.i

# target to preprocess a source file
src/nr_fapi_p7.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_p7.dir/build.make nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_p7.dir/src/nr_fapi_p7.c.i
.PHONY : src/nr_fapi_p7.c.i

src/nr_fapi_p7.s: src/nr_fapi_p7.c.s
.PHONY : src/nr_fapi_p7.s

# target to generate assembly for a file
src/nr_fapi_p7.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_p7.dir/build.make nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_p7.dir/src/nr_fapi_p7.c.s
.PHONY : src/nr_fapi_p7.c.s

src/nr_fapi_p7_utils.o: src/nr_fapi_p7_utils.c.o
.PHONY : src/nr_fapi_p7_utils.o

# target to build an object file
src/nr_fapi_p7_utils.c.o:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_p7.dir/build.make nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_p7.dir/src/nr_fapi_p7_utils.c.o
.PHONY : src/nr_fapi_p7_utils.c.o

src/nr_fapi_p7_utils.i: src/nr_fapi_p7_utils.c.i
.PHONY : src/nr_fapi_p7_utils.i

# target to preprocess a source file
src/nr_fapi_p7_utils.c.i:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_p7.dir/build.make nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_p7.dir/src/nr_fapi_p7_utils.c.i
.PHONY : src/nr_fapi_p7_utils.c.i

src/nr_fapi_p7_utils.s: src/nr_fapi_p7_utils.c.s
.PHONY : src/nr_fapi_p7_utils.s

# target to generate assembly for a file
src/nr_fapi_p7_utils.c.s:
	cd /home/<USER>/oaiseu/build_debug && $(MAKE) $(MAKESILENT) -f nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_p7.dir/build.make nfapi/open-nFAPI/fapi/CMakeFiles/nr_fapi_p7.dir/src/nr_fapi_p7_utils.c.s
.PHONY : src/nr_fapi_p7_utils.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... nr_fapi_common"
	@echo "... nr_fapi_p5"
	@echo "... nr_fapi_p7"
	@echo "... src/nr_fapi.o"
	@echo "... src/nr_fapi.i"
	@echo "... src/nr_fapi.s"
	@echo "... src/nr_fapi_p5.o"
	@echo "... src/nr_fapi_p5.i"
	@echo "... src/nr_fapi_p5.s"
	@echo "... src/nr_fapi_p5_utils.o"
	@echo "... src/nr_fapi_p5_utils.i"
	@echo "... src/nr_fapi_p5_utils.s"
	@echo "... src/nr_fapi_p7.o"
	@echo "... src/nr_fapi_p7.i"
	@echo "... src/nr_fapi_p7.s"
	@echo "... src/nr_fapi_p7_utils.o"
	@echo "... src/nr_fapi_p7_utils.i"
	@echo "... src/nr_fapi_p7_utils.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/oaiseu/build_debug && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

