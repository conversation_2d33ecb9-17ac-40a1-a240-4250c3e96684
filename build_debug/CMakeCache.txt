# This is the CMakeCache file.
# For build in directory: /home/<USER>/oaiseu/build_debug
# It was generated by CMake: /usr/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//path to asn1c executable
ASN1C_EXEC:FILEPATH=/opt/asn1c/bin/asn1c

//Path to a program.
ASN1C_EXEC_PATH:FILEPATH=/opt/asn1c/bin/asn1c

//Whether AVX2 intrinsics is available on the host processor
AVX2:BOOL=ON

//Whether AVX512 intrinsics is available on the host processor
AVX512:BOOL=OFF

//CCache
CCACHE_ACTIVE:BOOL=ON

//Path to a program.
CCACHE_FOUND:FILEPATH=/usr/bin/ccache

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=/usr/bin/addr2line

//Path to a program.
CMAKE_AR:FILEPATH=/usr/bin/ar

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=Debug

//Enable/Disable color output during build.
CMAKE_COLOR_MAKEFILE:BOOL=ON

//CXX compiler
CMAKE_CXX_COMPILER:FILEPATH=/usr/bin/c++

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar-11

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib-11

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//C compiler
CMAKE_C_COMPILER:FILEPATH=/usr/bin/cc

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar-11

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib-11

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/usr/local

//Path to a program.
CMAKE_LINKER:FILEPATH=/usr/bin/ld

//Path to a program.
CMAKE_MAKE_PROGRAM:FILEPATH=/usr/bin/gmake

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/usr/bin/nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=/usr/bin/objcopy

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/usr/bin/objdump

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=OpenAirInterface

//Path to a program.
CMAKE_RANLIB:FILEPATH=/usr/bin/ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=/usr/bin/readelf

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/usr/bin/strip

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Don't create a package lock file in the binary path
CPM_DONT_CREATE_PACKAGE_LOCK:BOOL=OFF

//Don't update the module path to allow using find_package
CPM_DONT_UPDATE_MODULE_PATH:BOOL=OFF

//Always download dependencies from source
CPM_DOWNLOAD_ALL:BOOL=OFF

//Add all packages added through CPM.cmake to the package lock
CPM_INCLUDE_ALL_IN_PACKAGE_LOCK:BOOL=OFF

//Only use `find_package` to get dependencies
CPM_LOCAL_PACKAGES_ONLY:BOOL=OFF

//Directory to download CPM dependencies
CPM_SOURCE_CACHE:FILEPATH=/home/<USER>/.cache/cpm

//Always try to use `find_package` to get dependencies
CPM_USE_LOCAL_PACKAGES:BOOL=OFF

//Use additional directory of package name in cache on the most
// nested level.
CPM_USE_NAMED_CACHE_DIRECTORIES:BOOL=OFF

//Enable PHY layer debugging options
DEBUG_PHY:BOOL=False

//Enable debugging of PHY layer procedures
DEBUG_PHY_PROC:BOOL=False

//O-RAN-compliant E2 Agent
E2_AGENT:STRING=OFF

//Whether to build the lte enbcope
ENABLE_ENBSCOPE:BOOL=OFF

//Enable phy scope based on imgui
ENABLE_IMSCOPE:BOOL=OFF

//Enable recording IQ data for imscope
ENABLE_IMSCOPE_RECORD:BOOL=OFF

//Build support for CUDA
ENABLE_LDPC_CUDA:BOOL=OFF

//Build support for LDPC Offload to T2 library
ENABLE_LDPC_T2:BOOL=OFF

//Build support for LDPC Offload to XDMA library
ENABLE_LDPC_XDMA:BOOL=OFF

//Activate the LTTNG tracer, a debugging/monitoring framework
ENABLE_LTTNG:BOOL=False

//Whether to build the 5G scope
ENABLE_NRSCOPE:BOOL=OFF

//Activate build of physim tests
ENABLE_PHYSIM_TESTS:BOOL=OFF

//Whether to build telnet support in modems
ENABLE_TELNETSRV:BOOL=OFF

//Activate build of tests
ENABLE_TESTS:BOOL=OFF

//Whether to build the lte uescope
ENABLE_UESCOPE:BOOL=OFF

//Add data in vcd traces: disable it if perf issues
ENABLE_USE_CPU_EXECUTION_TIME:BOOL=False

//always true now, time measurements of proc calls and var displays
ENABLE_VCD:BOOL=False

//time measurements of proc calls and var displays sent to FIFO
// (one more thread)
ENABLE_VCD_FIFO:BOOL=False

//Whether to build the webserver
ENABLE_WEBSRV:BOOL=OFF

//Directory under which to collect all populated content
FETCHCONTENT_BASE_DIR:PATH=/home/<USER>/oaiseu/build_debug/_deps

//Disables all attempts to download or update content and assumes
// source dirs already exist
FETCHCONTENT_FULLY_DISCONNECTED:BOOL=OFF

//Enables QUIET option for all content population
FETCHCONTENT_QUIET:BOOL=ON

//Enables UPDATE_DISCONNECTED behavior for all content population
FETCHCONTENT_UPDATES_DISCONNECTED:BOOL=OFF

//Generate source code doc using doxygen
GENERATE_DOXYGEN:BOOL=False

//Git command line client
GIT_EXECUTABLE:FILEPATH=/usr/bin/git

//Carrier component data arrays size (oai doesn't support carrier
// aggreagtion for now)
MAX_NUM_CCs:STRING=1

//UE NAS layer present in this executable
NAS_BUILT_IN_UE:BOOL=True

//Number of antennas in reception
NB_ANTENNAS_RX:STRING=4

//Number of antennas in transmission
NB_ANTENNAS_TX:STRING=4

//Activate OAI's AERIAL driver
OAI_AERIAL:BOOL=OFF

//Activate OAI's AW2S driver
OAI_AW2SORI:BOOL=OFF

//Activate OAI's BladeRF driver
OAI_BLADERF:BOOL=OFF

//Activate OAI's Ethernet transport driver
OAI_ETHERNET:BOOL=OFF

//Activate OAI's FHI 7.2 (xran/fhi_lib) driver
OAI_FHI72:BOOL=OFF

//Activate OAI's IRIS/SoapySDR driver
OAI_IRIS:BOOL=OFF

//Activate OAI's LimeSDR driver
OAI_LMSSDR:BOOL=OFF

//Activate OAI's rfsimulator driver
OAI_SIMU:BOOL=ON

//Activate OAI's USRP driver
OAI_USRP:BOOL=OFF

//Value Computed by CMake
OpenAirInterface_BINARY_DIR:STATIC=/home/<USER>/oaiseu/build_debug

//Value Computed by CMake
OpenAirInterface_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
OpenAirInterface_SOURCE_DIR:STATIC=/home/<USER>/oaiseu

//enable UE_EXPANSION with max 256 UE
PHY_TX_THREAD:BOOL=False

//Arguments to supply to pkg-config
PKG_CONFIG_ARGN:STRING=

//pkg-config executable
PKG_CONFIG_EXECUTABLE:FILEPATH=/usr/bin/pkg-config

//enable UE_EXPANSION with max 256 UE
PRE_SCD_THREAD:BOOL=False

//set the LTE RLC mode to AM for the default bearer, otherwise
// it is UM.
RRC_DEFAULT_RAB_IS_AM:BOOL=True

//enable the address sanitizer (ASan)
SANITIZE_ADDRESS:BOOL=False

//enable the memory sanitizer (MSan, requires clang, incompatible
// with ASan/UBSan)
SANITIZE_MEMORY:BOOL=False

//enable the address sanitizer (TSan)
SANITIZE_THREAD:BOOL=False

//enable the undefined behavior sanitizer (UBSan)
SANITIZE_UNDEFINED:BOOL=False

//Rohde&Schwarz SMBV100A vector signal generator
SMBV:BOOL=False

//Enable ASN1 encoder/decoder debug traces via OAI logging system
TRACE_ASN1C_ENC_DEC:BOOL=OFF

//Enable tracy instrumentation
TRACY_ENABLE:BOOL=OFF

//Activate the T tracer, a debugging/monitoring framework
T_TRACER:BOOL=True

//Compile T tracer GUI tools
T_TRACER_GUI:BOOL=OFF

//Activate UE autotest specific logs
UE_AUTOTEST_TRACE:BOOL=False

//Activate UE debug trace
UE_DEBUG_TRACE:BOOL=False

//enable UE_EXPANSION with max 256 UE
UE_EXPANSION:BOOL=False

//Activate UE timing trace
UE_TIMING_TRACE:BOOL=False

//Activate OAI's shared memory radio driver
VRTSIM_RADIO:BOOL=ON

//Path to a library.
pkgcfg_lib_OpenSSL_crypto:FILEPATH=/usr/lib/x86_64-linux-gnu/libcrypto.so

//Path to a library.
pkgcfg_lib_OpenSSL_ssl:FILEPATH=/usr/lib/x86_64-linux-gnu/libssl.so

//Path to a library.
pkgcfg_lib_blas_blas:FILEPATH=/usr/lib/x86_64-linux-gnu/openblas-pthread/libblas.so

//Path to a library.
pkgcfg_lib_lapacke_lapacke:FILEPATH=/usr/lib/x86_64-linux-gnu/liblapacke.so

//Path to a library.
pkgcfg_lib_libconfig_config:FILEPATH=/usr/lib/x86_64-linux-gnu/libconfig.so

//The directory containing a CMake configuration file for yaml-cpp.
yaml-cpp_DIR:PATH=/usr/lib/x86_64-linux-gnu/cmake/yaml-cpp


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//STRINGS property for variable: CMAKE_BUILD_TYPE
CMAKE_BUILD_TYPE-STRINGS:INTERNAL=Debug;Release;RelWithDebInfo;MinSizeRel
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/home/<USER>/oaiseu/build_debug
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=22
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=1
//ADVANCED property for variable: CMAKE_COLOR_MAKEFILE
CMAKE_COLOR_MAKEFILE-ADVANCED:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/usr/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/usr/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/usr/bin/ctest
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_AR
CMAKE_CXX_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_RANLIB
CMAKE_CXX_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_AR
CMAKE_C_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_RANLIB
CMAKE_C_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=ELF
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Unix Makefiles
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/home/<USER>/oaiseu
//Install .so files without execute permission.
CMAKE_INSTALL_SO_NO_EXE:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=89
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/usr/share/cmake-3.22
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
CPM_DIRECTORY:INTERNAL=/home/<USER>/.cache/cpm/cpm
//Don't download or configure dependencies (for testing)
CPM_DRY_RUN:INTERNAL=OFF
CPM_FILE:INTERNAL=/home/<USER>/.cache/cpm/cpm/CPM_0.40.1.cmake
CPM_INDENT:INTERNAL=CPM:
CPM_MODULE_PATH:INTERNAL=/home/<USER>/oaiseu/build_debug/CPM_modules
CPM_PACKAGES:INTERNAL=
CPM_PACKAGE_LOCK_FILE:INTERNAL=/home/<USER>/oaiseu/build_debug/cpm-package-lock.cmake
CPM_VERSION:INTERNAL=0.40.1
//STRINGS property for variable: E2_AGENT
E2_AGENT-STRINGS:INTERNAL=ON;OFF
//Details about finding Git
FIND_PACKAGE_MESSAGE_DETAILS_Git:INTERNAL=[/usr/bin/git][v2.34.1()]
//Details about finding PkgConfig
FIND_PACKAGE_MESSAGE_DETAILS_PkgConfig:INTERNAL=[/usr/bin/pkg-config][v0.29.2()]
//ADVANCED property for variable: GIT_EXECUTABLE
GIT_EXECUTABLE-ADVANCED:INTERNAL=1
//STRINGS property for variable: NB_ANTENNAS_RX
NB_ANTENNAS_RX-STRINGS:INTERNAL=1;2;4
//STRINGS property for variable: NB_ANTENNAS_TX
NB_ANTENNAS_TX-STRINGS:INTERNAL=1;2;4
OpenSSL_CFLAGS:INTERNAL=
OpenSSL_CFLAGS_I:INTERNAL=
OpenSSL_CFLAGS_OTHER:INTERNAL=
OpenSSL_FOUND:INTERNAL=1
OpenSSL_INCLUDEDIR:INTERNAL=/usr/include
OpenSSL_INCLUDE_DIRS:INTERNAL=
OpenSSL_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lssl;-lcrypto
OpenSSL_LDFLAGS_OTHER:INTERNAL=
OpenSSL_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
OpenSSL_LIBRARIES:INTERNAL=ssl;crypto
OpenSSL_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
OpenSSL_LIBS:INTERNAL=
OpenSSL_LIBS_L:INTERNAL=
OpenSSL_LIBS_OTHER:INTERNAL=
OpenSSL_LIBS_PATHS:INTERNAL=
OpenSSL_MODULE_NAME:INTERNAL=openssl
OpenSSL_PREFIX:INTERNAL=/usr
OpenSSL_STATIC_CFLAGS:INTERNAL=
OpenSSL_STATIC_CFLAGS_I:INTERNAL=
OpenSSL_STATIC_CFLAGS_OTHER:INTERNAL=
OpenSSL_STATIC_INCLUDE_DIRS:INTERNAL=
OpenSSL_STATIC_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lssl;-lcrypto;-ldl;-pthread
OpenSSL_STATIC_LDFLAGS_OTHER:INTERNAL=-pthread
OpenSSL_STATIC_LIBDIR:INTERNAL=
OpenSSL_STATIC_LIBRARIES:INTERNAL=ssl;crypto;dl
OpenSSL_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
OpenSSL_STATIC_LIBS:INTERNAL=
OpenSSL_STATIC_LIBS_L:INTERNAL=
OpenSSL_STATIC_LIBS_OTHER:INTERNAL=
OpenSSL_STATIC_LIBS_PATHS:INTERNAL=
OpenSSL_VERSION:INTERNAL=3.0.2
OpenSSL_openssl_INCLUDEDIR:INTERNAL=
OpenSSL_openssl_LIBDIR:INTERNAL=
OpenSSL_openssl_PREFIX:INTERNAL=
OpenSSL_openssl_VERSION:INTERNAL=
//ADVANCED property for variable: PKG_CONFIG_ARGN
PKG_CONFIG_ARGN-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_EXECUTABLE
PKG_CONFIG_EXECUTABLE-ADVANCED:INTERNAL=1
__pkg_config_arguments_OpenSSL:INTERNAL=openssl;REQUIRED
__pkg_config_arguments_blas:INTERNAL=REQUIRED;blas
__pkg_config_arguments_lapacke:INTERNAL=REQUIRED;lapacke
__pkg_config_arguments_libconfig:INTERNAL=REQUIRED;libconfig
__pkg_config_checked_OpenSSL:INTERNAL=1
__pkg_config_checked_blas:INTERNAL=1
__pkg_config_checked_cap:INTERNAL=1
__pkg_config_checked_cblas:INTERNAL=1
__pkg_config_checked_lapacke:INTERNAL=1
__pkg_config_checked_libconfig:INTERNAL=1
blas_CFLAGS:INTERNAL=-I/usr/include/x86_64-linux-gnu
blas_CFLAGS_I:INTERNAL=
blas_CFLAGS_OTHER:INTERNAL=
blas_FOUND:INTERNAL=1
blas_INCLUDEDIR:INTERNAL=/usr/include/x86_64-linux-gnu
blas_INCLUDE_DIRS:INTERNAL=/usr/include/x86_64-linux-gnu
blas_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu/openblas-pthread;-lblas
blas_LDFLAGS_OTHER:INTERNAL=
blas_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu/openblas-pthread
blas_LIBRARIES:INTERNAL=blas
blas_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu/openblas-pthread
blas_LIBS:INTERNAL=
blas_LIBS_L:INTERNAL=
blas_LIBS_OTHER:INTERNAL=
blas_LIBS_PATHS:INTERNAL=
blas_MODULE_NAME:INTERNAL=blas
blas_PREFIX:INTERNAL=/usr
blas_STATIC_CFLAGS:INTERNAL=-I/usr/include/x86_64-linux-gnu
blas_STATIC_CFLAGS_I:INTERNAL=
blas_STATIC_CFLAGS_OTHER:INTERNAL=
blas_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/x86_64-linux-gnu
blas_STATIC_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu/openblas-pthread;-lblas;-lgfortran;-lpthread;-lm
blas_STATIC_LDFLAGS_OTHER:INTERNAL=
blas_STATIC_LIBDIR:INTERNAL=
blas_STATIC_LIBRARIES:INTERNAL=blas;gfortran;pthread;m
blas_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu/openblas-pthread
blas_STATIC_LIBS:INTERNAL=
blas_STATIC_LIBS_L:INTERNAL=
blas_STATIC_LIBS_OTHER:INTERNAL=
blas_STATIC_LIBS_PATHS:INTERNAL=
blas_VERSION:INTERNAL=0.3.20+ds
blas_blas_INCLUDEDIR:INTERNAL=
blas_blas_LIBDIR:INTERNAL=
blas_blas_PREFIX:INTERNAL=
blas_blas_VERSION:INTERNAL=
cap_CFLAGS:INTERNAL=
cap_CFLAGS_I:INTERNAL=
cap_CFLAGS_OTHER:INTERNAL=
cap_FOUND:INTERNAL=
cap_INCLUDEDIR:INTERNAL=
cap_LIBDIR:INTERNAL=
cap_LIBS:INTERNAL=
cap_LIBS_L:INTERNAL=
cap_LIBS_OTHER:INTERNAL=
cap_LIBS_PATHS:INTERNAL=
cap_MODULE_NAME:INTERNAL=
cap_PREFIX:INTERNAL=
cap_STATIC_CFLAGS:INTERNAL=
cap_STATIC_CFLAGS_I:INTERNAL=
cap_STATIC_CFLAGS_OTHER:INTERNAL=
cap_STATIC_LIBDIR:INTERNAL=
cap_STATIC_LIBS:INTERNAL=
cap_STATIC_LIBS_L:INTERNAL=
cap_STATIC_LIBS_OTHER:INTERNAL=
cap_STATIC_LIBS_PATHS:INTERNAL=
cap_VERSION:INTERNAL=
cap_libcap_INCLUDEDIR:INTERNAL=
cap_libcap_LIBDIR:INTERNAL=
cap_libcap_PREFIX:INTERNAL=
cap_libcap_VERSION:INTERNAL=
cblas_CFLAGS:INTERNAL=
cblas_CFLAGS_I:INTERNAL=
cblas_CFLAGS_OTHER:INTERNAL=
cblas_FOUND:INTERNAL=
cblas_INCLUDEDIR:INTERNAL=
cblas_LIBDIR:INTERNAL=
cblas_LIBS:INTERNAL=
cblas_LIBS_L:INTERNAL=
cblas_LIBS_OTHER:INTERNAL=
cblas_LIBS_PATHS:INTERNAL=
cblas_MODULE_NAME:INTERNAL=
cblas_PREFIX:INTERNAL=
cblas_STATIC_CFLAGS:INTERNAL=
cblas_STATIC_CFLAGS_I:INTERNAL=
cblas_STATIC_CFLAGS_OTHER:INTERNAL=
cblas_STATIC_LIBDIR:INTERNAL=
cblas_STATIC_LIBS:INTERNAL=
cblas_STATIC_LIBS_L:INTERNAL=
cblas_STATIC_LIBS_OTHER:INTERNAL=
cblas_STATIC_LIBS_PATHS:INTERNAL=
cblas_VERSION:INTERNAL=
cblas_cblas_INCLUDEDIR:INTERNAL=
cblas_cblas_LIBDIR:INTERNAL=
cblas_cblas_PREFIX:INTERNAL=
cblas_cblas_VERSION:INTERNAL=
lapacke_CFLAGS:INTERNAL=-I/usr/include/x86_64-linux-gnu
lapacke_CFLAGS_I:INTERNAL=
lapacke_CFLAGS_OTHER:INTERNAL=
lapacke_FOUND:INTERNAL=1
lapacke_INCLUDEDIR:INTERNAL=/usr/include/x86_64-linux-gnu
lapacke_INCLUDE_DIRS:INTERNAL=/usr/include/x86_64-linux-gnu
lapacke_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-llapacke
lapacke_LDFLAGS_OTHER:INTERNAL=
lapacke_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
lapacke_LIBRARIES:INTERNAL=lapacke
lapacke_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
lapacke_LIBS:INTERNAL=
lapacke_LIBS_L:INTERNAL=
lapacke_LIBS_OTHER:INTERNAL=
lapacke_LIBS_PATHS:INTERNAL=
lapacke_MODULE_NAME:INTERNAL=lapacke
lapacke_PREFIX:INTERNAL=
lapacke_STATIC_CFLAGS:INTERNAL=-I/usr/include/x86_64-linux-gnu
lapacke_STATIC_CFLAGS_I:INTERNAL=
lapacke_STATIC_CFLAGS_OTHER:INTERNAL=
lapacke_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/x86_64-linux-gnu
lapacke_STATIC_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-L/usr/lib/x86_64-linux-gnu/openblas-pthread;-llapacke;-llapack;-lgfortran;-lpthread;-lm
lapacke_STATIC_LDFLAGS_OTHER:INTERNAL=
lapacke_STATIC_LIBDIR:INTERNAL=
lapacke_STATIC_LIBRARIES:INTERNAL=lapacke;lapack;gfortran;pthread;m
lapacke_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu;/usr/lib/x86_64-linux-gnu/openblas-pthread
lapacke_STATIC_LIBS:INTERNAL=
lapacke_STATIC_LIBS_L:INTERNAL=
lapacke_STATIC_LIBS_OTHER:INTERNAL=
lapacke_STATIC_LIBS_PATHS:INTERNAL=
lapacke_VERSION:INTERNAL=3.10.0
lapacke_lapacke_INCLUDEDIR:INTERNAL=
lapacke_lapacke_LIBDIR:INTERNAL=
lapacke_lapacke_PREFIX:INTERNAL=
lapacke_lapacke_VERSION:INTERNAL=
libconfig_CFLAGS:INTERNAL=
libconfig_CFLAGS_I:INTERNAL=
libconfig_CFLAGS_OTHER:INTERNAL=
libconfig_FOUND:INTERNAL=1
libconfig_INCLUDEDIR:INTERNAL=/usr/include
libconfig_INCLUDE_DIRS:INTERNAL=
libconfig_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lconfig
libconfig_LDFLAGS_OTHER:INTERNAL=
libconfig_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
libconfig_LIBRARIES:INTERNAL=config
libconfig_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
libconfig_LIBS:INTERNAL=
libconfig_LIBS_L:INTERNAL=
libconfig_LIBS_OTHER:INTERNAL=
libconfig_LIBS_PATHS:INTERNAL=
libconfig_MODULE_NAME:INTERNAL=libconfig
libconfig_PREFIX:INTERNAL=/usr
libconfig_STATIC_CFLAGS:INTERNAL=
libconfig_STATIC_CFLAGS_I:INTERNAL=
libconfig_STATIC_CFLAGS_OTHER:INTERNAL=
libconfig_STATIC_INCLUDE_DIRS:INTERNAL=
libconfig_STATIC_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lconfig
libconfig_STATIC_LDFLAGS_OTHER:INTERNAL=
libconfig_STATIC_LIBDIR:INTERNAL=
libconfig_STATIC_LIBRARIES:INTERNAL=config
libconfig_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
libconfig_STATIC_LIBS:INTERNAL=
libconfig_STATIC_LIBS_L:INTERNAL=
libconfig_STATIC_LIBS_OTHER:INTERNAL=
libconfig_STATIC_LIBS_PATHS:INTERNAL=
libconfig_VERSION:INTERNAL=1.5
libconfig_libconfig_INCLUDEDIR:INTERNAL=
libconfig_libconfig_LIBDIR:INTERNAL=
libconfig_libconfig_PREFIX:INTERNAL=
libconfig_libconfig_VERSION:INTERNAL=
//ADVANCED property for variable: pkgcfg_lib_OpenSSL_crypto
pkgcfg_lib_OpenSSL_crypto-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_OpenSSL_ssl
pkgcfg_lib_OpenSSL_ssl-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_blas_blas
pkgcfg_lib_blas_blas-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_lapacke_lapacke
pkgcfg_lib_lapacke_lapacke-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_libconfig_config
pkgcfg_lib_libconfig_config-ADVANCED:INTERNAL=1
prefix_result:INTERNAL=/usr/lib/x86_64-linux-gnu

