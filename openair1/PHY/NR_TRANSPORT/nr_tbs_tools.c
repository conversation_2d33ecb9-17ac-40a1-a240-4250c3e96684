/*
 * Licensed to the OpenAirInterface (OAI) Software Alliance under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The OpenAirInterface Software Alliance licenses this file to You under
 * the OAI Public License, Version 1.0  (the "License"); you may not use this file
 * except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.openairinterface.org/?page_id=698
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *-------------------------------------------------------------------------------
 * For more information about the OpenAirInterface (OAI) Software Alliance:
 *      <EMAIL>
 */

/*! \file PHY/NR_TRANSPORT/nr_tbs_tools.c
 * \brief Top-level routines for implementing LDPC-coded (DLSCH) transport channels from 38-212, 15.2
 * \author <PERSON><PERSON>
 * \date 2018
 * \version 0.1
 * \company Eurecom
 * \email:
 * \note
 * \warning
 */

#include "nr_transport_common_proto.h"
#include "PHY/CODING/coding_defs.h"
#include "PHY/defs_nr_common.h"

/* 函数：nr_get_G - 计算可用编码比特数G
 * 作用：根据资源分配和MIMO层数计算总的可用编码比特数
 * 调用位置：PDSCH/PUSCH编码时计算总可用比特数
 *
 * 4×4 MIMO 修改说明：
 * - 支持Nl=1,2,4层（原来主要支持1,2层）
 * - G值会随层数线性增长：4×4时G约为2×2时的2倍
 * - 需要确保G值不会溢出uint32_t范围
 *
 * 参数说明：
 * @nb_rb: 分配的资源块数
 * @nb_symb_sch: 调度的OFDM符号数
 * @nb_re_dmrs: 每个PRB中DMRS占用的RE数
 * @length_dmrs: DMRS符号长度
 * @unav_res: 不可用资源数
 * @Qm: 调制阶数（QPSK=2, 16QAM=4, 64QAM=6, 256QAM=8）
 * @Nl: MIMO层数（1,2,4 for 4×4 MIMO）
 *
 * 返回值：总可用编码比特数G
 */
uint32_t
nr_get_G(uint16_t nb_rb, uint16_t nb_symb_sch, uint8_t nb_re_dmrs, uint16_t length_dmrs, uint32_t unav_res, uint8_t Qm, uint8_t Nl)
{
  /* 参数有效性检查 - 4×4 MIMO扩展 */
  AssertFatal(Nl > 0 && Nl <= 4, "Invalid number of layers Nl=%d (must be 1-4 for 4×4 MIMO)\n", Nl);
  AssertFatal(Qm > 0 && Qm <= 8, "Invalid modulation order Qm=%d\n", Qm);
  AssertFatal(nb_rb > 0, "Invalid number of RBs nb_rb=%d\n", nb_rb);

  /* 计算每个PRB的可用RE数 */
  uint32_t re_per_rb = (NR_NB_SC_PER_RB * nb_symb_sch) - (nb_re_dmrs * length_dmrs);

  /* 计算总的可用编码比特数：RE数 × RB数 × 调制阶数 × 层数 */
  uint64_t G_temp = (uint64_t)re_per_rb * nb_rb * Qm * Nl;

  /* 减去不可用资源占用的比特数 */
  G_temp -= (uint64_t)unav_res * Qm * Nl;

  /* 检查是否溢出uint32_t范围 */
  AssertFatal(G_temp <= UINT32_MAX, "G value overflow: G=%lu exceeds uint32_t range\n", G_temp);

  uint32_t G = (uint32_t)G_temp;

  /* 调试信息 - 4×4 MIMO特殊标记 */
  if (Nl == 4) {
    LOG_D(PHY, "4×4 MIMO: G=%u (nb_rb=%d, nb_symb_sch=%d, Qm=%d, Nl=%d)\n",
          G, nb_rb, nb_symb_sch, Qm, Nl);
  }

  return G;
}

/* 函数：nr_get_E - 计算每个代码段的输出比特数E
 * 作用：将总可用比特数G分配给各个代码段
 * 调用位置：速率匹配时确定每个代码段的输出长度
 *
 * 4×4 MIMO 修改说明：
 * - 支持Nl=4层，E值会相应增加
 * - 需要处理更大的G值和E值
 * - 确保E值分配的公平性和正确性
 *
 * 参数说明：
 * @G: 总可用编码比特数（4×4 MIMO时约为2×2的2倍）
 * @C: 代码段总数
 * @Qm: 调制阶数
 * @Nl: MIMO层数（1,2,4）
 * @r: 当前代码段索引（0到C-1）
 *
 * 返回值：第r个代码段的输出比特数E
 */
uint32_t nr_get_E(uint32_t G, uint8_t C, uint8_t Qm, uint8_t Nl, uint8_t r)
{
  uint32_t E;
  uint8_t Cprime = C; // assume CBGTI not present

  /* 参数有效性检查 - 4×4 MIMO扩展 */
  AssertFatal(Nl > 0 && Nl <= 4, "Invalid Nl=%d (must be 1-4 for 4×4 MIMO)\n", Nl);
  AssertFatal(Qm > 0 && Qm <= 8, "Invalid Qm=%d\n", Qm);
  AssertFatal(C > 0, "Invalid C=%d\n", C);
  AssertFatal(r < C, "Invalid segment index r=%d (must be < C=%d)\n", r, C);

  /* 计算每个代码段的基本比特数 */
  uint32_t base_bits_per_segment = G / (Nl * Qm * Cprime);
  uint32_t remainder = (G / (Nl * Qm)) % Cprime;

  /* 根据38.212标准分配E值：
   * - 前(Cprime - remainder)个段分配基本比特数
   * - 后remainder个段分配(基本比特数 + 1) */
  if (r <= Cprime - remainder - 1) {
    E = Nl * Qm * base_bits_per_segment;
  } else {
    E = Nl * Qm * (base_bits_per_segment + 1);
  }

  /* 4×4 MIMO特殊检查：确保E值合理 */
  if (Nl == 4) {
    AssertFatal(E > 0, "4×4 MIMO: E=0 for segment %d\n", r);
    AssertFatal(E <= G, "4×4 MIMO: E=%u exceeds G=%u for segment %d\n", E, G, r);

    LOG_D(PHY, "4×4 MIMO nr_get_E: segment %d, E=%u (G=%u, C=%d, Qm=%d, Nl=%d)\n",
          r, E, G, C, Qm, Nl);
  } else {
    LOG_D(PHY, "nr_get_E : (G %d, C %d, Qm %d, Nl %d, r %d), E %d\n", G, C, Qm, Nl, r, E);
  }

  return E;
}

static const uint8_t index_k0[2][4] = {{0, 17, 33, 56}, {0, 13, 25, 43}};

int nr_get_R_ldpc_decoder(int rvidx, int E, int BG, int Z, int *llrLen, int round)
{
  AssertFatal(BG == 1 || BG == 2, "Unknown BG %d\n", BG);

  int Ncb = (BG == 1) ? (66 * Z) : (50 * Z);
  int infoBits = (index_k0[BG - 1][rvidx] * Z + E);

  if (round == 0)
    *llrLen = infoBits;
  if (infoBits > Ncb)
    infoBits = Ncb;
  if (infoBits > *llrLen)
    *llrLen = infoBits;

  int sysBits = (BG == 1) ? (22 * Z) : (10 * Z);
  float decoderR = (float)sysBits / (infoBits + 2 * Z);

  if (BG == 2)
    if (decoderR < 0.3333)
      return 15;
    else if (decoderR < 0.6667)
      return 13;
    else
      return 23;
  else if (decoderR < 0.6667)
    return 13;
  else if (decoderR < 0.8889)
    return 23;
  else
    return 89;
}
