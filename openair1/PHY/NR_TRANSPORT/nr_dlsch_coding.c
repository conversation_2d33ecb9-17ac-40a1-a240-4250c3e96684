/*
 * Licensed to the OpenAirInterface (OAI) Software Alliance under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The OpenAirInterface Software Alliance licenses this file to You under
 * the OAI Public License, Version 1.0  (the "License"); you may not use this file
 * except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.openairinterface.org/?page_id=698
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *-------------------------------------------------------------------------------
 * For more information about the OpenAirInterface (OAI) Software Alliance:
 *      <EMAIL>
 */

/*! \file PHY/NR_TRANSPORT/nr_dlsch_coding.c
 * \brief NR 下行链路共享信道（DLSCH）编码的顶层实现
 *
 * 中文说明：
 * 本文件实现 NR PDSCH 的完整编码链路，包括：
 * 1. 传输块（TB）CRC 添加
 * 2. 传输块分段（nr_segmentation）
 * 3. LDPC 编码参数设置
 * 4. 调用 LDPC 编码接口（包含速率匹配和交织）
 *
 * 数据流向：
 * MAC PDU → CRC添加 → TB分段 → LDPC编码 → 速率匹配 → 交织 → 输出比特流
 *
 * 主要函数：
 * - new_gNB_dlsch(): 创建和初始化 DLSCH 结构体
 * - nr_dlsch_encoding(): DLSCH 编码主函数
 * - free_gNB_dlsch(): 释放 DLSCH 相关内存
 *
 * 参考标准：3GPP TS 38.212 Section 5（传输信道处理）
 */

#include "PHY/defs_gNB.h"
#include "PHY/CODING/coding_extern.h"
#include "PHY/CODING/coding_defs.h"
#include "PHY/CODING/lte_interleaver_inline.h"
#include "PHY/CODING/nrLDPC_coding/nrLDPC_coding_interface.h"
#include "PHY/CODING/nrLDPC_extern.h"
#include "PHY/NR_TRANSPORT/nr_transport_proto.h"
#include "PHY/NR_TRANSPORT/nr_transport_common_proto.h"
#include "PHY/NR_TRANSPORT/nr_dlsch.h"
#include "SCHED_NR/sched_nr.h"
#include "common/utils/LOG/vcd_signal_dumper.h"
#include "common/utils/LOG/log.h"
#include "common/utils/nr/nr_common.h"
#include <syscall.h>
#include <openair2/UTIL/OPT/opt.h>

// #define DEBUG_DLSCH_CODING
// #define DEBUG_DLSCH_FREE 1

void free_gNB_dlsch(NR_gNB_DLSCH_t *dlsch, uint16_t N_RB, const NR_DL_FRAME_PARMS *frame_parms)
{
  int max_layers = (frame_parms->nb_antennas_tx < NR_MAX_NB_LAYERS) ? frame_parms->nb_antennas_tx : NR_MAX_NB_LAYERS;
  uint16_t a_segments = MAX_NUM_NR_DLSCH_SEGMENTS_PER_LAYER * max_layers;

  if (N_RB != 273) {
    a_segments = a_segments * N_RB;
    a_segments = a_segments / 273 + 1;
  }

  NR_DL_gNB_HARQ_t *harq = &dlsch->harq_process;
  if (harq->b) {
    free16(harq->b, a_segments * 1056);
    harq->b = NULL;
  }
  if (harq->f) {
    free16(harq->f, N_RB * NR_SYMBOLS_PER_SLOT * NR_NB_SC_PER_RB * 8 * NR_MAX_NB_LAYERS);
    harq->f = NULL;
  }
  for (int r = 0; r < a_segments; r++) {
    free(harq->c[r]);
    harq->c[r] = NULL;
  }
  free(harq->c);
}

/* 函数：new_gNB_dlsch - 创建和初始化 gNB 端 DLSCH 结构体
 * 作用：为 PDSCH 编码分配必要的内存缓冲区
 * 调用位置：gNB 初始化阶段，为每个 DLSCH 实例分配资源
 *
 * 参数说明：
 * @frame_parms: 帧参数结构体，包含天线数等配置
 * @N_RB: 资源块数，用于计算缓冲区大小
 *
 * 返回值：初始化完成的 DLSCH 结构体
 */
NR_gNB_DLSCH_t new_gNB_dlsch(NR_DL_FRAME_PARMS *frame_parms, uint16_t N_RB)
{
  /* 计算最大层数：取天线数和系统支持的最大层数的较小值 */
  int max_layers = (frame_parms->nb_antennas_tx < NR_MAX_NB_LAYERS) ? frame_parms->nb_antennas_tx : NR_MAX_NB_LAYERS;

  /* 计算需要分配的代码段数量：每层最大段数 × 层数 */
  uint16_t a_segments = MAX_NUM_NR_DLSCH_SEGMENTS_PER_LAYER * max_layers; // number of segments to be allocated

  /* 根据实际 RB 数调整段数（273 是参考值） */
  if (N_RB != 273) {
    a_segments = a_segments * N_RB;
    a_segments = a_segments / 273 + 1;
  }

  LOG_D(PHY, "Allocating %d segments (MAX %d, N_PRB %d)\n", a_segments, MAX_NUM_NR_DLSCH_SEGMENTS_PER_LAYER, N_RB);

  /* 计算传输块缓冲区大小：每段 1056 字节 */
  uint32_t dlsch_bytes = a_segments * 1056; // allocated bytes per segment
  NR_gNB_DLSCH_t dlsch;

  /* 初始化 HARQ 进程结构体 */
  NR_DL_gNB_HARQ_t *harq = &dlsch.harq_process;
  bzero(harq, sizeof(NR_DL_gNB_HARQ_t));

  /* 分配传输块缓冲区 harq->b：存储添加 CRC 后的传输块数据 */
  harq->b = malloc16(dlsch_bytes);
  AssertFatal(harq->b, "cannot allocate memory for harq->b\n");
  bzero(harq->b, dlsch_bytes);

  /* 分配代码块缓冲区数组 harq->c：存储分段后的各个代码块 */
  harq->c = (uint8_t **)malloc16(a_segments * sizeof(uint8_t *));
  for (int r = 0; r < a_segments; r++) {
    /* 为每个代码块分配缓冲区
     * 8448 是 NR 中代码块的最大大小（BG1: 66*Zmax, BG2: 50*Zmax）
     * 需要考虑填充位和多段情况下的 CRC */
    harq->c[r] = malloc16(8448);
    AssertFatal(harq->c[r], "cannot allocate harq->c[%d]\n", r);
    bzero(harq->c[r], 8448);
  }

  /* 分配编码输出缓冲区 harq->f：存储速率匹配和交织后的比特流
   * 大小 = RB数 × 每时隙符号数 × 每RB子载波数 × 最大调制阶数 × 最大层数 */
  harq->f = malloc16(N_RB * NR_SYMBOLS_PER_SLOT * NR_NB_SC_PER_RB * 8 * NR_MAX_NB_LAYERS);
  AssertFatal(harq->f, "cannot allocate harq->f\n");
  bzero(harq->f, N_RB * NR_SYMBOLS_PER_SLOT * NR_NB_SC_PER_RB * 8 * NR_MAX_NB_LAYERS);

  return (dlsch);
}

/* 函数：nr_dlsch_encoding - NR DLSCH 编码主函数
 * 作用：执行完整的 PDSCH 编码流程，从 MAC PDU 到可调制的比特流
 * 调用位置：gNB 物理层处理链路中，在资源映射之前调用
 *
 * 参数说明：
 * @gNB: gNB 物理层变量结构体
 * @msgTx: 包含待编码 PDSCH 数据的消息结构体
 * @frame: 帧号
 * @slot: 时隙号
 * @frame_parms: 帧参数
 * @output: 编码输出缓冲区
 * @各种 time_stats_t: 性能统计计时器
 *
 * 返回值：0成功，-1失败
 *
 * 编码流程：
 * 1. CRC 添加（24bit 或 16bit）
 * 2. 传输块分段（nr_segmentation）
 * 3. LDPC 编码参数设置
 * 4. 调用 LDPC 编码接口（包含速率匹配和交织）
 */
int nr_dlsch_encoding(PHY_VARS_gNB *gNB,
                      processingData_L1tx_t *msgTx,
                      int frame,
                      uint8_t slot,
                      NR_DL_FRAME_PARMS *frame_parms,
                      unsigned char *output,
                      time_stats_t *tinput,
                      time_stats_t *tprep,
                      time_stats_t *tparity,
                      time_stats_t *toutput,
                      time_stats_t *dlsch_rate_matching_stats,
                      time_stats_t *dlsch_interleaving_stats,
                      time_stats_t *dlsch_segmentation_stats)
{
  VCD_SIGNAL_DUMPER_DUMP_FUNCTION_BY_NAME(VCD_SIGNAL_DUMPER_FUNCTIONS_gNB_DLSCH_ENCODING, VCD_FUNCTION_IN);

  /* 初始化传输块编码参数数组：支持一个时隙内多个 PDSCH */
  nrLDPC_TB_encoding_parameters_t TBs[msgTx->num_pdsch_slot];
  memset(TBs, 0, sizeof(TBs));

  /* 初始化时隙级编码参数：包含所有传输块的公共参数 */
  nrLDPC_slot_encoding_parameters_t slot_parameters = {.frame = frame,
                                                       .slot = slot,
                                                       .nb_TBs = msgTx->num_pdsch_slot,
                                                       .threadPool = &gNB->threadPool,
                                                       .tinput = tinput,
                                                       .tprep = tprep,
                                                       .tparity = tparity,
                                                       .toutput = toutput,
                                                       .TBs = TBs};

  int num_segments = 0;  // 统计所有传输块的总段数

  /* 第一阶段：CRC 添加和传输块分段
   * 遍历当前时隙内的所有 PDSCH 传输块 */
  for (int dlsch_id = 0; dlsch_id < msgTx->num_pdsch_slot; dlsch_id++) {
    NR_gNB_DLSCH_t *dlsch = msgTx->dlsch[dlsch_id];

    /* 获取 HARQ 进程和相关参数 */
    NR_DL_gNB_HARQ_t *harq = &dlsch->harq_process;
    unsigned int crc = 1;
    nfapi_nr_dl_tti_pdsch_pdu_rel15_t *rel15 = &harq->pdsch_pdu.pdsch_pdu_rel15;

    /* A: 传输块大小（比特数），从字节转换为比特 */
    uint32_t A = rel15->TBSize[0] << 3;
    unsigned char *a = harq->pdu;  // 指向原始 MAC PDU 数据
    /* PDU 跟踪：记录下行传输的 MAC PDU（除了系统信息） */
    if (rel15->rnti != SI_RNTI) {
      ws_trace_t tmp = {.nr = true,
                        .direction = DIRECTION_DOWNLINK,
                        .pdu_buffer = a,
                        .pdu_buffer_size = rel15->TBSize[0],
                        .ueid = 0,
                        .rntiType = WS_C_RNTI,
                        .rnti = rel15->rnti,
                        .sysFrame = frame,
                        .subframe = slot,
                        .harq_pid = 0, // difficult to find the harq pid here
                        .oob_event = 0,
                        .oob_event_value = 0};
      trace_pdu(&tmp);
    }

    /* 获取物理层统计信息结构体，用于性能监控 */
    NR_gNB_PHY_STATS_t *phy_stats = NULL;
    if (rel15->rnti != 0xFFFF)
      phy_stats = get_phy_stats(gNB, rel15->rnti);

    /* 更新统计信息：传输字节数、层数、调制阶数等 */
    if (phy_stats) {
      phy_stats->frame = frame;
      phy_stats->dlsch_stats.total_bytes_tx += rel15->TBSize[0];
      phy_stats->dlsch_stats.current_RI = rel15->nrOfLayers;  // 层数（Rank Indicator）
      phy_stats->dlsch_stats.current_Qm = rel15->qamModOrder[0];  // 调制阶数
    }

    /* 计算最大缓冲区大小：每层最大段数 × 层数 × 每段大小 */
    int max_bytes = MAX_NUM_NR_DLSCH_SEGMENTS_PER_LAYER * rel15->nrOfLayers * 1056;
    int B;  // 添加 CRC 后的传输块大小

    /* CRC 添加：根据传输块大小选择 CRC 类型（38.212 Section 5.1）*/
    if (A > NR_MAX_PDSCH_TBS) {
      /* 大传输块：添加 24-bit CRC（CRC24A 多项式）*/
      crc = crc24a(a, A) >> 8;
      a[A >> 3] = ((uint8_t *)&crc)[2];      // CRC 高字节
      a[1 + (A >> 3)] = ((uint8_t *)&crc)[1]; // CRC 中字节
      a[2 + (A >> 3)] = ((uint8_t *)&crc)[0]; // CRC 低字节
      B = A + 24;  // 总比特数 = 原始比特数 + 24bit CRC

      /* 检查缓冲区大小并拷贝数据 */
      AssertFatal((A / 8) + 4 <= max_bytes, "A %d is too big (A/8+4 = %d > %d)\n", A, (A / 8) + 4, max_bytes);
      memcpy(harq->b, a, (A / 8) + 4); // +4 字节确保对齐
    } else {
      /* 小传输块：添加 16-bit CRC（CRC16 多项式）*/
      crc = crc16(a, A) >> 16;
      a[A >> 3] = ((uint8_t *)&crc)[1];      // CRC 高字节
      a[1 + (A >> 3)] = ((uint8_t *)&crc)[0]; // CRC 低字节
      B = A + 16;  // 总比特数 = 原始比特数 + 16bit CRC

      /* 检查缓冲区大小并拷贝数据 */
      AssertFatal((A / 8) + 3 <= max_bytes, "A %d is too big (A/8+3 = %d > %d)\n", A, (A / 8) + 3, max_bytes);
      memcpy(harq->b, a, (A / 8) + 3); // +3 字节用于对齐
    }

    /* 设置传输块编码参数结构体 */
    nrLDPC_TB_encoding_parameters_t *TB_parameters = &TBs[dlsch_id];

    /* 设置 LDPC 编码参数
     * 数据来源说明：
     * - BG: 来自 DCI 中的基图指示
     * - Z: 来自之前的计算或默认值
     * - A: 原始传输块大小（比特数）*/
    TB_parameters->harq_unique_pid = dlsch_id;  // 使用 dlsch_id 作为唯一标识
    TB_parameters->BG = rel15->maintenance_parms_v3.ldpcBaseGraph;  // LDPC 基图类型（1或2）
    TB_parameters->Z = harq->Z;   // 提升因子（将由分段函数更新）
    TB_parameters->A = A;         // 原始传输块大小

    /* 调用传输块分段函数：将大的传输块分割成适合 LDPC 编码的代码块
     * 输入：harq->b（添加CRC后的TB），B（TB+CRC大小），BG（基图类型）
     * 输出：harq->c[]（分段后的代码块数组），C（代码块数），K（每个CB大小），Z（提升因子），F（填充位数）*/
    start_meas(dlsch_segmentation_stats);
    TB_parameters->Kb = nr_segmentation(harq->b,           // 输入：添加CRC后的传输块
                                        harq->c,           // 输出：代码块数组
                                        B,                 // 输入：传输块+CRC总大小
                                        &TB_parameters->C, // 输出：代码块数量
                                        &TB_parameters->K, // 输出：每个代码块大小
                                        &TB_parameters->Z, // 输出：最终提升因子
                                        &TB_parameters->F, // 输出：填充比特数
                                        TB_parameters->BG); // 输入：基图类型
    stop_meas(dlsch_segmentation_stats);

    /* 检查代码块数量是否超出系统限制 */
    if (TB_parameters->C > MAX_NUM_NR_DLSCH_SEGMENTS_PER_LAYER * rel15->nrOfLayers) {
      LOG_E(PHY, "nr_segmentation.c: too many segments %d, B %d\n", TB_parameters->C, B);
      return (-1);
    }
    num_segments += TB_parameters->C;  // 累计所有传输块的段数
  }

  /* 第二阶段：LDPC 编码参数设置
   * 为所有代码段分配参数结构体数组 */
  nrLDPC_segment_encoding_parameters_t segments[num_segments];
  memset(segments, 0, sizeof(segments));
  size_t segments_offset = 0;  // 段参数数组的偏移量
  size_t dlsch_offset = 0;     // 输出缓冲区的偏移量

  /* 遍历所有传输块，设置 LDPC 编码和速率匹配参数 */
  for (int dlsch_id = 0; dlsch_id < msgTx->num_pdsch_slot; dlsch_id++) {
    NR_gNB_DLSCH_t *dlsch = msgTx->dlsch[dlsch_id];
    NR_DL_gNB_HARQ_t *harq = &dlsch->harq_process;
    nfapi_nr_dl_tti_pdsch_pdu_rel15_t *rel15 = &harq->pdsch_pdu.pdsch_pdu_rel15;

    nrLDPC_TB_encoding_parameters_t *TB_parameters = &TBs[dlsch_id];

#ifdef DEBUG_DLSCH_CODING
    for (int r = 0; r < TB_parameters->C; r++) {
      LOG_D(PHY, "Encoder: B %d F %d \n", harq->B, TB_parameters->F);
      LOG_D(PHY, "start ldpc encoder segment %d/%d\n", r, TB_parameters->C);
      LOG_D(PHY, "input %d %d %d %d %d \n", harq->c[r][0], harq->c[r][1], harq->c[r][2], harq->c[r][3], harq->c[r][4]);
      for (int cnt = 0; cnt < 22 * (TB_parameters->Z) / 8; cnt++) {
        LOG_D(PHY, "%d ", harq->c[r][cnt]);
      }
      LOG_D(PHY, "\n");
    }
#endif

    /* 设置物理层传输参数（来自 DCI 和资源分配）*/
    TB_parameters->nb_rb = rel15->rbSize;           // 分配的资源块数
    TB_parameters->Qm = rel15->qamModOrder[0];      // 调制阶数（QPSK=2, 16QAM=4, 64QAM=6, 256QAM=8）
    TB_parameters->mcs = rel15->mcsIndex[0];        // 调制编码方案索引

    /* 4×4 MIMO层数设置和验证 */
    TB_parameters->nb_layers = rel15->nrOfLayers;   // 层数（MIMO层数，1/2/4）
    AssertFatal(TB_parameters->nb_layers > 0 && TB_parameters->nb_layers <= 4,
                "Invalid number of layers: %d (must be 1-4 for 4×4 MIMO)\n", TB_parameters->nb_layers);

    /* 4×4 MIMO特殊处理 */
    if (TB_parameters->nb_layers == 4) {
      LOG_I(PHY, "4×4 MIMO encoding: TB %d, layers=%d, RBs=%d, MCS=%d\n",
            dlsch_id, TB_parameters->nb_layers, TB_parameters->nb_rb, TB_parameters->mcs);
    }

    TB_parameters->rv_index = rel15->rvIndex[0];    // 冗余版本索引（HARQ用）

    /* 计算 DMRS 占用的 RE 数量（每个 PRB）
     * Type1: 6个RE × CDM组数，Type2: 4个RE × CDM组数 */
    int nb_re_dmrs =
        (rel15->dmrsConfigType == NFAPI_NR_DMRS_TYPE1) ? (6 * rel15->numDmrsCdmGrpsNoData) : (4 * rel15->numDmrsCdmGrpsNoData);

    /* 计算总可用编码比特数 G（这是速率匹配的目标）
     * G = (每PRB可用RE数) × PRB数 × 调制阶数 × 层数 */
    TB_parameters->G = nr_get_G(rel15->rbSize,              // PRB 数
                                rel15->NrOfSymbols,         // 数据符号数
                                nb_re_dmrs,                 // 每PRB的DMRS RE数
                                get_num_dmrs(rel15->dlDmrsSymbPos), // DMRS符号数
                                harq->unav_res,             // 不可用资源
                                rel15->qamModOrder[0],      // 调制阶数
                                rel15->nrOfLayers);         // 层数（4×4 MIMO时为4）

    /* LBRM（Limited Buffer Rate Matching）参数 */
    TB_parameters->tbslbrm = rel15->maintenance_parms_v3.tbSizeLbrmBytes;

    /* 设置输出缓冲区指针和段参数数组指针 */
    TB_parameters->output = &output[dlsch_offset >> 3];      // 输出比特流缓冲区
    TB_parameters->segments = &segments[segments_offset];    // 段参数数组

    /* 为每个代码段设置编码参数 */
    for (int r = 0; r < TB_parameters->C; r++) {
      nrLDPC_segment_encoding_parameters_t *segment_parameters = &TB_parameters->segments[r];
      segment_parameters->c = harq->c[r];  // 指向第 r 个代码段的数据

      /* 计算第 r 个代码段的速率匹配输出比特数 E
       * E 由总比特数 G 按代码段数 C 分配，考虑调制阶数和层数 */
      segment_parameters->E = nr_get_E(TB_parameters->G,      // 总可用比特数
                                       TB_parameters->C,      // 代码段数
                                       TB_parameters->Qm,     // 调制阶数
                                       rel15->nrOfLayers,     // 层数
                                       r);                    // 当前段索引

      /* 重置性能统计计时器 */
      reset_meas(&segment_parameters->ts_interleave);    // 交织时间
      reset_meas(&segment_parameters->ts_rate_match);    // 速率匹配时间
      reset_meas(&segment_parameters->ts_ldpc_encode);   // LDPC编码时间
    }

    segments_offset += TB_parameters->C;  // 更新段偏移量

    /* 计算输出缓冲区偏移量：确保 64 字节对齐
     * 每个 DLSCH 的输出大小 = RB数 × 符号数 × 子载波数 × 调制阶数 × 层数
     * 4×4 MIMO: 层数为4时，输出大小约为2×2 MIMO的2倍 */
    const size_t dlsch_size = rel15->rbSize * NR_SYMBOLS_PER_SLOT * NR_NB_SC_PER_RB * rel15->qamModOrder[0] * rel15->nrOfLayers;

    /* 4×4 MIMO缓冲区大小检查 */
    if (rel15->nrOfLayers == 4) {
      AssertFatal(dlsch_size <= (1UL << 24), "4×4 MIMO: DLSCH size %zu exceeds 16MB limit\n", dlsch_size);
      LOG_D(PHY, "4×4 MIMO: DLSCH %d size = %zu bytes (RB=%d, layers=%d)\n",
            dlsch_id, dlsch_size, rel15->rbSize, rel15->nrOfLayers);
    }

    dlsch_offset += ceil_mod(dlsch_size, 8 * 64);  // 向上对齐到 64 字节边界
  }

  /* 第三阶段：执行 LDPC 编码
   * 调用 LDPC 编码接口，执行以下步骤：
   * 1. LDPC 编码（每个代码段）
   * 2. 速率匹配（nr_rate_matching_ldpc）
   * 3. 比特交织（nr_interleaving_ldpc）
   * 4. 输出比特流组装 */
  gNB->nrLDPC_coding_interface.nrLDPC_coding_encoder(&slot_parameters);

  /* 第四阶段：收集性能统计信息 */
  for (int dlsch_id = 0; dlsch_id < msgTx->num_pdsch_slot; dlsch_id++) {
    nrLDPC_TB_encoding_parameters_t *TB_parameters = &TBs[dlsch_id];
    for (int r = 0; r < TB_parameters->C; r++) {
      nrLDPC_segment_encoding_parameters_t *segment_parameters = &TB_parameters->segments[r];
      /* 合并各个代码段的性能统计数据 */
      merge_meas(dlsch_interleaving_stats, &segment_parameters->ts_interleave);      // 交织耗时
      merge_meas(dlsch_rate_matching_stats, &segment_parameters->ts_rate_match);     // 速率匹配耗时
      // merge_meas(, &segment_parameters->ts_ldpc_encode);  // LDPC编码耗时（暂未使用）
    }
  }

  VCD_SIGNAL_DUMPER_DUMP_FUNCTION_BY_NAME(VCD_SIGNAL_DUMPER_FUNCTIONS_gNB_DLSCH_ENCODING, VCD_FUNCTION_OUT);
  return 0;
}
