/*
 * Licensed to the OpenAirInterface (OAI) Software Alliance under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The OpenAirInterface Software Alliance licenses this file to You under
 * the OAI Public License, Version 1.1  (the "License"); you may not use this file
 * except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.openairinterface.org/?page_id=698
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *-------------------------------------------------------------------------------
 * For more information about the OpenAirInterface (OAI) Software Alliance:
 *      <EMAIL>
 */

/* file: nr_segmentation.c
   purpose: Procedures for transport block segmentation for NR (LDPC-coded transport channels)
   author: Hongzhi WANG (TCL)
   date: 12.09.2017
*/
#include "PHY/defs_nr_UE.h"
//#include "SCHED/extern.h"

//#define DEBUG_SEGMENTATION

/* 中文说明：NR 传输块分段函数
 * 作用：将大的传输块 B 分割成多个代码块，每个代码块适合 LDPC 编码
 * 数据流向：TB(B bits) → 分段 → C个代码块(每个K bits) → LDPC编码
 * 调用位置：
 *   - nr_dlsch_coding.c (PDSCH编码)
 *   - nr_ulsch_coding.c (PUSCH编码)
 *   - nr_dlsch_decoding.c (PDSCH解码)
 *   - nr_ulsch_decoding.c (PUSCH解码)
 *
 * 参数说明：
 * @input_buffer: 输入传输块数据，可为NULL（仅计算参数时）
 * @output_buffers: 输出分段后的代码块数组，可为NULL
 * @B: 传输块大小（比特数）
 * @C: 输出 - 代码块数量（这就是速率匹配中用到的C）
 * @K: 输出 - 每个代码块的大小（比特数）
 * @Zout: 输出 - 提升因子Z（LDPC编码参数）
 * @F: 输出 - 填充比特数（每个代码块）
 * @BG: 输入 - LDPC基图类型（1或2）
 *
 * 返回值：Kb（信息比特列数）
 */
int32_t nr_segmentation(unsigned char *input_buffer,
                        unsigned char **output_buffers,
                        unsigned int B,
                        unsigned int *C,
                        unsigned int *K,
                        unsigned int *Zout, // [hna] Zout is Zc
                        unsigned int *F,
                        uint8_t BG)
{

  unsigned int L,Bprime,Z,r,Kcb,Kb,k,s,crc,Kprime;

  /* 第一步：确定最大代码块大小 Kcb
   * BG1: 8448 bits, BG2: 3840 bits（38.212 Table 5.3.2-1）*/
  if (BG==1)
    Kcb=8448;
  else
    Kcb=3840;

  /* 第二步：判断是否需要分段
   * 如果 TB 大小 B <= Kcb，则不需要分段（C=1）
   * 否则需要分段，并为每个代码块添加 24bit CRC */
  if (B<=Kcb) {
    L=0;        // 无需额外CRC
    *C=1;       // 只有一个代码块
    Bprime=B;   // 总比特数等于TB大小
  } else {
    L=24;       // 每个代码块需要24bit CRC
    *C = B/(Kcb-L);  // 初步计算代码块数

    if ((Kcb-L)*(*C) < B)
      *C=*C+1;  // 向上取整，确保能容纳所有比特

    Bprime = B+((*C)*L);  // 总比特数 = TB + 所有CRC比特
#ifdef DEBUG_SEGMENTATION
    printf("Bprime %u\n",Bprime);
#endif
  }

  /* 第三步：计算每个代码块的信息比特数 Kprime */
  Kprime = Bprime/(*C);

  /* 第四步：确定信息比特列数 Kb（38.212 Table 5.3.2-1）
   * BG1: 固定 22 列
   * BG2: 根据 TB 大小选择 6/8/9/10 列 */
  if (BG==1)
    Kb = 22;
  else {
    if (B > 640) {
      Kb = 10;
    } else if (B > 560) {
      Kb = 9;
    } else if (B > 192) {
      Kb = 8;
    }
    else {
      Kb = 6;
    }
  }


/* 第五步：计算初步提升因子 Z
 * Z 需要满足：Z * Kb >= Kprime */
if ((Kprime%Kb) > 0)
  Z  = (Kprime/Kb)+1;  // 向上取整
else
  Z = (Kprime/Kb);

 LOG_D(PHY,"nr segmentation B %u Bprime %u Kprime %u z %u \n", B, Bprime, Kprime, Z);

  /* 第六步：将 Z 调整为标准支持的提升因子（38.212 Table 5.3.2-1）
   * 支持的 Z 值有特定的集合，需要选择最小的满足条件的值 */
  if (Z <= 2) {
    *K = 2;
  } else if (Z<=16) { // 1的倍数：2,3,4,...,16
    *K = Z;
  } else if (Z <=32) { // 2的倍数：18,20,22,...,32
    *K = (Z>>1)<<1;  // 向下取到2的倍数

    if (*K < Z)
      *K = *K + 2;   // 向上调整到下一个2的倍数

  } else if (Z <= 64) { // 4的倍数：36,40,44,...,64
    *K = (Z>>2)<<2;  // 向下取到4的倍数

    if (*K < Z)
      *K = *K + 4;   // 向上调整到下一个4的倍数

  } else if (Z <=128 ) { // 8的倍数：72,80,88,...,128

    *K = (Z>>3)<<3;  // 向下取到8的倍数

    if (*K < Z)
      *K = *K + 8;   // 向上调整到下一个8的倍数

#ifdef DEBUG_SEGMENTATION
    printf("Z_by_C %u , K2 %u\n",Z,*K);
#endif
  } else if (Z <= 256) { // 16的倍数：144,160,176,...,256
      *K = (Z>>4)<<4;  // 向下取到16的倍数

      if (*K < Z)
        *K = *K + 16;  // 向上调整到下一个16的倍数

  } else if (Z <= 384) { // 32的倍数：288,320,352,384
      *K = (Z>>5)<<5;  // 向下取到32的倍数

      if (*K < Z)
        *K = *K + 32;  // 向上调整到下一个32的倍数

  } else {
    // 超出支持范围
    return -1;
  }

  *Zout = *K;  // 输出最终的提升因子 Z

  /* 第七步：计算每个代码块的总长度 K（信息位+校验位）
   * BG1: K = Z * 22（22列信息位）
   * BG2: K = Z * 10（10列信息位）*/
  if(BG==1)
    *K = *K*22;
  else
    *K = *K*10;

  /* 第八步：计算填充比特数 F
   * F = K - Kprime，用于填充到标准长度 */
  *F = ((*K) - Kprime);

  LOG_D(PHY,"final nr seg output Z %u K %u F %u \n", *Zout, *K, *F);
  LOG_D(PHY,"C %u, K %u, Bprime_bytes %u, Bprime %u, F %u\n",*C,*K,Bprime>>3,Bprime,*F);

  if ((input_buffer) && (output_buffers)) {

    s = 0;

    for (r=0; r<*C; r++) {

      k = 0;
      memcpy(output_buffers[r],input_buffer+s,(Kprime-L)>>3);
      s+=(Kprime-L)>>3;

      if (*C > 1) { // add CRC
        crc = crc24b(output_buffers[r],Kprime-L)>>8;
        output_buffers[r][(Kprime-L)>>3] = ((uint8_t*)&crc)[2];
        output_buffers[r][1+((Kprime-L)>>3)] = ((uint8_t*)&crc)[1];
        output_buffers[r][2+((Kprime-L)>>3)] = ((uint8_t*)&crc)[0];
      }

      if (*F>0) {
        for (k=Kprime>>3; k<(*K)>>3; k++) {
          output_buffers[r][k] = 0;
          //printf("r %d filler bits [%d] = %d Kprime %d \n", r,k, output_buffers[r][k], Kprime);
        }
      }

    }
  }

  return Kb;
}



#ifdef MAIN
main()
{

  unsigned int K,C,F,Bbytes, Zout;

  for (Bbytes=5; Bbytes<8; Bbytes++) {
    nr_segmentation(0,0,Bbytes<<3,&C,&K,&Zout, &F);
    printf("Bbytes %u : C %u, K %u, F %u\n",
           Bbytes, C, K, F);
  }
}
#endif
