/*
 * Licensed to the OpenAirInterface (OAI) Software Alliance under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The OpenAirInterface Software Alliance licenses this file to You under
 * the OAI Public License, Version 1.0  (the "License"); you may not use this file
 * except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.openairinterface.org/?page_id=698
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *-------------------------------------------------------------------------------
 * For more information about the OpenAirInterface (OAI) Software Alliance:
 *      <EMAIL>
 */

/*! \file PHY/CODING/nrLDPC_coding/nrLDPC_coding_segment/nrLDPC_coding_segment_encoder.c
 * \brief Top-level routines for implementing LDPC encoding of transport channels
 */
/* 中文说明：本文件实现“时隙级 LDPC 编码”的软件路径（以代码段为单位组织），
 * 串联 NR 38.212 的关键步骤：段级 LDPC 编码 → 速率匹配 → 比特交织 → 多段打包输出。
 * 核心优化点：按 8 段并行进行比特打包，分别针对 AVX512/AVX2/ARM NEON 做了 SIMD 优化。
 *
 * 详细功能说明：
 * 1. 时隙级编码管理：协调一个时隙内多个传输块的编码
 * 2. 传输块级编码：管理单个传输块的多个代码段编码
 * 3. 段级编码：执行单个代码段的完整编码流程
 * 4. 多线程优化：支持并行编码多个代码段
 * 5. SIMD 优化：针对不同架构的位操作优化
 *
 * 主要函数：
 * - nrLDPC_coding_encoder(): 时隙级编码入口
 * - nrLDPC_launch_TB_encoding(): 传输块级编码管理
 * - ldpc8blocks(): 8个代码段并行编码
 * - write_task_output(): 多段比特流打包输出
 *
 * 调用关系：
 * nr_dlsch_coding.c → nrLDPC_coding_encoder() → nrLDPC_launch_TB_encoding() → ldpc8blocks()
 *
 * 数据流向：
 * harq->c[r] → LDPC编码 → 速率匹配 → 交织 → f/f2缓冲区 → 最终输出比特流
 */


#include "nr_rate_matching.h"
#include "PHY/defs_gNB.h"
#include "PHY/CODING/coding_extern.h"
#include "PHY/CODING/coding_defs.h"
#include "PHY/CODING/lte_interleaver_inline.h"
#include "PHY/CODING/nrLDPC_coding/nrLDPC_coding_interface.h"
#include "PHY/CODING/nrLDPC_extern.h"
#include "PHY/NR_TRANSPORT/nr_transport_proto.h"
#include "PHY/NR_TRANSPORT/nr_transport_common_proto.h"
#include "PHY/NR_TRANSPORT/nr_dlsch.h"
#include "SCHED_NR/sched_nr.h"
#include "common/utils/LOG/vcd_signal_dumper.h"
#include "common/utils/LOG/log.h"
#include "common/utils/nr/nr_common.h"
#include <openair2/UTIL/OPT/opt.h>
#include "PHY/CODING/mimo_4x4_config.h"  // 4×4 MIMO配置支持

#include <syscall.h>

#define DEBUG_LDPC_ENCODING
//#define DEBUG_LDPC_ENCODING_FREE 1

/* 函数：write_task_output - 多段比特流打包输出
 * 作用：将 8 个代码段的交织输出打包成连续的比特流
 * 调用位置：在 ldpc8blocks() 中，完成所有段的编码和交织后调用
 *
 * 核心功能：
 * 1. 段间解交织：将并行打包的段数据分离成独立的段
 * 2. 段内重排：将每个段的比特按正确顺序排列
 * 3. 连续拼接：将所有段的比特流连接成最终输出
 * 4. SIMD 优化：使用 AVX512/AVX2/NEON 加速位操作
 *
 * 数据流向：
 * f[段间并行] + f2[段间并行] → 段间解交织 → 段内重排 → output[连续比特流]
 */

/**
 * \brief write nrLDPC_coding_segment_encoder output with interleaver output
 * the interleaver output has 8 bits from 8 different segments per byte
 * nrLDPC_coding_segment_encoder is made of concatenated packed segments
 * 参数详细说明：
 * \param f 输入 - 第一组交织后的段数据
 *          来源：ldpc8blocks() 中对前 E2_first_segment 个段的交织输出
 *          格式：每字节包含来自 8 个不同段的 8 个比特（段间并行打包）
 *          长度：每个段 E 个比特
 * \param E 第一组段的比特数
 *          来源：nr_get_E() 计算的较小 E 值
 *          作用：确定第一组段的数据长度
 * \param f2 输入 - 第二组交织后的段数据（当存在 E 差异时）
 *           来源：ldpc8blocks() 中对后续段的交织输出
 *           格式：与 f 相同的段间并行打包格式
 *           长度：每个段 E2 个比特
 * \param E2 第二组段的比特数
 *           来源：nr_get_E() 计算的较大 E 值
 *           作用：确定第二组段的数据长度
 * \param Eshift E 值差异标志
 *               来源：ldpc8blocks() 中的逻辑判断
 *               作用：指示是否存在两种不同的 E 值
 *               true: 存在 E 和 E2 两种长度，false: 所有段长度相同
 * \param E2_first_segment 第二组段的起始索引
 *                         来源：ldpc8blocks() 中的分组逻辑
 *                         作用：确定哪些段使用 E，哪些段使用 E2
 * \param nb_segments 当前组的段数（通常为 8）
 *                    来源：ldpc8blocks() 的批处理大小
 *                    作用：确定并行处理的段数量
 * \param output 输出 - 最终的连续比特流缓冲区
 *               去向：传递给调制器或资源映射模块
 *               格式：按段顺序连接的比特流
 * \param Eoffset 当前组在输出缓冲区中的比特偏移
 *                来源：前面已处理段组的累计比特数
 *                作用：确定当前组数据在输出缓冲区中的起始位置
 */
  /* 中文：将交织输出按“每字节来自 8 个段的 8 个比特”的格式写入输出缓冲，
   *        支持组内存在两种不同 E（E 与 E2）的情况，并据此对齐不同段的起止偏移。*/

static void write_task_output(uint8_t *f,
                              uint32_t E,
                              uint8_t *f2,
                              uint32_t E2,
                              bool Eshift,
                              uint32_t E2_first_segment,
                              uint32_t nb_segments,
                              uint8_t *output,
                              uint32_t Eoffset)
{

#if defined(__AVX512VBMI__)
  uint64_t *output_p = (uint64_t*)output;
  __m512i inc = _mm512_set1_epi8(0x1);

  for (int i=0;i<E2;i+=64) {
    uint32_t Eoffset2 = Eoffset;
    __m512i bitperm = _mm512_set1_epi64(0x3830282018100800);
    if (i<E) {
      for (int j=0; j < E2_first_segment; j++) {
        // Note: Here and below for AVX2, we are using the 64-bit SIMD instruction
        // 中文：此处（以及下方 AVX2 的对应实现）使用 64bit SIMD 移位而非 C 的 << >>，
        //       因当偏移为 64 或 0 时，C 的位移未定义，而 SIMD 版本能给出期望的 0。*/

        // instead of C >>/<< because when the Eoffset2_bit is 64 or 0, the <<
        // and >> operations are undefined and in fact don't give "0" which is
        // what we want here. The SIMD version do give 0 when the shift is 64
        uint32_t Eoffset2_byte = Eoffset2 >> 6;
        uint32_t Eoffset2_bit = Eoffset2 & 63;
        __m64 tmp = (__m64)_mm512_bitshuffle_epi64_mask(((__m512i *)f)[i >> 6],bitperm);
        *(__m64*)(output_p + Eoffset2_byte)   = _mm_or_si64(*(__m64*)(output_p + Eoffset2_byte),_mm_slli_si64(tmp,Eoffset2_bit));
        *(__m64*)(output_p + Eoffset2_byte+1) = _mm_or_si64(*(__m64*)(output_p + Eoffset2_byte+1),_mm_srli_si64(tmp,(64-Eoffset2_bit)));
        Eoffset2 += E;
        bitperm = _mm512_add_epi8(bitperm ,inc);
      }
    } else {
      for (int j=0; j < E2_first_segment; j++) {
        Eoffset2 += E;
        bitperm = _mm512_add_epi8(bitperm ,inc);
      }
    }
    for (int j=E2_first_segment; j < nb_segments; j++) {
      uint32_t Eoffset2_byte = Eoffset2 >> 6;
      uint32_t Eoffset2_bit = Eoffset2 & 63;
      __m64 tmp = (__m64)_mm512_bitshuffle_epi64_mask(((__m512i *)f2)[i >> 6],bitperm);
      *(__m64*)(output_p + Eoffset2_byte)   = _mm_or_si64(*(__m64*)(output_p + Eoffset2_byte),_mm_slli_si64(tmp,Eoffset2_bit));
      *(__m64*)(output_p + Eoffset2_byte+1) = _mm_or_si64(*(__m64*)(output_p + Eoffset2_byte+1),_mm_srli_si64(tmp,(64-Eoffset2_bit)));
      Eoffset2 += E2;
      bitperm = _mm512_add_epi8(bitperm ,inc);
    }
    output_p++;
  }

#elif defined(__aarch64__)
  uint16_t *output_p = (uint16_t*)output;
  const int8_t __attribute__ ((aligned (16))) ucShift[8][16] = {
    {0,1,2,3,4,5,6,7,0,1,2,3,4,5,6,7},     // segment 0
    {-1,0,1,2,3,4,5,6,-1,0,1,2,3,4,5,6},   // segment 1
    {-2,-1,0,1,2,3,4,5,-2,-1,0,1,2,3,4,5}, // segment 2
    {-3,-2,-1,0,1,2,3,4,-3,-2,-1,0,1,2,3,4}, // segment 3
    {-4,-3,-2,-1,0,1,2,3,-4,-3,-2,-1,0,1,2,3}, // segment 4
    {-5,-4,-3,-2,-1,0,1,2,-5,-4,-3,-2,-1,0,1,2}, // segment 5
    {-6,-5,-4,-3,-2,-1,0,1,-6,-5,-4,-3,-2,-1,0,1}, // segment 6
    {-7,-6,-5,-4,-3,-2,-1,0,-7,-6,-5,-4,-3,-2,-1,0}}; // segment 7
  const uint8_t __attribute__ ((aligned (16))) masks[16] =
      {0x1,0x2,0x4,0x8,0x10,0x20,0x40,0x80,0x1,0x2,0x4,0x8,0x10,0x20,0x40,0x80};
  int8x16_t vshift[8];
  for (int n=0;n<8;n++) vshift[n] = vld1q_s8(ucShift[n]);
  uint8x16_t vmask  = vld1q_u8(masks);

  for (int i=0;i<E2;i+=16) {
    uint32_t Eoffset2 = Eoffset;
    if (i<E) {
      for (int j=0; j < E2_first_segment; j++) {
        uint32_t Eoffset2_byte = Eoffset2 >> 4;
        uint32_t Eoffset2_bit = Eoffset2 & 15;
        uint8x16_t cshift = vandq_u8(vshlq_u8(((uint8x16_t*)f)[i >> 4],vshift[j]),vmask);
        int32_t tmp = (int)vaddv_u8(vget_low_u8(cshift));
        tmp += (int)(vaddv_u8(vget_high_u8(cshift))<<8);
        *(output_p + Eoffset2_byte)   |= (uint16_t)(tmp<<Eoffset2_bit);
        *(output_p + Eoffset2_byte+1) |= (uint16_t)(tmp>>(16-Eoffset2_bit));
        Eoffset2 += E;
      }
    } else {
      for (int j=0; j < E2_first_segment; j++) {
        Eoffset2 += E;
      }
    }
    for (int j=E2_first_segment; j < nb_segments; j++) {
      uint32_t Eoffset2_byte = Eoffset2 >> 4;
      uint32_t Eoffset2_bit = Eoffset2 & 15;
      uint8x16_t cshift = vandq_u8(vshlq_u8(((uint8x16_t*)f2)[i >> 4],vshift[j]),vmask);
      int32_t tmp = (int)vaddv_u8(vget_low_u8(cshift));
      tmp += (int)(vaddv_u8(vget_high_u8(cshift))<<8);
      *(output_p + Eoffset2_byte)   |= (uint16_t)(tmp<<Eoffset2_bit);
      *(output_p + Eoffset2_byte+1) |= (uint16_t)(tmp>>(16-Eoffset2_bit));
      Eoffset2 += E2;
    }
    output_p++;
  }

#else
  uint32_t *output_p = (uint32_t*)output;

  for (int i=0; i < E2; i += 32) {
    uint32_t Eoffset2 = Eoffset;
    if (i < E) {
      for (int j = 0; j < E2_first_segment; j++) {
        // Note: Here and below, we are using the 64-bit SIMD instruction
        // instead of C >>/<< because when the Eoffset2_bit is 64 or 0, the <<
        // and >> operations are undefined and in fact don't give "0" which is
        // what we want here. The SIMD version do give 0 when the shift is 64
        uint32_t Eoffset2_byte = Eoffset2 >> 5;
        uint32_t Eoffset2_bit = Eoffset2 & 31;
        int tmp = _mm256_movemask_epi8(_mm256_slli_epi16(((__m256i *)f)[i >> 5], 7 - j));
        __m64 tmp64 = _mm_set1_pi32(tmp);
        __m64 out64 = _mm_set_pi32(*(output_p + Eoffset2_byte + 1), *(output_p + Eoffset2_byte));
        __m64 tmp64b = _mm_or_si64(out64, _mm_slli_pi32(tmp64, Eoffset2_bit));
        __m64 tmp64c = _mm_or_si64(out64, _mm_srli_pi32(tmp64, (32 - Eoffset2_bit)));
        *(output_p + Eoffset2_byte) = _m_to_int(tmp64b);
        *(output_p + Eoffset2_byte + 1) = _m_to_int(_mm_srli_si64(tmp64c, 32));
        Eoffset2 += E;
      }
    } else {
      for (int j = 0; j < E2_first_segment; j++) {
        Eoffset2 += E;
      }
    }
    for (int j = E2_first_segment; j < nb_segments; j++) {
      uint32_t Eoffset2_byte = Eoffset2 >> 5;
      uint32_t Eoffset2_bit = Eoffset2 & 31;
      int tmp = _mm256_movemask_epi8(_mm256_slli_epi16(((__m256i *)f2)[i >> 5], 7 - j));
      __m64 tmp64 = _mm_set1_pi32(tmp);
      __m64 out64 = _mm_set_pi32(*(output_p + Eoffset2_byte + 1), *(output_p + Eoffset2_byte));
      __m64 tmp64b = _mm_or_si64(out64, _mm_slli_pi32(tmp64, Eoffset2_bit));
      __m64 tmp64c = _mm_or_si64(out64, _mm_srli_pi32(tmp64, (32 - Eoffset2_bit)));
      *(output_p + Eoffset2_byte)  = _m_to_int(tmp64b);
      *(output_p + Eoffset2_byte + 1) = _m_to_int(_mm_srli_si64(tmp64c, 32));
      Eoffset2 += E2;
    }
    output_p++;
  }


#endif
}

/* 结构体：ldpc8blocks_args_t - 8段并行编码任务的参数结构
 * 作用：封装传递给 ldpc8blocks() 函数的所有参数
 * 使用场景：多线程编码时，每个线程处理一个任务
 *
 * 成员详细说明：
 * @nrLDPC_TB_encoding_parameters: 传输块编码参数
 *                                 来源：nr_dlsch_coding.c 中设置的TB级参数
 *                                 包含：BG, Z, C, G, Qm, nb_rb, rv_index等
 * @impp: 编码器实现相关参数
 *        来源：nrLDPC_launch_TB_encoding() 中为每个任务设置
 *        包含：first_seg, n_segments, K, F, Zc等段级参数
 * @f: 第一组交织输出缓冲区
 *     来源：nrLDPC_launch_TB_encoding() 中分配
 *     用途：存储前 E2_first_segment 个段的交织输出
 *     格式：段间并行打包（每字节来自8个段的8个比特）
 * @f2: 第二组交织输出缓冲区
 *      来源：nrLDPC_launch_TB_encoding() 中分配
 *      用途：存储后续段的交织输出（当存在E值差异时）
 *      格式：与f相同的段间并行打包格式
 */

/**
 * \typedef ldpc8blocks_args_t
 * \struct ldpc8blocks_args_s
 * \brief Arguments of an encoding task
 * encode up to 8 code blocks
 * \var nrLDPC_TB_encoding_parameters TB encoding parameters as defined in the coding library interface
 * \var impp encoder implementation specific parameters for the task
 * \var f first interleaver output to be filled by the task
 * \var f2 second interleaver output to be filled by the task
 * in case of a shift of E in the code blocks group processed by the task
 */
typedef struct ldpc8blocks_args_s {
  nrLDPC_TB_encoding_parameters_t *nrLDPC_TB_encoding_parameters;
  encoder_implemparams_t impp;
  uint8_t *f;
  uint8_t *f2;
} ldpc8blocks_args_t;

/* 函数：ldpc8blocks - 8个代码段并行编码的核心函数
 * 作用：执行最多8个代码段的完整编码流程（LDPC编码→速率匹配→交织）
 * 调用位置：由 nrLDPC_launch_TB_encoding() 通过线程池调用
 *
 * 参数说明：
 * @p: 指向 ldpc8blocks_args_t 结构的void指针
 *     来源：nrLDPC_launch_TB_encoding() 中为每个任务准备的参数包
 *     包含：TB参数、实现参数、输出缓冲区指针
 *
 * 核心流程：
 * 1. 参数解析和初始化
 * 2. LDPC 编码（调用 LDPCencoder）
 * 3. E 值分析（检测是否存在不同的E值）
 * 4. 速率匹配（调用 nr_rate_matching_ldpc）
 * 5. 比特交织（调用 nr_interleaving_ldpc）
 * 6. 段间并行打包（SIMD优化的位操作）
 *
 * 优化特点：
 * - 批处理：一次处理8个段，提高缓存效率
 * - SIMD优化：使用向量指令加速位操作
 * - 内存对齐：64字节对齐的缓冲区提高访问效率
 * - 分组处理：支持不同E值的段混合处理
 */
static void ldpc8blocks(void *p)
{
  /* 参数解析：从void指针恢复具体的参数结构 */
  ldpc8blocks_args_t *args = (ldpc8blocks_args_t *)p;
  nrLDPC_TB_encoding_parameters_t *nrLDPC_TB_encoding_parameters = args->nrLDPC_TB_encoding_parameters;
  encoder_implemparams_t *impp = &args->impp;

  /* 提取关键编码参数 */
  uint8_t mod_order = nrLDPC_TB_encoding_parameters->Qm;    // 调制阶数（来自MCS表）
  uint16_t nb_rb = nrLDPC_TB_encoding_parameters->nb_rb;    // 资源块数（来自调度）
  uint32_t A = nrLDPC_TB_encoding_parameters->A;           // 原始TB大小（来自MAC）

  unsigned int G = nrLDPC_TB_encoding_parameters->G;           // 总可用编码比特数（来自资源分配）
  LOG_D(PHY, "dlsch coding A %d K %d G %d (nb_rb %d, mod_order %d)\n", A, impp->K, G, nb_rb, (int)mod_order);

  /* 第一步：准备LDPC编码的输入输出缓冲区 */
  // LDPC编码器输出缓冲区：64字节对齐以优化SIMD访问
  uint8_t d[68 * 384] __attribute__((aligned(64)));

  // 代码段指针数组：指向各个代码段的输入数据
  uint8_t *c[nrLDPC_TB_encoding_parameters->C];

  // 当前任务处理的段范围
  unsigned int macro_segment, macro_segment_end;

  /* 确定当前任务处理的段范围：最多8个段 */
  macro_segment = impp->first_seg;                          // 起始段索引
  macro_segment_end = (impp->n_segments > impp->first_seg + 8) ? impp->first_seg + 8 : impp->n_segments;  // 结束段索引
  /* 第二步：设置代码段指针数组，指向各个段的输入数据 */
  for (int r = 0; r < nrLDPC_TB_encoding_parameters->C; r++)
    c[r] = nrLDPC_TB_encoding_parameters->segments[r].c;    // 来自harq->c[r]，经过分段的代码块数据

  /* 第三步：执行LDPC编码 */
  start_meas(&nrLDPC_TB_encoding_parameters->segments[impp->first_seg].ts_ldpc_encode);
  LDPCencoder(c, d, impp);                                  // 调用LDPC编码器：c[输入] → d[输出]
  stop_meas(&nrLDPC_TB_encoding_parameters->segments[impp->first_seg].ts_ldpc_encode);

#ifdef DEBUG_LDPC_ENCODING
  LOG_D(PHY, "rvidx in encoding = %d\n", nrLDPC_TB_encoding_parameters->rv_index);
#endif

  /* 第四步：分析E值分布，检测是否存在不同的E值 */
  const uint32_t E = nrLDPC_TB_encoding_parameters->segments[macro_segment].E;  // 第一个段的E值
  uint32_t E2=E;                                            // 第二种E值（如果存在）
  bool Eshift=false;                                        // E值差异标志
  uint32_t Emax = E;                                        // 最大E值

  /* 遍历当前组的所有段，检测E值是否一致 */
  for (int s=macro_segment;s<macro_segment_end;s++)
      if (nrLDPC_TB_encoding_parameters->segments[s].E != E) {
	 E2=nrLDPC_TB_encoding_parameters->segments[s].E;      // 发现不同的E值
         Eshift=true;                                           // 标记存在E值差异
         if(E2 > Emax)
           Emax = E2;                                           // 更新最大E值
         break;                                                 // 只需要找到第一个不同的E值
      }


  LOG_D(NR_PHY,
        "Rate Matching, Code segment %d...%d/%d (coded bits (G) %u, E %d, E2 %d Filler bits %d, Filler offset %d mod_order %d, nb_rb "
          "%d,nrOfLayer %d)...\n",
        macro_segment,
        macro_segment_end,
        impp->n_segments,
        G,
        E,E2,
        impp->F,
        impp->K - impp->F - 2 * impp->Zc,
        mod_order,
        nb_rb,
        nrLDPC_TB_encoding_parameters->nb_layers);
/*
  printf("Rate Matching, Code segment %d...%d/%d (coded bits (G) %u, E %d, E2 %d Filler bits %d, Filler offset %d mod_order %d, nb_rb "
          "%d,nrOfLayer %d)...\n",
        macro_segment,
        macro_segment_end,
        impp->n_segments,
        G,
        E,E2,
        impp->F,
        impp->K - impp->F - 2 * impp->Zc,
        mod_order,
        nb_rb,
        nrLDPC_TB_encoding_parameters->nb_layers);
*/
  /* 第五步：准备速率匹配参数 */
  uint32_t Tbslbrm = nrLDPC_TB_encoding_parameters->tbslbrm;    // LBRM参数（来自RRC配置）

  /* 分配速率匹配输出缓冲区：64字节对齐以优化SIMD访问
   * 4×4 MIMO优化：支持更大的Emax值 */
  uint8_t *e = NULL;
  if (Emax > 65536) {  // 64KB阈值
    /* 4×4 MIMO大缓冲区：使用堆分配避免栈溢出 */
    e = (uint8_t *)aligned_alloc(64, Emax);
    AssertFatal(e != NULL, "4×4 MIMO: Failed to allocate rate matching buffer (size=%u)\n", Emax);
    LOG_D(PHY, "4×4 MIMO: Using heap allocation for rate matching buffer (%u bytes)\n", Emax);
  } else {
    /* 小缓冲区：使用栈分配 */
    static uint8_t e_stack[65536] __attribute__((aligned(64)));
    e = e_stack;
  }

  /* 获取交织输出缓冲区指针：来自上层分配的段间并行缓冲区 */
  uint8_t *f = args->f;                                         // 第一组段的交织输出
  uint8_t *f2 = args->f2;                                       // 第二组段的交织输出（如果存在E差异）

  /* 初始化缓冲区为0 */
  bzero(e, Emax);                                               // 清零速率匹配输出缓冲区
  bzero(f, ceil_mod(E, 64));                                    // 清零第一组交织输出（64字节对齐）
  if (Eshift) {
    bzero(f2, ceil_mod(E2, 64));                                // 清零第二组交织输出（如果需要）
  }

  /* 第六步：执行速率匹配 */
  start_meas(&nrLDPC_TB_encoding_parameters->segments[macro_segment].ts_rate_match);
  nr_rate_matching_ldpc(Tbslbrm,                                // LBRM参数
                        impp->BG,                               // 基图类型（1或2）
                        impp->Zc,                               // 提升因子
                        d,                                      // 输入：LDPC编码输出
                        e,                                      // 输出：速率匹配后的比特
                        impp->n_segments,                       // 代码段总数
                        impp->F,                                // 填充比特数
                        impp->K - impp->F - 2 * impp->Zc,      // 填充比特偏移
                        nrLDPC_TB_encoding_parameters->rv_index, // 冗余版本索引
                        Emax);                                  // 输出比特数

  stop_meas(&nrLDPC_TB_encoding_parameters->segments[macro_segment].ts_rate_match);
  if (impp->K - impp->F - 2 * impp->Zc > E) {
    LOG_E(PHY,
          "dlsch coding A %d  Kr %d G %d (nb_rb %d, mod_order %d)\n",
          A,
          impp->K,
          G,
          nb_rb,
          (int)mod_order);

    LOG_E(NR_PHY,
          "Rate Matching, Code segments %d...%d/%d (coded bits (G) %u, E %d, Kr %d, Filler bits %d, Filler offset %d mod_order %d, "
          "nb_rb %d)...\n",
          macro_segment,
	  macro_segment_end,
          impp->n_segments,
          G,
          E,
          impp->K,
          impp->F,
          impp->K - impp->F - 2 * impp->Zc,
          mod_order,
          nb_rb);
  }

  /* 第七步：执行比特交织 */
  start_meas(&nrLDPC_TB_encoding_parameters->segments[macro_segment].ts_interleave);

  /* 对第一组段进行交织（E值较小的段） */
  nr_interleaving_ldpc(E,                                       // 第一组段的比特数
                       mod_order,                               // 调制阶数（Qm）
                       e,                                       // 输入：速率匹配输出
                       f);                                      // 输出：第一组交织结果

  /* 如果存在E值差异，对第二组段进行交织（E值较大的段） */
  if (Eshift)
    nr_interleaving_ldpc(E2,                                    // 第二组段的比特数
                         mod_order,                             // 调制阶数（与第一组相同）
                         e,                                     // 输入：速率匹配输出（重用缓冲区）
                         f2);                                   // 输出：第二组交织结果

  stop_meas(&nrLDPC_TB_encoding_parameters->segments[macro_segment].ts_interleave);

  /* 4×4 MIMO内存清理：释放动态分配的速率匹配缓冲区 */
  if (Emax > 65536 && e != NULL) {  // 与分配条件保持一致
    free(e);
    e = NULL;
    LOG_D(PHY, "4×4 MIMO: Released heap-allocated rate matching buffer\n");
  }

  /* 标记当前任务完成 */
  completed_task_ans(impp->ans);
}

/* 函数：nrLDPC_launch_TB_encoding - 传输块级编码任务启动器
 * 作用：将一个传输块的多个代码段分组，创建并提交编码任务到线程池
 * 调用位置：由 nrLDPC_coding_encoder() 为每个传输块调用
 *
 * 参数详细说明：
 * @nrLDPC_slot_encoding_parameters: 时隙级编码参数
 *                                   来源：nrLDPC_coding_encoder() 的输入参数
 *                                   包含：所有TB的编码参数、线程池、性能统计等
 * @dlsch_id: 传输块索引（0到num_pdsch_slot-1）
 *            来源：nrLDPC_coding_encoder() 的循环变量
 *            作用：标识当前处理的是哪个传输块
 * @t_info: 线程任务信息结构
 *          来源：nrLDPC_coding_encoder() 中分配
 *          作用：管理任务队列和同步
 * @f: 第一组交织输出缓冲区数组
 *     来源：nrLDPC_coding_encoder() 中分配的二维数组
 *     格式：f[task_id] 指向每个任务的第一组输出缓冲区
 * @f2: 第二组交织输出缓冲区数组
 *      来源：nrLDPC_coding_encoder() 中分配的二维数组
 *      格式：f2[task_id] 指向每个任务的第二组输出缓冲区
 *
 * 返回值：创建的任务数量
 *
 * 核心功能：
 * 1. 设置公共编码参数（BG, Z, K, F等）
 * 2. 计算任务数量（每8个段一个任务）
 * 3. 为每个任务分配参数结构
 * 4. 提交任务到线程池执行
 */
static int nrLDPC_launch_TB_encoding(nrLDPC_slot_encoding_parameters_t *nrLDPC_slot_encoding_parameters,
                                      int dlsch_id,
                                      thread_info_tm_t *t_info,
                                      uint8_t **f,
                                      uint8_t **f2)
{
  /* 获取当前传输块的编码参数 */
  nrLDPC_TB_encoding_parameters_t *nrLDPC_TB_encoding_parameters = &nrLDPC_slot_encoding_parameters->TBs[dlsch_id];

  /* 设置所有任务共享的编码参数 */
  encoder_implemparams_t common_segment_params = {
    .n_segments = nrLDPC_TB_encoding_parameters->C,             // 代码段总数（来自分段结果）
    .tinput = nrLDPC_slot_encoding_parameters->tinput,          // 输入处理时间统计
    .tprep = nrLDPC_slot_encoding_parameters->tprep,            // 预处理时间统计
    .tparity = nrLDPC_slot_encoding_parameters->tparity,        // 校验位生成时间统计
    .toutput = nrLDPC_slot_encoding_parameters->toutput,        // 输出处理时间统计
    .Kb = nrLDPC_TB_encoding_parameters->Kb,                    // 基础信息块大小（来自分段）
    .Zc = nrLDPC_TB_encoding_parameters->Z,                     // 提升因子（来自分段）
    .BG = nrLDPC_TB_encoding_parameters->BG,                    // 基图类型（来自分段）
    .output = nrLDPC_TB_encoding_parameters->output,            // 最终输出缓冲区
    .K = nrLDPC_TB_encoding_parameters->K,                      // 代码块大小（来自分段）
    .F = nrLDPC_TB_encoding_parameters->F,                      // 填充比特数（来自分段）
  };

  /* 计算任务数量：每8个代码段一个任务，向上取整 */
  const size_t n_tasks = (common_segment_params.n_segments + 7) >> 3;

  /* 为每个任务创建参数结构并提交到线程池 */
  for (int j = 0; j < n_tasks; j++) {
    /* 从任务缓冲区分配参数结构 */
    ldpc8blocks_args_t *perJobImpp = &((ldpc8blocks_args_t *)t_info->buf)[t_info->len];
    DevAssert(t_info->len < t_info->cap);                       // 确保缓冲区容量足够
    t_info->len += 1;                                           // 更新已使用的缓冲区长度

    /* 设置任务特定的参数 */
    perJobImpp->impp = common_segment_params;                   // 复制公共参数
    perJobImpp->impp.first_seg = j * 8;                         // 设置起始段索引（0, 8, 16, ...）
    perJobImpp->impp.ans = t_info->ans;                         // 设置任务同步对象
    perJobImpp->nrLDPC_TB_encoding_parameters = nrLDPC_TB_encoding_parameters;  // 传输块参数
    perJobImpp->f = f[j];                                       // 第一组输出缓冲区
    perJobImpp->f2 = f2[j];                                     // 第二组输出缓冲区

    /* 创建任务并提交到线程池 */
    task_t t = {.func = ldpc8blocks, .args = perJobImpp};       // 任务结构：函数指针+参数
    pushTpool(nrLDPC_slot_encoding_parameters->threadPool, t);  // 提交到线程池执行
  }

  return n_tasks;                                               // 返回创建的任务数量
}

/* 函数：nrLDPC_coding_encoder - 时隙级LDPC编码器主入口
 * 作用：协调一个时隙内所有传输块的LDPC编码，是整个编码流程的顶层控制函数
 * 调用位置：由 nr_dlsch_coding.c 中的 nr_dlsch_encoding() 调用
 *
 * 参数详细说明：
 * @nrLDPC_slot_encoding_parameters: 时隙级编码参数结构
 *                                   来源：nr_dlsch_coding.c 中构造的slot_parameters
 *                                   包含：所有TB的编码参数、线程池、性能统计等
 *
 * 返回值：0表示成功，非0表示错误
 *
 * 核心流程：
 * 1. 预扫描：计算任务数量和最大E值
 * 2. 内存分配：为所有任务分配交织输出缓冲区
 * 3. 任务创建：为每个传输块创建编码任务
 * 4. 并行执行：通过线程池执行所有任务
 * 5. 结果整合：将各任务的输出整合成最终比特流
 *
 * 优化特点：
 * - 多TB并行：同时处理多个传输块
 * - 段组并行：每8个段一组并行处理
 * - 内存优化：预分配对齐的缓冲区
 * - 负载均衡：通过线程池实现动态负载均衡
 */
int nrLDPC_coding_encoder(nrLDPC_slot_encoding_parameters_t *nrLDPC_slot_encoding_parameters)
{
  int nbTasks = 0;                                              // 总任务数计数器

  /* 第一阶段：预扫描所有传输块，计算任务数量和最大E值 */
  uint32_t Emax = 0;                                            // 所有段中的最大E值
  for (int dlsch_id = 0; dlsch_id < nrLDPC_slot_encoding_parameters->nb_TBs; dlsch_id++) {
    /* 获取当前传输块的编码参数 */
    nrLDPC_TB_encoding_parameters_t *nrLDPC_TB_encoding_parameters = &nrLDPC_slot_encoding_parameters->TBs[dlsch_id];

    /* 计算当前TB需要的任务数：每8个段一个任务，向上取整 */
    size_t n_seg = (nrLDPC_TB_encoding_parameters->C / 8 + ((nrLDPC_TB_encoding_parameters->C & 7) == 0 ? 0 : 1));
    nbTasks += n_seg;                                           // 累加到总任务数

    /* 搜索当前TB中所有段的最大E值，用于分配缓冲区 */
    for (int seg_id = 0; seg_id < nrLDPC_TB_encoding_parameters->C; seg_id++) {
      uint32_t E = nrLDPC_TB_encoding_parameters->segments[seg_id].E;  // 当前段的E值
      Emax = E > Emax ? E : Emax;                               // 更新全局最大E值
    }
  }

  /* 第二阶段：分配交织输出缓冲区 - 4×4 MIMO优化 */
  // 将最大E值向上对齐到64字节边界，优化SIMD访问
  uint32_t Emax_ceil_mod = ceil_mod(Emax, 64);

  /* 4×4 MIMO缓冲区大小检查和调整 */
  if (Emax_ceil_mod > (1 << 20)) {  // 1MB限制检查
    LOG_W(PHY, "4×4 MIMO: Large buffer size Emax_ceil_mod=%u, may impact performance\n", Emax_ceil_mod);
  }

  /* 动态分配大缓冲区以避免栈溢出 */
  uint8_t (*f)[Emax_ceil_mod] = NULL;
  uint8_t (*f2)[Emax_ceil_mod] = NULL;

  if (Emax_ceil_mod * nbTasks > (1 << 18)) {  // 256KB阈值
    /* 使用堆分配避免栈溢出 */
    f = (uint8_t (*)[Emax_ceil_mod])aligned_alloc(64, nbTasks * Emax_ceil_mod);
    f2 = (uint8_t (*)[Emax_ceil_mod])aligned_alloc(64, nbTasks * Emax_ceil_mod);
    AssertFatal(f != NULL && f2 != NULL, "4×4 MIMO: Failed to allocate large buffers\n");
    LOG_D(PHY, "4×4 MIMO: Using heap allocation for large buffers (%u bytes each)\n",
          nbTasks * Emax_ceil_mod);
  } else {
    /* 使用栈分配（小缓冲区） */
    static uint8_t f_stack[32][65536] __attribute__((aligned(64)));   // 最大32个任务
    static uint8_t f2_stack[32][65536] __attribute__((aligned(64)));
    AssertFatal(nbTasks <= 32 && Emax_ceil_mod <= 65536,
                "Buffer size exceeded: nbTasks=%d, Emax_ceil_mod=%u\n", nbTasks, Emax_ceil_mod);
    f = (uint8_t (*)[Emax_ceil_mod])f_stack;
    f2 = (uint8_t (*)[Emax_ceil_mod])f2_stack;
  }

  /* 第三阶段：初始化任务管理结构 */
  ldpc8blocks_args_t arr[nbTasks];                              // 任务参数数组
  task_ans_t ans;                                               // 任务同步对象
  init_task_ans(&ans, nbTasks);                                 // 初始化同步对象，等待nbTasks个任务完成

  // 线程任务信息结构：管理任务队列和同步
  thread_info_tm_t t_info = {.buf = (uint8_t *)arr, .len = 0, .cap = nbTasks, .ans = &ans};

  /* 第四阶段：创建并提交所有编码任务 */
  int nbEncode = 0;                                             // 实际创建的任务数计数器
  nbTasks = 0;                                                  // 重置任务计数器，用作索引

  for (int dlsch_id = 0; dlsch_id < nrLDPC_slot_encoding_parameters->nb_TBs; dlsch_id++) {
    /* 获取当前传输块的参数 */
    nrLDPC_TB_encoding_parameters_t *nrLDPC_TB_encoding_parameters = &nrLDPC_slot_encoding_parameters->TBs[dlsch_id];
    size_t n_seg = (nrLDPC_TB_encoding_parameters->C / 8 + ((nrLDPC_TB_encoding_parameters->C & 7) == 0 ? 0 : 1));

    /* 为当前TB创建缓冲区指针数组，便于传递给任务函数
     * 将二维数组f[nbTasks+j][*]转换为一维指针数组f_2d[j] */
    uint8_t *f_2d[n_seg];                                       // 第一组缓冲区指针数组
    uint8_t *f2_2d[n_seg];                                      // 第二组缓冲区指针数组
    for (int j = 0; j < n_seg; j++) {
      f_2d[j] = &f[nbTasks + j][0];                             // 指向第(nbTasks+j)个任务的第一组缓冲区
      f2_2d[j] = &f2[nbTasks + j][0];                           // 指向第(nbTasks+j)个任务的第二组缓冲区
    }

    /* 启动当前传输块的编码任务 */
    nbEncode += nrLDPC_launch_TB_encoding(nrLDPC_slot_encoding_parameters, dlsch_id, &t_info, f_2d, f2_2d);

    nbTasks += n_seg;                                           // 更新任务索引
  }

  /* 如果某些任务没有被创建（例如段数为0），手动标记为完成 */
  if (nbEncode < nbTasks) {
    completed_many_task_ans(&ans, nbTasks - nbEncode);
  }

  /* 第五阶段：等待所有任务完成 */
  join_task_ans(&ans);                                          // 阻塞等待所有任务完成

  /* 第六阶段：整合所有任务的输出，生成最终比特流 */
  nbTasks = 0;                                                  // 重置任务计数器，用作索引

  for (int dlsch_id = 0; dlsch_id < nrLDPC_slot_encoding_parameters->nb_TBs; dlsch_id++) {
    /* 获取当前传输块的参数 */
    nrLDPC_TB_encoding_parameters_t *nrLDPC_TB_encoding_parameters = &nrLDPC_slot_encoding_parameters->TBs[dlsch_id];
    uint32_t C = nrLDPC_TB_encoding_parameters->C;              // 代码段总数
    size_t n_seg = (C / 8 + ((C & 7) == 0 ? 0 : 1));           // 任务数量

    time_stats_t *toutput = nrLDPC_slot_encoding_parameters->toutput;  // 输出处理时间统计

    /* 处理当前TB的每个任务的输出 */
    for (int j = 0; j < n_seg; j++) {
      /* 确定当前任务处理的段范围 */
      unsigned int macro_segment = j * 8;                       // 起始段索引
      unsigned int macro_segment_end = (C > macro_segment + 8) ? macro_segment + 8 : C;  // 结束段索引

      /* 分析当前任务组的E值分布（与ldpc8blocks中的逻辑相同） */
      const uint32_t E = nrLDPC_TB_encoding_parameters->segments[macro_segment].E;  // 第一个段的E值
      uint32_t E2 = E, E2_first_segment = macro_segment_end - macro_segment;        // 初始化第二组参数
      bool Eshift = false;                                      // E值差异标志
      uint32_t Emax = E;                                        // 最大E值

      /* 检测当前组是否存在不同的E值 */
      for (int s = macro_segment; s < macro_segment_end; s++) {
        if (nrLDPC_TB_encoding_parameters->segments[s].E != E) {
           E2 = nrLDPC_TB_encoding_parameters->segments[s].E;   // 发现不同的E值
           Eshift = true;                                       // 标记存在E值差异
           E2_first_segment = s-macro_segment;                  // 记录第二组的起始位置
           if(E2 > Emax)
             Emax = E2;                                         // 更新最大E值
           break;
        }
      }

      /* 开始输出处理时间统计 */
      if(toutput != NULL) start_meas(toutput);

      /* 计算当前任务组在最终输出中的比特偏移量 */
      uint32_t Eoffset=0;                                       // 累计偏移量
      for (int s=0; s<macro_segment; s++)
        Eoffset += (nrLDPC_TB_encoding_parameters->segments[s].E);  // 累加前面所有段的比特数

      /* 调用输出写入函数，将段间并行的交织输出转换为连续比特流 */
      write_task_output(&f[nbTasks + j][0],                     // 第一组交织输出
                        E,                                      // 第一组段的比特数
                        &f2[nbTasks + j][0],                    // 第二组交织输出
                        E2,                                     // 第二组段的比特数
                        Eshift,                                 // E值差异标志
                        E2_first_segment,                       // 第二组起始段索引
                        macro_segment_end - macro_segment,      // 当前组的段数
                        nrLDPC_TB_encoding_parameters->output,  // 最终输出缓冲区
                        Eoffset);                               // 当前组的比特偏移

      /* 结束输出处理时间统计 */
      if(toutput != NULL) stop_meas(toutput);
    }
    nbTasks += n_seg;                                           // 更新任务索引
  }

  /* 4×4 MIMO内存清理：释放动态分配的缓冲区 */
  if (Emax_ceil_mod * nbTasks > (1 << 18)) {  // 与分配条件保持一致
    if (f != NULL) {
      free(f);
      f = NULL;
    }
    if (f2 != NULL) {
      free(f2);
      f2 = NULL;
    }
    LOG_D(PHY, "4×4 MIMO: Released heap-allocated buffers\n");
  }

  return 0;                                                     // 成功完成所有编码
}
}
