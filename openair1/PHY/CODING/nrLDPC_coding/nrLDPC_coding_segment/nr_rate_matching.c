/*
 * Licensed to the OpenAirInterface (OAI) Software Alliance under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The OpenAirInterface Software Alliance licenses this file to You under
 * the OAI Public License, Version 1.1  (the "License"); you may not use this file
 * except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.openairinterface.org/?page_id=698
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *-------------------------------------------------------------------------------
 * For more information about the OpenAirInterface (OAI) Software Alliance:
 *      <EMAIL>
 */

#include "PHY/sse_intrin.h"
#include "nr_rate_matching.h"
#include "common/utils/LOG/log.h"
/* 中文说明：本文件实现 NR LDPC 的“速率匹配/反速率匹配”和“交织/反交织”。
 * - 编码端：d（编码后）→ 速率匹配 e（按 RV/填充/重复等）→ 交织 f（按 Qm）
 * - 解码端：f → 反交织 e → 反速率匹配累加到 d（支持 HARQ 首轮清零/后续累加）
 * 参照 3GPP TS 38.212（5.4.2 比特选择、5.4.1 填充、5.4.3 比特交织）。
 *
 * 调用关系总览：
 * 编码端调用链：
 *   nr_dlsch_encoding() [nr_dlsch_coding.c] openair1/PHY/NR_TRANSPORT/nr_dlsch_coding.c，是 NR 下行链路共享信道（DLSCH）编码的主要实现文件
 *   → nrLDPC_coding_encoder() [segment/xdma/t2实现]
 *   → nr_rate_matching_ldpc() + nr_interleaving_ldpc() [本文件]
 *
 * 解码端调用链：
 *   nr_dlsch_decoding() [nr_dlsch_decoding.c]
 *   → nrLDPC_coding_decoder() [segment/xdma/t2实现]
 *   → nr_deinterleaving_ldpc() + nr_rate_matching_ldpc_rx() [本文件]
 *
 * 数据流向：
 *   TB分段 → CRC → LDPC编码 → 速率匹配 → 交织 → 调制 → 发射
 *   接收 → 解调 → 反交织 → 反速率匹配(HARQ) → LDPC解码 → CRC → TB重组
 */

// #define RM_DEBUG 1

/* 中文：index_k0[BG-1][rv] 提供 38.212 中的 k0（按 BG 与 RV 确定的起始索引），
 * 在本实现中按 Ncb/N 比例缩放并乘以 Z 得到 bit 选择的起始 ind。*/

static const uint8_t index_k0[2][4] = {{0, 17, 33, 56}, {0, 13, 25, 43}};
/*用来存储 NR LDPC 速率匹配中的 k0 起始索引表
第一个维度 [2] 对应 BG（Base Graph）：
index_k0[0][...] → BG=1 的 k0 值
index_k0[1][...] → BG=2 的 k0 值
第二个维度 [4] 对应 RV（Redundancy Version）：
index_k0[...][0] → RV=0 的 k0 值
index_k0[...][1] → RV=1 的 k0 值
index_k0[...][2] → RV=2 的 k0 值
index_k0[...][3] → RV=3 的 k0 值*/

/* 中文：nr_interleaving_ldpc —— 将速率匹配后的 e 按调制阶数 Qm 进行比特交织，输出 f。
 * 思路：把 e 分成 Qm 个等长子序列 e0..e(Qm-1)，结果 f 以 [e0(i), e1(i), ...] 方式交织拼接。
 * 针对 Qm=2/4/6/8 分别用 AVX512/128bit/SIMD 优化批量处理，尾部用标量补齐。*/

/* 函数：nr_interleaving_ldpc - NR LDPC 比特交织（编码端）
 * 作用：将速率匹配后的比特序列 e 按调制阶数 Qm 进行交织，输出交织后的序列 f
 * 数据流向：速率匹配 e[E] → 交织 → f[E] → 调制映射
 * 调用位置：在 DLSCH/ULSCH 编码链路中，速率匹配之后、调制之前
 *
 * 参数详细说明：
 * @E: 速率匹配后的比特数（必须能被 Qm 整除）
 *     - 来源：由 nr_get_E(G, C, Qm, Nl, r) 计算得出
 *     - G 是总可用比特数，C 是代码块数，r 是当前代码块索引
 *     - 约束：E % Qm == 0，否则无法均匀分组
 *
 * @Qm: 调制阶数（每个调制符号承载的比特数）
 *     - 来源：从 DCI 中的 MCS 索引通过 MCS 表查找得到
 *     - 调用：nr_get_Qm_dl(mcs_index, mcs_table) 或 nr_get_Qm_ul(mcs_index, mcs_table)
 *     - 取值：QPSK=2, 16QAM=4, 64QAM=6, 256QAM=8
 *
 * @e: 输入 - 速率匹配后的比特序列，长度 E
 *     - 来源：nr_rate_matching_ldpc() 函数的输出
 *     - 内容：从 LDPC 编码输出中按 RV 和填充规则选择的比特
 *     - 格式：每个元素是 0 或 1 的比特值
 *
 * @f: 输出 - 交织后的比特序列，长度 E
 *     - 去向：传递给调制器进行符号映射，或多层传输中的层映射
 *     - 格式：按调制符号重新排列的比特序列
 *
 * 交织原理与作用：
 * 1. 分组：将 e 分成 Qm 个等长子序列，每个长度 H = E/Qm
 *    e0 = [e[0], e[H], e[2H], ...]     // 第0个子序列
 *    e1 = [e[1], e[H+1], e[2H+1], ...] // 第1个子序列
 *    ...
 *    e(Qm-1) = [e[Qm-1], e[H+Qm-1], ...] // 第(Qm-1)个子序列
 *
 * 2. 交织：输出 f 按调制符号组织
 *    f = [e0[0], e1[0], e2[0], ..., e(Qm-1)[0],  // 第1个调制符号的比特
 *         e0[1], e1[1], e2[1], ..., e(Qm-1)[1],  // 第2个调制符号的比特
 *         ...]
 *
 * 3. 目的：
 *    - 抗突发错误：将连续比特分散到不同调制符号中
 *    - 提高分集增益：相邻比特经历不同信道衰落
 *    - 优化解调性能：使 LDPC 解码器输入更加随机化
 */
void nr_interleaving_ldpc(uint32_t E, uint8_t Qm, uint8_t *e, uint8_t *f)
{
  /* 计算每个子序列的长度 H = E/Qm
   * 例如：E=4998, Qm=6 → H=833，将 e 分成 6 个长度为 833 的子序列 */
  const uint32_t EQm = E / Qm;  // 每个子序列的长度

  /* 初始化输出缓冲区为 0 */
  memset(f, 0, E * sizeof(uint8_t));
  /* 根据调制阶数 Qm 选择不同的交织实现
   * 不同 Qm 值对应不同的 SIMD 优化策略 */
  switch(Qm) {
    case 2: {
      /* QPSK 调制（Qm=2）：将 e 分成 2 个子序列 e0, e1
       * 输出：f = [e0[0], e1[0], e0[1], e1[1], e0[2], e1[2], ...] */
      uint8_t *e0 = e;        // 第0个子序列起始位置
      uint8_t *e1 = e0 + EQm; // 第1个子序列起始位置
      int i = 0;              // 循环计数器
      /* AVX512 SIMD 优化：一次处理 64 个比特
       * 使用 512 位向量指令加速交织操作 */
#ifdef __AVX512VBMI__
      // clang-format off
      /* 预定义的置换模式：定义如何将 e0 和 e1 的比特交织
       * p8a 和 p8b 分别处理前 32 和后 32 个比特的交织模式 */
      const __m512i p8a = _mm512_set_epi8(95, 31, 94, 30, 93, 29, 92, 28, 91, 27, 90, 26, 89, 25, 88, 24,
                                          87, 23, 86, 22, 85, 21, 84, 20, 83, 19, 82, 18, 81, 17, 80, 16,
                                          79, 15, 78, 14, 77, 13, 76, 12, 75, 11, 74, 10, 73, 9, 72, 8,
                                          71, 7, 70, 6, 69, 5, 68, 4, 67, 3, 66, 2, 65, 1, 64, 0);
      const __m512i p8b = _mm512_set_epi8(127, 63, 126, 62, 125, 61, 124, 60, 123, 59, 122, 58, 121, 57, 120,
                                          56, 119, 55, 118, 54, 117, 53, 116, 52, 115, 51, 114, 50, 113, 49, 112,
                                          48, 111, 47, 110, 46, 109, 45, 108, 44, 107, 43, 106, 42, 105, 41, 104,
                                          40, 103, 39, 102, 38, 101, 37, 100, 36, 99, 35, 98, 34, 97, 33, 96, 32);
      // clang-format on

      /* 将指针转换为 512 位向量指针 */
      __m512i *f_512 = (__m512i *)f;
      __m512i *e0_512 = (__m512i *)e0;
      __m512i *e1_512 = (__m512i *)e1;

      /* 批量处理：每次处理 64 个比特（512位 / 8位 = 64） */
      for (; i < (EQm & ~63); i += 64) {
        __m512i e0j = _mm512_loadu_si512(e0_512++);  // 加载 e0 的 64 个比特
        __m512i e1j = _mm512_loadu_si512(e1_512++);  // 加载 e1 的 64 个比特

        /* 使用置换指令进行交织：e0(i) e1(i) e0(i+1) e1(i+1) ... */
        _mm512_storeu_si512(f_512++, _mm512_permutex2var_epi8(e0j, p8a, e1j));
        _mm512_storeu_si512(f_512++, _mm512_permutex2var_epi8(e0j, p8b, e1j));
      }

      /* 更新指针到未处理的位置 */
      e0 = (uint8_t *)e0_512;
      e1 = (uint8_t *)e1_512;
      f = (uint8_t *)f_512;
#endif
      /* 128 位 SIMD 优化：一次处理 16 个比特
       * 当不支持 AVX512 时的备选优化方案 */
#ifdef USE128BIT
      simde__m128i *f_128 = (simde__m128i *)f;
      simde__m128i *e0_128 = (simde__m128i *)e0;
      simde__m128i *e1_128 = (simde__m128i *)e1;

      /* 批量处理：每次处理 16 个比特（128位 / 8位 = 16） */
      for (; i < (EQm & ~15); i += 16) {
        simde__m128i e0j = simde_mm_loadu_si128(e0_128++);  // 加载 e0 的 16 个比特
        simde__m128i e1j = simde_mm_loadu_si128(e1_128++);  // 加载 e1 的 16 个比特

        /* 使用 unpack 指令进行交织 */
        simde_mm_storeu_si128(f_128++, simde_mm_unpacklo_epi8(e0j, e1j));  // 低 8 字节交织
        simde_mm_storeu_si128(f_128++, simde_mm_unpackhi_epi8(e0j, e1j));  // 高 8 字节交织
      }

      /* 更新指针到未处理的位置 */
      e0 = (uint8_t *)e0_128;
      e1 = (uint8_t *)e1_128;
      f = (uint8_t *)f_128;
#endif

      /* 标量处理：处理剩余的比特（SIMD 无法处理的尾部）
       * 逐个调制符号进行交织：每次取 e0 和 e1 各一个比特 */
      for (; i < EQm; i++) {
        *f++ = *e0++;  // 第i个调制符号的第0个比特
        *f++ = *e1++;  // 第i个调制符号的第1个比特
      }
    } break;
    case 4: {
      /* 16QAM 调制（Qm=4）：将 e 分成 4 个子序列 e0, e1, e2, e3
       * 输出：f = [e0[0], e1[0], e2[0], e3[0], e0[1], e1[1], e2[1], e3[1], ...] */
      uint8_t *e0 = e;        // 第0个子序列起始位置
      uint8_t *e1 = e0 + EQm; // 第1个子序列起始位置
      uint8_t *e2 = e1 + EQm; // 第2个子序列起始位置
      uint8_t *e3 = e2 + EQm; // 第3个子序列起始位置
      int i = 0;              // 循环计数器
#ifdef __AVX512VBMI__
      // clang-format off
      const __m512i p8a = _mm512_set_epi8(95, 31, 94, 30, 93, 29, 92, 28, 91, 27, 90, 26, 89, 25, 88, 24, 87, 23, 86,
                                          22, 85, 21, 84, 20, 83, 19, 82, 18, 81, 17, 80, 16, 79, 15, 78, 14, 77, 13,
                                          76, 12, 75, 11, 74, 10, 73, 9, 72, 8, 71, 7, 70, 6, 69, 5, 68, 4, 67, 3, 66, 2, 65, 1, 64, 0);
      const __m512i p8b = _mm512_set_epi8(127, 63, 126, 62, 125, 61, 124, 60, 123, 59, 122, 58, 121, 57, 120, 56, 119,
                                          55, 118, 54, 117, 53, 116, 52, 115, 51, 114, 50, 113, 49, 112, 48, 111, 47,
                                          110, 46, 109, 45, 108, 44, 107, 43, 106, 42, 105, 41, 104, 40, 103, 39, 102,
                                          38, 101, 37, 100, 36, 99, 35, 98, 34, 97, 33, 96, 32);
      const __m512i p16a = _mm512_set_epi16(47, 15, 46, 14, 45, 13, 44, 12, 43, 11, 42, 10, 41, 9, 40, 8, 39, 7, 38, 6,
                                            37, 5, 36, 4, 35, 3, 34, 2, 33, 1, 32, 0);
      const __m512i p16b = _mm512_set_epi16(63, 31, 62, 30, 61, 29, 60, 28, 59, 27, 58, 26, 57, 25, 56, 24, 55, 23, 54,
                                            22, 53, 21, 52, 20, 51, 19, 50, 18, 49, 17, 48, 16);
      // clang-format on
      __m512i *e0_512 = (__m512i *)e0;
      __m512i *e1_512 = (__m512i *)e1;
      __m512i *e2_512 = (__m512i *)e2;
      __m512i *e3_512 = (__m512i *)e3;
      __m512i *f_512 = (__m512i *)f;
      for (; i < (EQm & ~63); i += 64) {
        __m512i e0j = _mm512_loadu_si512(e0_512++);
        __m512i e1j = _mm512_loadu_si512(e1_512++);
        __m512i e2j = _mm512_loadu_si512(e2_512++);
        __m512i e3j = _mm512_loadu_si512(e3_512++);
        __m512i tmp0 = _mm512_permutex2var_epi8(e0j, p8a, e1j); // e0(i) e1(i) e0(i+1) e1(i+1) .... e0(i+15) e1(i+15)
        __m512i tmp1 = _mm512_permutex2var_epi8(e2j, p8a, e3j); // e2(i) e3(i) e2(i+1) e3(i+1) .... e2(i+15) e3(i+15)
        // e0(i) e1(i) e2(i) e3(i) ... e0(i+7) e1(i+7) e2(i+7) e3(i+7)
        _mm512_storeu_si512(f_512++, _mm512_permutex2var_epi16(tmp0, p16a, tmp1));
        _mm512_storeu_si512(f_512++, _mm512_permutex2var_epi16(tmp0, p16b, tmp1));
        tmp0 = _mm512_permutex2var_epi8(e0j, p8b, e1j); // e0(i) e1(i) e0(i+1) e1(i+1) .... e0(i+15) e1(i+15)
        tmp1 = _mm512_permutex2var_epi8(e2j, p8b, e3j); // e2(i) e3(i) e2(i+1) e3(i+1) .... e2(i+15) e3(i+15)
        // e0(i) e1(i) e2(i) e3(i) ... e0(i+7) e1(i+7) e2(i+7) e3(i+7)
        _mm512_storeu_si512(f_512++, _mm512_permutex2var_epi16(tmp0, p16a, tmp1));
        _mm512_storeu_si512(f_512++, _mm512_permutex2var_epi16(tmp0, p16b, tmp1));
      }
      e0 = (uint8_t *)e0_512;
      e1 = (uint8_t *)e1_512;
      e2 = (uint8_t *)e2_512;
      e3 = (uint8_t *)e3_512;
      f = (uint8_t *)f_512;
#endif
#ifdef USE128BIT
      simde__m128i *e0_128 = (simde__m128i *)e0;
      simde__m128i *e1_128 = (simde__m128i *)e1;
      simde__m128i *e2_128 = (simde__m128i *)e2;
      simde__m128i *e3_128 = (simde__m128i *)e3;
      simde__m128i *f_128 = (simde__m128i *)f;
      for (; i < (EQm & ~15); i += 16) {
        simde__m128i e0j = simde_mm_loadu_si128(e0_128++);
        simde__m128i e1j = simde_mm_loadu_si128(e1_128++);
        simde__m128i e2j = simde_mm_loadu_si128(e2_128++);
        simde__m128i e3j = simde_mm_loadu_si128(e3_128++);
        simde__m128i tmp0 = simde_mm_unpacklo_epi8(e0j, e1j); // e0(i) e1(i) e0(i+1) e1(i+1) .... e0(i+7) e1(i+7)
        simde__m128i tmp1 = simde_mm_unpacklo_epi8(e2j, e3j); // e2(i) e3(i) e2(i+1) e3(i+1) .... e2(i+7) e3(i+7)
        simde_mm_storeu_si128(f_128++,
                              simde_mm_unpacklo_epi16(tmp0, tmp1)); // e0(i) e1(i) e2(i) e3(i) ... e0(i+3) e1(i+3) e2(i+3) e3(i+3)
        simde_mm_storeu_si128(
            f_128++,
            simde_mm_unpackhi_epi16(tmp0, tmp1)); // e0(i+4) e1(i+4) e2(i+4) e3(i+4) ... e0(i+7) e1(i+7) e2(i+7) e3(i+7)
        tmp0 = simde_mm_unpackhi_epi8(e0j, e1j); // e0(i+8) e1(i+8) e0(i+9) e1(i+9) .... e0(i+15) e1(i+15)
        tmp1 = simde_mm_unpackhi_epi8(e2j, e3j); // e2(i+8) e3(i+9) e2(i+10) e3(i+10) .... e2(i+31) e3(i+31)
        simde_mm_storeu_si128(f_128++, simde_mm_unpacklo_epi16(tmp0, tmp1));
        simde_mm_storeu_si128(f_128++, simde_mm_unpackhi_epi16(tmp0, tmp1));
      }
      e0 = (uint8_t *)e0_128;
      e1 = (uint8_t *)e1_128;
      e2 = (uint8_t *)e2_128;
      e3 = (uint8_t *)e3_128;
      f = (uint8_t *)f_128;
#endif
      /* 标量处理：处理剩余的比特（SIMD 无法处理的尾部）
       * 逐个调制符号进行交织：每次取 e0, e1, e2, e3 各一个比特 */
      for (; i < EQm; i++) {
        *f++ = *e0++;  // 第i个调制符号的第0个比特
        *f++ = *e1++;  // 第i个调制符号的第1个比特
        *f++ = *e2++;  // 第i个调制符号的第2个比特
        *f++ = *e3++;  // 第i个调制符号的第3个比特
      }
    } break;
    case 6: {
      /* 64QAM 调制（Qm=6）：将 e 分成 6 个子序列 e0, e1, e2, e3, e4, e5
       * 输出：f = [e0[0], e1[0], e2[0], e3[0], e4[0], e5[0], e0[1], e1[1], e2[1], e3[1], e4[1], e5[1], ...] */
      uint8_t *e0 = e;        // 第0个子序列起始位置
      uint8_t *e1 = e0 + EQm; // 第1个子序列起始位置
      uint8_t *e2 = e1 + EQm; // 第2个子序列起始位置
      uint8_t *e3 = e2 + EQm; // 第3个子序列起始位置
      uint8_t *e4 = e3 + EQm; // 第4个子序列起始位置
      uint8_t *e5 = e4 + EQm; // 第5个子序列起始位置
      int i = 0;              // 循环计数器
#ifdef __AVX512VBMI__
      // clang-format off
      const __m512i p8a = _mm512_set_epi8(95, 31, 94, 30, 93, 29, 92, 28, 91, 27, 90, 26, 89, 25, 88, 24, 87, 23,
                                          86, 22, 85, 21, 84, 20, 83, 19, 82, 18, 81, 17, 80, 16, 79, 15, 78, 14,
                                          77, 13, 76, 12, 75, 11, 74, 10, 73, 9, 72, 8, 71, 7, 70, 6, 69, 5, 68, 4, 67, 3, 66, 2, 65, 1, 64, 0);
      const __m512i p8b = _mm512_set_epi8(127, 63, 126, 62, 125, 61, 124, 60, 123, 59, 122, 58, 121, 57, 120, 56, 119,
                                          55, 118, 54, 117, 53, 116, 52, 115, 51, 114, 50, 113, 49, 112, 48, 111, 47, 110,
                                          46, 109, 45, 108, 44, 107, 43, 106, 42, 105, 41, 104, 40, 103, 39, 102, 38, 101, 37, 100, 36, 99, 35, 98, 34, 97, 33, 96, 32);
      const __m512i p16a = _mm512_set_epi16(47, 15, 46, 14, 45, 13, 44, 12, 43, 11, 42, 10, 41, 9, 40, 8, 39, 7, 38, 6, 37, 5, 36, 4, 35, 3, 34, 2, 33, 1, 32, 0);
      const __m512i p16b = _mm512_set_epi16(63, 31, 62, 30, 61, 29, 60, 28, 59, 27, 58, 26, 57, 25, 56, 24, 55, 23, 54, 22, 53, 21, 52, 20, 51, 19, 50, 18, 49, 17, 48, 16);
      const __m512i p16c = _mm512_set_epi16(21, 20, 41, 19, 18, 40, 17, 16, 39, 15, 14, 38, 13, 12, 37, 11, 10, 36, 9, 8, 35, 7, 6, 34, 5, 4, 33, 3, 2, 32, 1, 0);
      const __m512i p16d = _mm512_set_epi16(10, 52, 9, 8, 51, 7, 6, 50, 5, 4, 49, 3, 2, 48, 1, 0, 47, 31, 30, 46, 29, 28, 45, 27, 26, 44, 25, 24, 43, 23, 22, 42);
      const __m512i p16d2 = _mm512_set_epi16(32 + 10, 30, 32 + 9, 32 + 8, 27, 32 + 7, 32 + 6, 24, 32 + 5, 32 + 4, 21, 32 + 3, 32 + 2, 18, 32 + 1, 32 + 0,
                                             15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0);
      const __m512i p16e = _mm512_set_epi16(63, 31, 30, 62, 29, 28, 61, 27, 26, 60, 25, 24, 59, 23, 22, 58, 21, 20, 57, 19, 18, 56, 17, 16, 55, 15, 14, 54, 13, 12, 53, 11);
      // clang-format on
      __m512i *e0_512 = (__m512i *)e0;
      __m512i *e1_512 = (__m512i *)e1;
      __m512i *e2_512 = (__m512i *)e2;
      __m512i *e3_512 = (__m512i *)e3;
      __m512i *e4_512 = (__m512i *)e4;
      __m512i *e5_512 = (__m512i *)e5;
      __m512i *f_512 = (__m512i *)f;
      for (; i < (EQm & ~63); i += 64) {
        __m512i e0j = _mm512_loadu_si512(e0_512++);
        __m512i e1j = _mm512_loadu_si512(e1_512++);
        __m512i e2j = _mm512_loadu_si512(e2_512++);
        __m512i e3j = _mm512_loadu_si512(e3_512++);
        __m512i e4j = _mm512_loadu_si512(e4_512++);
        __m512i e5j = _mm512_loadu_si512(e5_512++);
        __m512i tmp0 = _mm512_permutex2var_epi8(e0j, p8a, e1j); // e0(i) e1(i) e0(i+1) e1(i+1) .... e0(i+31) e1(i+31)
        __m512i tmp1 = _mm512_permutex2var_epi8(e2j, p8a, e3j); // e2(i) e3(i) e2(i+1) e3(i+1) .... e2(i+31) e3(i+31)
        __m512i tmp2 = _mm512_permutex2var_epi8(e4j, p8a, e5j); // e4(i) e5(i) e4(i+1) e5(i+1) .... e4(i+31) e5(i+31)
        // e0(i) e1(i) e2(i) e3(i) ... e0(i+15) e1(i+15) e2(i+15) e3(i+15)
        __m512i tmp3 = _mm512_permutex2var_epi16(tmp0, p16a, tmp1);
        // e0(i+16) e1(i+16) e2(i+16) e3(i+16) ... e0(i+31) e1(i+31) e2(i+31) e3(i+31)
        __m512i tmp4 = _mm512_permutex2var_epi16(tmp0, p16b, tmp1);
        // e0(i) e1(i) e2(i) e3(i) e4(i) e5(i) ... e0(i+9) e1(i+9) e2(i+9) e3(i+9) e4(i+9) e5(i+9) e0(i+10) e1(i+10)
        _mm512_storeu_si512(f_512++, _mm512_permutex2var_epi16(tmp3, p16c, tmp2));
        // e2(i+10) e3(i+10) e4(i+10) e5(i+10) ... e0(i+15) e1(i+15) e2(i+15) e3(i+15) e4(i+15)
        // e5(i+15) x x x x e4(i+16) e5(i+16) x x x x .... e4(i+20) e5(i+20) x x
        __m512i tmp5 = _mm512_permutex2var_epi16(tmp3, p16d, tmp2);
        // e2(i+10) e3(i+10) e4(i+10) e5(i+10) ... e0(i+15) e1(i+15) e2(i+15) e3(i+15)
        // e4(i+15) e5(i+15) e0(i+16) e1(i+16) e2(i+16) e3(i+16) e4(i+16) e5(i+16)  e0(i+20)
        // e1(i+20) e2(i+20) e3(i+20) e4(i+20) e5(i+20) e0(i+21) e1(i+21)
        _mm512_storeu_si512(f_512++, _mm512_permutex2var_epi16(tmp5, p16d2, tmp4));
        // e2(i+21) e3(i+21) e4(i+21) e5(i+21) .... e0(i+31) e1(i+31) e2(i+31) e3(i+31) e4(i+31) e5(i+31)
        _mm512_storeu_si512(f_512++, _mm512_permutex2var_epi16(tmp4, p16e, tmp2));

        tmp0 = _mm512_permutex2var_epi8(e0j, p8b, e1j); // e0(i+32) e1(i+32) e0(i+32) e1(i+32) .... e0(i+63) e1(i+63)
        tmp1 = _mm512_permutex2var_epi8(e2j, p8b, e3j); // e2(i+32) e3(i+32) e2(i+32) e3(i+32) .... e2(i+63) e3(i+63)
        tmp2 = _mm512_permutex2var_epi8(e4j, p8b, e5j); // e4(i+32) e5(i+32) e4(i+32) e5(i+32) .... e4(i+63) e5(i+63)
        tmp3 = _mm512_permutex2var_epi16(tmp0, p16a, tmp1); // e0(i) e1(i) e2(i) e3(i) ... e0(i+15) e1(i+15) e2(i+15) e3(i+15)
        // e0(i+16) e1(i+16) e2(i+16) e3(i+16) ... e0(i+31) e1(i+31) e2(i+31) e3(i+31)
        tmp4 = _mm512_permutex2var_epi16(tmp0, p16b, tmp1);
        // e0(i) e1(i) e2(i) e3(i) e4(i) e5(i) ... e0(i+9) e1(i+9) e2(i+9) e3(i+9) e4(i+9) e5(i+9) e0(i+10) e1(i+10)
        _mm512_storeu_si512(f_512++, _mm512_permutex2var_epi16(tmp3, p16c, tmp2));
        // e2(i+10) e3(i+10) e4(i+10) e5(i+10) ... e0(i+15) e1(i+15) e2(i+15) e3(i+15)
        // e4(i+15) e5(i+15) x x x x e4(i+16) e5(i+16) x x x x .... e4(i+20) e5(i+20) x x
        tmp5 = _mm512_permutex2var_epi16(tmp3, p16d, tmp2);
        // e2(i+10) e3(i+10) e4(i+10) e5(i+10) ... e0(i+15) e1(i+15) e2(i+15) e3(i+15)
        // e4(i+15) e5(i+15) e0(i+16) e1(i+16) e2(i+16) e3(i+16) e4(i+16) e5(i+16)  e0(i+20)
        // e1(i+20) e2(i+20) e3(i+20) e4(i+20) e5(i+20) e0(i+21) e1(i+21)
        _mm512_storeu_si512(f_512++, _mm512_permutex2var_epi16(tmp5, p16d2, tmp4));
        // e2(i+21) e3(i+21) e4(i+21) e5(i+21) .... e0(i+31) e1(i+31) e2(i+31) e3(i+31) e4(i+31) e5(i+31)
        _mm512_storeu_si512(f_512++, _mm512_permutex2var_epi16(tmp4, p16e, tmp2));
      }
      e0 = (uint8_t *)e0_512;
      e1 = (uint8_t *)e1_512;
      e2 = (uint8_t *)e2_512;
      e3 = (uint8_t *)e3_512;
      e4 = (uint8_t *)e4_512;
      e5 = (uint8_t *)e5_512;
      f = (uint8_t *)f_512;
#endif
      /* 标量处理：处理剩余的比特（SIMD 无法处理的尾部）
       * 逐个调制符号进行交织：每次取 e0, e1, e2, e3, e4, e5 各一个比特 */
      for (; i < EQm; i++) {
        *f++ = *e0++;  // 第i个调制符号的第0个比特
        *f++ = *e1++;  // 第i个调制符号的第1个比特
        *f++ = *e2++;  // 第i个调制符号的第2个比特
        *f++ = *e3++;  // 第i个调制符号的第3个比特
        *f++ = *e4++;  // 第i个调制符号的第4个比特
        *f++ = *e5++;  // 第i个调制符号的第5个比特
      }
    } break;
    case 8: {
      /* 256QAM 调制（Qm=8）：将 e 分成 8 个子序列 e0, e1, e2, e3, e4, e5, e6, e7
       * 输出：f = [e0[0], e1[0], e2[0], e3[0], e4[0], e5[0], e6[0], e7[0],
       *            e0[1], e1[1], e2[1], e3[1], e4[1], e5[1], e6[1], e7[1], ...] */
      uint8_t *e0 = e;        // 第0个子序列起始位置
      uint8_t *e1 = e0 + EQm; // 第1个子序列起始位置
      uint8_t *e2 = e1 + EQm; // 第2个子序列起始位置
      uint8_t *e3 = e2 + EQm; // 第3个子序列起始位置
      uint8_t *e4 = e3 + EQm; // 第4个子序列起始位置
      uint8_t *e5 = e4 + EQm; // 第5个子序列起始位置
      uint8_t *e6 = e5 + EQm; // 第6个子序列起始位置
      uint8_t *e7 = e6 + EQm; // 第7个子序列起始位置

      int i = 0;
#ifdef __AVX512VBMI__
      // clang-format off
      const __m512i p8a = _mm512_set_epi8(95, 31, 94, 30, 93, 29, 92, 28, 91, 27, 90, 26, 89, 25, 88, 24, 87, 23,
                                          86, 22, 85, 21, 84, 20, 83, 19, 82, 18, 81, 17, 80, 16, 79, 15, 78, 14, 77, 13,
                                          76, 12, 75, 11, 74, 10, 73, 9, 72, 8, 71, 7, 70, 6, 69, 5, 68, 4, 67, 3, 66, 2, 65, 1, 64, 0);
      const __m512i p8b = _mm512_set_epi8(127, 63, 126, 62, 125, 61, 124, 60, 123, 59, 122, 58, 121, 57, 120, 56, 119, 55,
                                          118, 54, 117, 53, 116, 52, 115, 51, 114, 50, 113, 49, 112, 48, 111, 47, 110, 46,
                                          109, 45, 108, 44, 107, 43, 106, 42, 105, 41, 104, 40, 103, 39, 102, 38, 101, 37, 100, 36, 99, 35, 98, 34, 97, 33, 96, 32);

      const __m512i p16a = _mm512_set_epi16(47, 15, 46, 14, 45, 13, 44, 12, 43, 11, 42, 10, 41, 9, 40, 8, 39, 7, 38, 6, 37, 5, 36, 4, 35, 3, 34, 2, 33, 1, 32, 0);
      const __m512i p16b = _mm512_set_epi16(63, 31, 62, 30, 61, 29, 60, 28, 59, 27, 58, 26, 57, 25, 56, 24, 55, 23, 54, 22, 53, 21, 52, 20, 51, 19, 50, 18, 49, 17, 48, 16);

      const __m512i p32a = _mm512_set_epi32(23, 7, 22, 6, 21, 5, 20, 4, 19, 3, 18, 2, 17, 1, 16, 0);
      const __m512i p32b = _mm512_set_epi32(31, 15, 30, 14, 29, 13, 28, 12, 27, 11, 26, 10, 25, 9, 24, 8);
      // clang-format on
      __m512i *e0_512 = (__m512i *)e0;
      __m512i *e1_512 = (__m512i *)e1;
      __m512i *e2_512 = (__m512i *)e2;
      __m512i *e3_512 = (__m512i *)e3;
      __m512i *e4_512 = (__m512i *)e4;
      __m512i *e5_512 = (__m512i *)e5;
      __m512i *e6_512 = (__m512i *)e6;
      __m512i *e7_512 = (__m512i *)e7;
      __m512i *f_512 = (__m512i *)f;
      for (; i < (EQm & ~63); i += 64) {
        __m512i e0j = _mm512_loadu_si512(e0_512++);
        __m512i e1j = _mm512_loadu_si512(e1_512++);
        __m512i e2j = _mm512_loadu_si512(e2_512++);
        __m512i e3j = _mm512_loadu_si512(e3_512++);
        __m512i e4j = _mm512_loadu_si512(e4_512++);
        __m512i e5j = _mm512_loadu_si512(e5_512++);
        __m512i e6j = _mm512_loadu_si512(e6_512++);
        __m512i e7j = _mm512_loadu_si512(e7_512++);
        __m512i tmp0 = _mm512_permutex2var_epi8(e0j, p8a, e1j); // e0(i) e1(i) e0(i+1) e1(i+1) .... e0(i+15) e1(i+15)
        __m512i tmp1 = _mm512_permutex2var_epi8(e2j, p8a, e3j); // e2(i) e3(i) e2(i+1) e3(i+1) .... e2(i+15) e3(i+15)
        __m512i tmp2 = _mm512_permutex2var_epi8(e4j, p8a, e5j); // e4(i) e5(i) e4(i+1) e5(i+1) .... e4(i+15) e5(i+15)
        __m512i tmp3 = _mm512_permutex2var_epi8(e6j, p8a, e7j); // e6(i) e7(i) e6(i+1) e7(i+1) .... e6(i+15) e7(i+15)
        __m512i tmp4 = _mm512_permutex2var_epi16(tmp0, p16a, tmp1); // e0(i) e1(i) e2(i) e3(i) ... e0(i+7) e1(i+7) e2(i+7) e3(i+7)
        // e4(i) e5(i) e6(i) e7(i) ... e4(i+7) e5(i+7) e6(i+7) e7(i+7)
        // e0(i) e1(i) e2(i) e3(i) e4(i) e5(i) e6(i) e7(i)... e0(i+3) e1(i+3)
        __m512i tmp5 = _mm512_permutex2var_epi16(tmp2, p16a, tmp3);
        // e2(i+3) e3(i+3) e4(i+3) e5(i+3) e6(i+3) e7(i+3))
        _mm512_storeu_si512(f_512++, _mm512_permutex2var_epi32(tmp4, p32a, tmp5));
        // e0(i+4) e1(i+4) e2(i+4) e3(i+4) e4(i+4) e5(i+4) e6(i+4) e7(i+4)...
        // e0(i+7) e1(i+7) e2(i+7) e3(i+7) e4(i+7) e5(i+7) e6(i+7) e7(i+7))
        _mm512_storeu_si512(f_512++, _mm512_permutex2var_epi32(tmp4, p32b, tmp5));
        // e0(i+8) e1(i+8) e2(i+8) e3(i+8) ... e0(i+15) e1(i+15) e2(i+15) e3(i+15)
        tmp4 = _mm512_permutex2var_epi16(tmp0, p16b, tmp1);
        // e4(i+8) e5(i+8) e6(i+8) e7(i+8) ... e4(i+15) e5(i+15) e6(i+15) e7(i+15)
        tmp5 = _mm512_permutex2var_epi16(tmp2, p16b, tmp3);
        // e0(i+8) e1(i+8) e2(i+8) e3(i+8) e4(i+8) e5(i+8) e6(i+8) e7(i+8)... e0(i+11)
        // e1(i+11) e2(i+11) e3(i+11) e4(i+11) e5(i+11) e6(i+11) e7(i+11))
        _mm512_storeu_si512(f_512++, _mm512_permutex2var_epi32(tmp4, p32a, tmp5));
        // e0(i+12) e1(i+12) e2(i+12) e3(i+12) e4(i+12) e5(i+12) e6(i+12) e7(i+12)... e0(i+15)
        // e1(i+15) e2(i+15) e3(i+15) e4(i+15) e5(i+15) e6(i+15) e7(i+15))
        _mm512_storeu_si512(f_512++, _mm512_permutex2var_epi32(tmp4, p32b, tmp5));

        tmp0 = _mm512_permutex2var_epi8(e0j, p8b, e1j); // e0(i+16) e1(i+16) e0(i+17) e1(i+17) .... e0(i+31) e1(i+31)
        tmp1 = _mm512_permutex2var_epi8(e2j, p8b, e3j); // e2(i+16) e3(i+16) e2(i+17) e3(i+17) .... e2(i+31) e3(i+31)
        tmp2 = _mm512_permutex2var_epi8(e4j, p8b, e5j); // e4(i+16) e5(i+16) e4(i+17) e5(i+17) .... e4(i+31) e5(i+31)
        tmp3 = _mm512_permutex2var_epi8(e6j, p8b, e7j); // e6(i+16) e7(i+16) e6(i+17) e7(i+17) .... e6(i+31) e7(i+31)
        // e0(i+!6) e1(i+16) e2(i+16) e3(i+16) ... e0(i+23) e1(i+23) e2(i+23) e3(i+23)
        tmp4 = _mm512_permutex2var_epi16(tmp0, p16a, tmp1);
        // e4(i+16) e5(i+16) e6(i+16) e7(i+16) ... e4(i+23) e5(i+23) e6(i+23) e7(i+23)
        tmp5 = _mm512_permutex2var_epi16(tmp2, p16a, tmp3);
        // e0(i+16) e1(i+16) e2(i+16) e3(i+16) e4(i+16) e5(i+16) e6(i+16) e7(i+16)... e0(i+19)
        // e1(i+19) e2(i+19) e3(i+19) e4(i+19) e5(i+19) e6(i+19) e7(i+19))
        _mm512_storeu_si512(f_512++, _mm512_permutex2var_epi32(tmp4, p32a, tmp5));
        // e0(i+20) e1(i+20) e2(i+20) e3(i+20) e4(i+20) e5(i+20) e6(i+20) e7(i+20)... e0(i+23)
        // e1(i+23) e2(i+23) e3(i+23) e4(i+23) e5(i+23) e6(i+23) e7(i+23))
        _mm512_storeu_si512(f_512++, _mm512_permutex2var_epi32(tmp4, p32b, tmp5));
        // e0(i+24) e1(i+24) e2(i+24) e3(i+24) ... e0(i+31) e1(i+31) e2(i+31) e3(i+31)
        tmp4 = _mm512_permutex2var_epi16(tmp0, p16b, tmp1);
        // e4(i+24) e5(i+24) e6(i+24) e7(i+24) ... e4(i+31) e5(i+31) e6(i+31) e7(i+31)
        tmp5 = _mm512_permutex2var_epi16(tmp2, p16b, tmp3);
        // e0(i+24) e1(i+24) e2(i+24) e3(i+24) e4(i+24) e5(i+24) e6(i+24) e7(i+24)... e0(i+27)
        // e1(i+27) e2(i+27) e3(i+27) e4(i+27) e5(i+27) e6(i+27) e7(i+27))
        _mm512_storeu_si512(f_512++, _mm512_permutex2var_epi32(tmp4, p32a, tmp5));
        // e0(i+28) e1(i+28) e2(i+28) e3(i+28) e4(i+28) e5(i+28) e6(i+28) e7(i+28)... e0(i+31)
        // e1(i+31) e2(i+31) e3(i+31) e4(i+31) e5(i+31) e6(i+31) e7(i+31))
        _mm512_storeu_si512(f_512++, _mm512_permutex2var_epi32(tmp4, p32b, tmp5));
      }
      e0 = (uint8_t *)e0_512;
      e1 = (uint8_t *)e1_512;
      e2 = (uint8_t *)e2_512;
      e3 = (uint8_t *)e3_512;
      e4 = (uint8_t *)e4_512;
      e5 = (uint8_t *)e5_512;
      e6 = (uint8_t *)e6_512;
      e7 = (uint8_t *)e7_512;
      f = (uint8_t *)f_512;
#endif
#ifdef USE128BIT
      e0_128 = (simde__m128i *)e0;
      e1_128 = (simde__m128i *)e1;
      e2_128 = (simde__m128i *)e2;
      e3_128 = (simde__m128i *)e3;
      e4_128 = (simde__m128i *)e4;
      e5_128 = (simde__m128i *)e5;
      e6_128 = (simde__m128i *)e6;
      e7_128 = (simde__m128i *)e7;
      for (; i < (EQm & ~15); i += 16) {
        simde__m128i e0j = simde_mm_loadu_si128(e0_128++);
        simde__m128i e1j = simde_mm_loadu_si128(e1_128++);
        simde__m128i e2j = simde_mm_loadu_si128(e2_128++);
        simde__m128i e3j = simde_mm_loadu_si128(e3_128++);
        simde__m128i e4j = simde_mm_loadu_si128(e4_128++);
        simde__m128i e5j = simde_mm_loadu_si128(e5_128++);
        simde__m128i e6j = simde_mm_loadu_si128(e6_128++);
        simde__m128i e7j = simde_mm_loadu_si128(e7_128++);
        simde__m128i tmp0 = simde_mm_unpacklo_epi8(e0j, e1j); // e0(i) e1(i) e0(i+1) e1(i+1) .... e0(i+7) e1(i+7)
        simde__m128i tmp1 = simde_mm_unpacklo_epi8(e2j, e3j); // e2(i) e3(i) e2(i+1) e3(i+1) .... e2(i+7) e3(i+7)
        // e0(i) e1(i) e2(i) e3(i) e0(i+1) e1(i+1) e2(i+1) e3(i+1) ... e0(i+3) e1(i+3) e2(i+3) e3(i+3)
        simde__m128i tmp2 = simde_mm_unpacklo_epi16(tmp0, tmp1);
        // e0(i+4) e1(i+4) e2(i+4) e3(i+4) e0(i+5) e1(i+5) e2(i+5) e3(i+5) ... e0(i+7) e1(i+7) e2(i+7) e3(i+7)
        simde__m128i tmp3 = simde_mm_unpackhi_epi16(tmp0, tmp1);
        simde__m128i tmp4 = simde_mm_unpacklo_epi8(e4j, e5j); // e4(i) e5(i) e4(i+1) e5(i+1) .... e4(i+7) e5(i+7)
        simde__m128i tmp5 = simde_mm_unpacklo_epi8(e6j, e7j); // e6(i) e7(i) e6(i+1) e7(i+1) .... e6(i+7) e7(i+7)
        // e4(i) e5(i) e6(i) e7(i) e4(i+1) e5(i+1) e6(i+1) e7(i+1) ... e4(i+3) e5(i+3) e6(i+) e7(i+3)
        simde__m128i tmp6 = simde_mm_unpacklo_epi16(tmp4, tmp5);
        // e4(i+4) e5(i+4) e6(i+4) e7(i+4) e4(i+5) e5(i+5) e6(i+5) e7(i+5) ... e4(i+7) e5(i+7) e6(i+7) e7(i+7)
        simde__m128i tmp7 = simde_mm_unpackhi_epi16(tmp4, tmp5);
        // e0(i) e1(i) e2(i) e3(i) e4(i) e5(i) e6(i) e7(i) e0(i+1) ... e7(i+1)
        simde_mm_storeu_si128(f_128++, simde_mm_unpacklo_epi32(tmp2, tmp6));
        // e0(i+2) e1(i+2) e2(i+2) e3(i+2) e4(i+2) e5(i+2) e6(i+2) e7(i+2) e0(i+3) e1(i+3) ... e7(i+3)
        simde_mm_storeu_si128(f_128++, simde_mm_unpackhi_epi32(tmp2, tmp6));
        // e0(i+4) e1(i+4) e2(i+4) e3(i+4) e4(i+4) e5(i+4) e6(i+4) e7+4(i) e0(i+5) ... e7(i+5)
        simde_mm_storeu_si128(f_128++, simde_mm_unpacklo_epi32(tmp3, tmp7));
        // e0(i+6) e1(i+6) e2(i+6) e3(i+6) e4(i+6) e5(i+6) e6(i+6) e7(i+6) e0(i+7) e0(i+7) ... e7(i+7)
        simde_mm_storeu_si128(f_128++, simde_mm_unpackhi_epi32(tmp3, tmp7));

        tmp0 = simde_mm_unpackhi_epi8(e0j, e1j); // e0(i+8) e1(i+8) e0(i+9) e1(i+9) .... e0(i+15) e1(i+15)
        tmp1 = simde_mm_unpackhi_epi8(e2j, e3j); // e2(i+8) e3(i+8) e2(i+9) e3(i+9) .... e2(i+15) e3(i+15)
        // e0(i) e1(i) e2(i) e3(i) e0(i+1) e1(i+1) e2(i+1) e3(i+1) ... e0(i+3) e1(i+3) e2(i+3) e3(i+3)
        tmp2 = simde_mm_unpacklo_epi16(tmp0, tmp1);
        // e0(i+4) e1(i+4) e2(i+4) e3(i+4) e0(i+5) e1(i+5) e2(i+5) e3(i+5) ... e0(i+7) e1(i+7) e2(i+7) e3(i+7)
        tmp3 = simde_mm_unpackhi_epi16(tmp0, tmp1);
        tmp4 = simde_mm_unpackhi_epi8(e4j, e5j); // e4(i+8) e5(i+8) e4(i+9) e5(i+9) .... e4(i+15) e515i+7)
        tmp5 = simde_mm_unpackhi_epi8(e6j, e7j); // e6(i+8) e7(i+8) e6(i+9) e7(i+9) .... e6(i+15) e7(i+15)
        // e4(i) e5(i) e6(i) e7(i) e4(i+1) e5(i+1) e6(i+1) e7(i+1) ... e4(i+3) e5(i+3) e6(i+) e7(i+3)
        tmp6 = simde_mm_unpacklo_epi16(tmp4, tmp5);
        // e4(i+4) e5(i+4) e6(i+4) e7(i+4) e4(i+5) e5(i+5) e6(i+5) e7(i+5) ... e4(i+7) e5(i+7) e6(i+7) e7(i+7)
        tmp7 = simde_mm_unpackhi_epi16(tmp4, tmp5);
        // e0(i) e1(i) e2(i) e3(i) e4(i) e5(i) e6(i) e7(i) e0(i+1) ... e7(i+1)
        simde_mm_storeu_si128(f_128++, simde_mm_unpacklo_epi32(tmp2, tmp6));
        // e0(i+2) e1(i+2) e2(i+2) e3(i+2) e4(i+2) e5(i+2) e6(i+2) e7(i+2) e0(i+3) e1(i+3) ... e7(i+3)
        simde_mm_storeu_si128(f_128++, simde_mm_unpackhi_epi32(tmp2, tmp6));
        // e0(i+4) e1(i+4) e2(i+4) e3(i+4) e4(i+4) e5(i+4) e6(i+4) e7+4(i) e0(i+5) ... e7(i+5)
        simde_mm_storeu_si128(f_128++, simde_mm_unpacklo_epi32(tmp3, tmp7));
        // e0(i+6) e1(i+6) e2(i+6) e3(i+6) e4(i+6) e5(i+6) e6(i+6) e7(i+6) e0(i+7) e0(i+7) ... e7(i+7)
        simde_mm_storeu_si128(f_128++, simde_mm_unpackhi_epi32(tmp3, tmp7));
      }
      e0 = (uint8_t *)e0_128;
      e1 = (uint8_t *)e1_128;
      e2 = (uint8_t *)e2_128;
      e3 = (uint8_t *)e3_128;
      e4 = (uint8_t *)e4_128;
      e5 = (uint8_t *)e5_128;
      e6 = (uint8_t *)e6_128;
      e7 = (uint8_t *)e7_128;
      f = (uint8_t *)f_128;
#endif
      /* 标量处理：处理剩余的比特（SIMD 无法处理的尾部）
       * 逐个调制符号进行交织：每次取 e0, e1, e2, e3, e4, e5, e6, e7 各一个比特 */
      for (; i < EQm; i++) {
        *f++ = *e0++;  // 第i个调制符号的第0个比特
        *f++ = *e1++;  // 第i个调制符号的第1个比特
        *f++ = *e2++;  // 第i个调制符号的第2个比特
        *f++ = *e3++;  // 第i个调制符号的第3个比特
        *f++ = *e4++;  // 第i个调制符号的第4个比特
        *f++ = *e5++;  // 第i个调制符号的第5个比特
        *f++ = *e6++;  // 第i个调制符号的第6个比特
        *f++ = *e7++;  // 第i个调制符号的第7个比特
      }
    } break;
    default:
      /* 不支持的调制阶数，触发断言错误 */
      AssertFatal(false, "Unsupported modulation order Qm=%d\n", Qm);
  }
}

/* 函数：nr_deinterleaving_ldpc - NR LDPC 比特反交织（解码端）
 * 作用：将接收到的软比特序列 f 按调制阶数 Qm 进行反交织，恢复为 e 的格式
 * 数据流向：解调 f[E] → 反交织 → e[E] → 反速率匹配
 * 调用位置：在 DLSCH/ULSCH 解码链路中，解调之后、反速率匹配之前
 *
 * 参数详细说明：
 * @E: 接收比特数（必须能被 Qm 整除）
 *     - 来源：与编码端的 E 相同，由资源分配决定
 *     - 约束：E % Qm == 0，否则无法均匀分组
 *
 * @Qm: 调制阶数（与发送端一致）
 *     - 来源：从接收到的 DCI 中解析，与编码端相同
 *     - 取值：QPSK=2, 16QAM=4, 64QAM=6, 256QAM=8
 *
 * @e: 输出 - 反交织后的软比特序列，长度 E
 *     - 格式：按 Qm 个子序列重新排列的 LLR 值
 *     - 去向：传递给 nr_rate_matching_ldpc_rx() 进行反速率匹配
 *     - 数据类型：int16_t，表示软比特的对数似然比（LLR）
 *
 * @f: 输入 - 接收到的软比特序列（LLR值），长度 E
 *     - 来源：解调器输出，按调制符号连续排列的软比特
 *     - 格式：[符号0的Qm个LLR, 符号1的Qm个LLR, 符号2的Qm个LLR, ...]
 *     - 数据类型：int16_t，正值表示比特为0的概率大，负值表示比特为1的概率大
 *
 * 反交织原理（与编码端交织的逆过程）：
 * 1. 编码端交织：e 的 Qm 个子序列 → f 按调制符号排列
 *    f = [e0[0], e1[0], ..., e(Qm-1)[0], e0[1], e1[1], ..., e(Qm-1)[1], ...]
 *
 * 2. 解码端反交织：f 按调制符号 → e 的 Qm 个子序列
 *    将 f 中每 Qm 个连续的 LLR 分别分配给 e 的对应子序列
 *    e0 = [f[0], f[Qm], f[2*Qm], ...]     // 每个调制符号的第0个LLR
 *    e1 = [f[1], f[Qm+1], f[2*Qm+1], ...] // 每个调制符号的第1个LLR
 *    ...
 *
 * 3. 目的：
 *    - 恢复编码端交织前的比特排列顺序
 *    - 为反速率匹配提供正确格式的软比特输入
 *    - 保持与编码端对称的处理流程
 */
void nr_deinterleaving_ldpc(uint32_t E, uint8_t Qm, int16_t *e, int16_t *f)
{
  /* 根据调制阶数 Qm 选择不同的反交织实现 */
  switch (Qm) {
    case 2: {
      /* QPSK 反交织（Qm=2）：将 f 中连续的 LLR 对分配给 e0 和 e1 子序列
       * 输入：f = [f0, f1, f2, f3, f4, f5, ...]（按调制符号排列）
       * 输出：e0 = [f0, f2, f4, ...], e1 = [f1, f3, f5, ...]（按子序列排列）*/
      AssertFatal(E % 2 == 0, "E=%d must be divisible by Qm=2", E);

      int16_t *e1 = e + (E / 2);  // e1 子序列起始位置（e0 子序列在 e 开头）
      int16_t *end = f + E - 1;   // f 的结束位置

      /* 逐对处理：每次从 f 取 2 个连续的 LLR，分别分配给 e0 和 e1 */
      while (f < end) {
        *e++ = *f++;   // f 的偶数位置 → e0 子序列
        *e1++ = *f++;  // f 的奇数位置 → e1 子序列
      }
    } break;
    case 4: {
      /* 16QAM 反交织（Qm=4）：将 f 中连续的 4 个 LLR 分配给 e0, e1, e2, e3 子序列
       * 输入：f = [f0, f1, f2, f3, f4, f5, f6, f7, ...]（按调制符号排列）
       * 输出：e0 = [f0, f4, f8, ...], e1 = [f1, f5, f9, ...], e2 = [f2, f6, f10, ...], e3 = [f3, f7, f11, ...]*/
      AssertFatal(E % 4 == 0, "E=%d must be divisible by Qm=4", E);

      int16_t *e1 = e + (E / 4);   // e1 子序列起始位置
      int16_t *e2 = e1 + (E / 4);  // e2 子序列起始位置
      int16_t *e3 = e2 + (E / 4);  // e3 子序列起始位置
      int16_t *end = f + E - 3;    // f 的结束位置（-3 确保最后一组有 4 个元素）

      /* 逐组处理：每次从 f 取 4 个连续的 LLR，分别分配给 e0, e1, e2, e3 */
      while (f < end) {
        *e++ = *f++;   // f 的第0个位置 → e0 子序列
        *e1++ = *f++;  // f 的第1个位置 → e1 子序列
        *e2++ = *f++;  // f 的第2个位置 → e2 子序列
        *e3++ = *f++;  // f 的第3个位置 → e3 子序列
      }
    } break;
    case 6: {
      /* 64QAM 反交织（Qm=6）：将 f 中连续的 6 个 LLR 分配给 e0, e1, e2, e3, e4, e5 子序列
       * 输入：f = [f0, f1, f2, f3, f4, f5, f6, f7, f8, f9, f10, f11, ...]（按调制符号排列）
       * 输出：e0 = [f0, f6, f12, ...], e1 = [f1, f7, f13, ...], ..., e5 = [f5, f11, f17, ...]*/
      AssertFatal(E % 6 == 0, "E=%d must be divisible by Qm=6", E);

      int16_t *e1 = e + (E / 6);   // e1 子序列起始位置
      int16_t *e2 = e1 + (E / 6);  // e2 子序列起始位置
      int16_t *e3 = e2 + (E / 6);  // e3 子序列起始位置
      int16_t *e4 = e3 + (E / 6);  // e4 子序列起始位置
      int16_t *e5 = e4 + (E / 6);  // e5 子序列起始位置
      int16_t *end = f + E - 5;    // f 的结束位置（-5 确保最后一组有 6 个元素）

      /* 逐组处理：每次从 f 取 6 个连续的 LLR，分别分配给 e0, e1, e2, e3, e4, e5 */
      while (f < end) {
        *e++ = *f++;   // f 的第0个位置 → e0 子序列
        *e1++ = *f++;  // f 的第1个位置 → e1 子序列
        *e2++ = *f++;  // f 的第2个位置 → e2 子序列
        *e3++ = *f++;  // f 的第3个位置 → e3 子序列
        *e4++ = *f++;  // f 的第4个位置 → e4 子序列
        *e5++ = *f++;  // f 的第5个位置 → e5 子序列
      }
    } break;
    case 8: {
      /* 256QAM 反交织（Qm=8）：将 f 中连续的 8 个 LLR 分配给 e0, e1, e2, e3, e4, e5, e6, e7 子序列
       * 输入：f = [f0, f1, f2, f3, f4, f5, f6, f7, f8, f9, f10, f11, f12, f13, f14, f15, ...]（按调制符号排列）
       * 输出：e0 = [f0, f8, f16, ...], e1 = [f1, f9, f17, ...], ..., e7 = [f7, f15, f23, ...]*/
      AssertFatal(E % 8 == 0, "E=%d must be divisible by Qm=8", E);

      int16_t *e1 = e + (E / 8);   // e1 子序列起始位置
      int16_t *e2 = e1 + (E / 8);  // e2 子序列起始位置
      int16_t *e3 = e2 + (E / 8);  // e3 子序列起始位置
      int16_t *e4 = e3 + (E / 8);  // e4 子序列起始位置
      int16_t *e5 = e4 + (E / 8);  // e5 子序列起始位置
      int16_t *e6 = e5 + (E / 8);  // e6 子序列起始位置
      int16_t *e7 = e6 + (E / 8);  // e7 子序列起始位置
      int16_t *end = f + E - 7;    // f 的结束位置（-7 确保最后一组有 8 个元素）

      /* 逐组处理：每次从 f 取 8 个连续的 LLR，分别分配给 e0, e1, e2, e3, e4, e5, e6, e7 */
      while (f < end) {
        *e++ = *f++;   // f 的第0个位置 → e0 子序列
        *e1++ = *f++;  // f 的第1个位置 → e1 子序列
        *e2++ = *f++;  // f 的第2个位置 → e2 子序列
        *e3++ = *f++;  // f 的第3个位置 → e3 子序列
        *e4++ = *f++;  // f 的第4个位置 → e4 子序列
        *e5++ = *f++;  // f 的第5个位置 → e5 子序列
        *e6++ = *f++;  // f 的第6个位置 → e6 子序列
        *e7++ = *f++;  // f 的第7个位置 → e7 子序列
      }
    } break;
    default:
      /* 不支持的调制阶数，触发断言错误 */
      AssertFatal(1 == 0, "Unsupported modulation order Qm=%d for deinterleaving\n", Qm);
      break;
  }
}

/* 中文：nr_rate_matching_ldpc —— 编码端速率匹配（比特选择与重复）。，即对TB进行删除或者填充操作，得到目标长度 E 的比特序列 e。
 * 关键参数：
 * - N = (BG==1? 66:50)*Z 为一段的编码比特总长；
 * - Ncb = min(N, Nref)，若 Tbslbrm!=0，则 Nref=3*Tbslbrm/(2*C)（LBRM限制，R=2/3）；
 * - k0 由 index_k0[BG-1][rvidx] 给出，缩放到 Ncb 并乘 Z，作为起始 ind；
 * - 跳过 filler 区间 [Foffset, Foffset+F)；不足 E 时，从头/两段两侧继续取，形成重复。*/

/* 函数：nr_rate_matching_ldpc - NR LDPC 速率匹配（编码端）
 * 作用：将 LDPC 编码后的比特序列 d 进行速率匹配，输出长度为 E 的比特序列 e
 * 数据流向：LDPC编码 d[Ncb] → 速率匹配 → e[E] → 交织
 * 调用位置：
 *   - openair1/PHY/NR_TRANSPORT/nr_dlsch_coding.c (PDSCH编码)
 *   - openair1/PHY/NR_UE_TRANSPORT/nr_ulsch_coding.c (PUSCH编码)
 *   - 在 LDPC 编码之后，交织之前调用
 *
 * 参数说明：
 * @Tbslbrm: LBRM（Limited Buffer Rate Matching）的TBS限制，0表示不启用LBRM
 * @BG: Base Graph类型（1或2），决定码字结构
 * @Z: 提升因子，决定码字长度 N=(BG==1?66:50)*Z
 * @d: 输入 - LDPC编码后的比特序列，长度 Ncb，包含系统位+校验位
 * @e: 输出 - 速率匹配后的比特序列，长度 E
 * @C: 代码块总数（当前处理第几个CB）
 * @F: 填充比特数量（filler bits）
 * @Foffset: 填充比特在 d 中的起始位置
 * @rvidx: 冗余版本索引（0,1,2,3），决定抽取起点，用于HARQ
 * @E: 期望输出的比特数，由资源分配和调制阶数决定
 *
 * 返回值：0成功，-1失败
 */
int nr_rate_matching_ldpc(uint32_t Tbslbrm,
                          uint8_t BG,
                          uint16_t Z,
                          uint8_t *d,
                          uint8_t *e,
                          uint8_t C,
                          uint32_t F,
                          uint32_t Foffset,
                          uint8_t rvidx,
                          uint32_t E)
{
  if (C == 0) {
    LOG_E(PHY, "nr_rate_matching: invalid parameter C %d\n", C);
    return -1;
  }

  /* 中文：计算一段的编码比特长度 N 以及受 LBRM 限制的 Ncb。Tbslbrm=0 表示不限制，Ncb=N。
   *
   * 数据来源说明：
   * - BG, Z: 来自编码配置，由码块大小和系统参数决定
   * - Tbslbrm: 来自RRC配置的LBRM参数，用于限制缓冲区大小
   * - C: 来自传输块分段结果，表示总代码块数，传输块分段的实现代码位于： openair1/PHY/CODING/nr_segmentation.c
   */

  //Bit selection - 比特选择阶段
  uint32_t N = (BG == 1) ? (66 * Z) : (50 * Z);  // 一个码字的总长度
  uint32_t Ncb;  // 实际可用的码字长度（可能受LBRM限制）
  if (Tbslbrm == 0)
    Ncb = N;  // 不启用LBRM，使用完整码字长度
  else {
    uint32_t Nref = 3 * Tbslbrm / (2 * C); //R_LBRM = 2/3，LBRM假设码率
    Ncb = min(N, Nref);  // 取较小值，限制缓冲区大小，和N比较后，得到的实际可用的码字长度
  }

  /* 中文：按 BG 与 RV 取 k0，并按比例缩放至 Ncb，再乘 Z 得到起始 ind（bit 选择起点）。
   *
   * 数据来源说明：
   * - index_k0: 38.212标准表格，根据BG和RV确定起始位置
   * - rvidx: 来自DCI中的RV字段，HARQ重传时会变化（0→2→3→1循环）
   * - 目的：不同RV提供不同的冗余信息，提高HARQ性能
   */

  uint32_t ind = (index_k0[BG - 1][rvidx] * Ncb / N) * Z;  // 计算比特选择起始索引

#ifdef RM_DEBUG
  printf("nr_rate_matching_ldpc: E %u, F %u, Foffset %u, k0 %u, Ncb %u, rvidx %d, Tbslbrm %u\n",
         E,
         F,
         Foffset,
         ind,
         Ncb,
         rvidx,
         Tbslbrm);
#endif

  if (Foffset > E) {
    LOG_E(PHY,
          "nr_rate_matching: invalid parameters (Foffset %d > E %d) F %d, k0 %d, Ncb %d, rvidx %d, Tbslbrm %d\n",
          Foffset,
          E,
          F,
          ind,
          Ncb,
          rvidx,
          Tbslbrm);
    return -1;
  }
  if (Foffset > Ncb) {
    LOG_E(PHY, "nr_rate_matching: invalid parameters (Foffset %d > Ncb %d)\n", Foffset, Ncb);
    return -1;
  }

  /* 中文：若起始 ind 落入填充区 [Foffset, Foffset+F)，则跳过到填充区末端。
   *
   * 数据来源说明：
   * - F, Foffset: 来自码块分段过程，当信息比特不足时需要填充
   * - 填充区域的比特不能传输，必须跳过
   * - 这确保了速率匹配不会选择填充比特
   */

  if (ind >= Foffset && ind < (F + Foffset))
    ind = F + Foffset;  // 跳过填充区域

  uint32_t k = 0;  // e 数组的填充索引

  /* 第一阶段：根据起始位置 ind 与填充区的关系，分情况拷贝
   * 数据流动：d[有效区域] → e[0..k-1]，跳过填充区 [Foffset, Foffset+F)
   */
  if (ind < Foffset) {
    // 情况1：起始位置在填充区之前，需要分两段拷贝
    memcpy((void *)e, (void *)(d + ind), Foffset - ind);  // 拷贝填充区前的部分

    if (E + F <= Ncb - ind) {
      // 子情况1a：所需比特数较少，拷贝填充区后的部分即可完成
      memcpy((void *)(e + Foffset - ind), (void *)(d + Foffset + F), E - Foffset + ind);
      k = E;
    } else {
      // 子情况1b：需要拷贝填充区后的所有比特，但仍不够
      memcpy((void *)(e + Foffset - ind), (void *)(d + Foffset + F), Ncb - Foffset - F);
      k = Ncb - F - ind;  // 已拷贝的比特数（扣除填充区）
    }
  } else {
    // 情况2：起始位置在填充区之后（或已跳过填充区）
    if (E <= Ncb - ind) {
      // 子情况2a：所需比特数不超过剩余长度，直接拷贝完成
      memcpy((void *)(e), (void *)(d + ind), E);
      k = E;
    } else {
      // 子情况2b：所需比特数超过剩余长度，先拷贝到码字末尾
      memcpy((void *)(e), (void *)(d + ind), Ncb - ind);
      k = Ncb - ind;  // 已拷贝的比特数
    }
  }

  /* 第二阶段：重复填充阶段（当 k < E 时）
   * 中文：当已选比特不足 E 时，进入重复阶段：从 0..Ncb-1 顺序遍历，跳过填充区，将 d[ind] 复制到 e[k]，直到满足 E。
   *
   * 数据流动：循环从 d[0..Ncb-1]（跳过填充区）→ e[k..E-1]
   * 作用：当信道条件差或码率低时，需要重复传输编码比特以提供更多冗余
   * 应用场景：低MCS、边缘用户、初始接入等需要高可靠性的场合
   */

  while (k < E) { // case where we do repetitions (low mcs)
    for (ind = 0; (ind < Ncb) && (k < E); ind++) {
#ifdef RM_DEBUG
      printf("RM_TX k%u Ind: %u (%d)\n", k, ind, d[ind]);
#endif

      if (ind == Foffset)
        ind = F + Foffset; // skip filler bits - 跳过填充比特

      e[k++] = d[ind];  // 拷贝有效比特到输出序列

    }
  }

  return 0;
}

/* 中文：nr_rate_matching_ldpc_rx —— 解码端反速率匹配。
 * 将反交织的软比特 soft_input（长度 E）累加回 d（长度 Ncb）；
 * 若 clear==1，先将 d 清零；随后按与编码端对称的顺序/跳过 filler，完成一次“增量HARQ”累加。*/

/* 函数：nr_rate_matching_ldpc_rx - NR LDPC 反速率匹配（解码端）
 * 作用：将反交织后的软比特序列 soft_input 按速率匹配的逆过程累加到 d 中，支持HARQ软合并
 * 数据流向：反交织 soft_input[E] → 反速率匹配 → d[Ncb] → LDPC解码
 * 调用位置：
 *   - openair1/PHY/NR_TRANSPORT/nr_dlsch_decoding.c (PDSCH解码)
 *   - openair1/PHY/NR_UE_TRANSPORT/nr_ulsch_decoding.c (PUSCH解码)
 *   - 在反交织之后，LDPC解码之前调用
 *
 * 参数说明：
 * @Tbslbrm: LBRM的TBS限制，与编码端一致
 * @BG: Base Graph类型，与编码端一致
 * @Z: 提升因子，与编码端一致
 * @d: 输出 - 累加后的软比特序列，长度 Ncb，作为LDPC解码器输入
 * @soft_input: 输入 - 反交织后的软比特序列（LLR值），长度 E
 * @C: 代码块总数，与编码端一致
 * @rvidx: 冗余版本索引，与当前接收的传输一致
 * @clear: HARQ控制标志，1=首轮清零，0=累加到现有软信息
 * @E: 接收到的软比特数，与编码端输出的E一致
 * @F: 填充比特数量，与编码端一致
 * @Foffset: 填充比特起始位置，与编码端一致
 *
 * 返回值：0成功，-1失败
 *
 * HARQ软合并原理：
 * - 首轮传输：clear=1，先清零d，然后累加soft_input
 * - 重传轮次：clear=0，在现有d的基础上累加新的soft_input
 * - 不同RV提供不同位置的冗余信息，累加后提高解码成功率
 */
int nr_rate_matching_ldpc_rx(uint32_t Tbslbrm,
                             uint8_t BG,
                             uint16_t Z,
                             int16_t *d,
                             int16_t *soft_input,
                             uint8_t C,
                             uint8_t rvidx,
                             uint8_t clear,
                             uint32_t E,
                             uint32_t F,
                             uint32_t Foffset)
{
  if (C == 0) {
    LOG_E(PHY, "nr_rate_matching: invalid parameter C %d\n", C);
    return -1;
  }

  //Bit selection
  uint32_t N = (BG == 1) ? (66 * Z) : (50 * Z);
  uint32_t Ncb;
  if (Tbslbrm == 0)
    Ncb = N;
  else {
    uint32_t Nref = (3 * Tbslbrm / (2 * C)); //R_LBRM = 2/3
    Ncb = min(N, Nref);
  }

  uint32_t ind = (index_k0[BG - 1][rvidx] * Ncb / N) * Z;
  /* 中文：与编码端一致地计算起始 ind（k0 缩放乘 Z）。*/

  if (Foffset > E) {
    LOG_E(PHY, "nr_rate_matching: invalid parameters (Foffset %d > E %d)\n", Foffset, E);
    return -1;
  }
  if (Foffset > Ncb) {
    LOG_E(PHY, "nr_rate_matching: invalid parameters (Foffset %d > Ncb %d)\n", Foffset, Ncb);
    return -1;
  }

#ifdef RM_DEBUG
  printf("nr_rate_matching_ldpc_rx: Clear %d, E %u, Foffset %u, k0 %u, Ncb %u, rvidx %d, Tbslbrm %u\n",
         clear,
         E,
         Foffset,
         ind,
         Ncb,
         rvidx,
         Tbslbrm);
#endif

  /* HARQ软合并控制
   * 中文：HARQ 首轮：clear==1 → 将 d 清零；后续轮：在原有 d 的基础上累加 soft_input。
   *
   * 数据来源说明：
   * - clear标志来自MAC层HARQ进程管理，首轮传输时为1，重传时为0
   * - 这样可以实现Chase合并（相同RV）或增量冗余合并（不同RV）
   */
  if (clear == 1)
    memset(d, 0, Ncb * sizeof(int16_t));  // 首轮：清零软比特缓冲区

  uint32_t k = 0;
  /* 中文：第一段：若 ind < Foffset，先把 filler 前的区间累加；若进入 filler 区间则跳过到末尾再继续。*/

  if (ind < Foffset)
    for (; (ind < Foffset) && (k < E); ind++) {
#ifdef RM_DEBUG
      printf("RM_RX k%u Ind %u(before filler): %d (%d)=>", k, ind, d[ind], soft_input[k]);
#endif
      d[ind] += soft_input[k++];
#ifdef RM_DEBUG
      printf("%d\n", d[ind]);
#endif
    }
  if (ind >= Foffset && ind < Foffset + F)
    ind = Foffset + F;

  for (; (ind < Ncb) && (k < E); ind++) {
#ifdef RM_DEBUG
    printf("RM_RX k%u Ind %u(after filler) %d (%d)=>", k, ind, d[ind], soft_input[k]);
#endif
    d[ind] += soft_input[k++];
#ifdef RM_DEBUG
    printf("%d\n", d[ind]);
#endif
  }

  while (k < E) {
  /* 中文：当 soft_input 尚未用尽（k<E），进行环回：先累加 [0, Foffset)，再累加 [Foffset+F, Ncb)，直至完成。*/

    for (ind = 0; (ind < Foffset) && (k < E); ind++) {
#ifdef RM_DEBUG
      printf("RM_RX k%u Ind %u(before filler) %d(%d)=>", k, ind, d[ind], soft_input[k]);
#endif
      d[ind] += soft_input[k++];
#ifdef RM_DEBUG
      printf("%d\n", d[ind]);
#endif
    }
    for (ind = Foffset + F; (ind < Ncb) && (k < E); ind++) {
#ifdef RM_DEBUG
      printf("RM_RX (after filler) k%u Ind: %u (%d)(soft in %d)=>", k, ind, d[ind], soft_input[k]);
#endif
      d[ind] += soft_input[k++];
#ifdef RM_DEBUG
      printf("%d\n", d[ind]);
#endif
    }
  }
  return 0;
}
