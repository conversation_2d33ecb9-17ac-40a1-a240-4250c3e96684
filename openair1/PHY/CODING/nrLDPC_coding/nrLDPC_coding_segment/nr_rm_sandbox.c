#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>
#include <math.h>

#define min(a,b) ((a)<(b)?(a):(b))
#define max(a,b) ((a)>(b)?(a):(b))

// Simplified rate matching functions without OAI dependencies
static const uint8_t index_k0[2][4] = {{0, 17, 33, 56}, {0, 13, 25, 43}};

// Simplified nr_rate_matching_ldpc (encoding)
int nr_rate_matching_ldpc_simple(uint8_t BG, uint16_t Z, uint8_t *d, uint8_t *e,
                                  uint32_t F, uint32_t Foffset, uint8_t rvidx, uint32_t E) {
  uint32_t N = (BG == 1) ? (66 * Z) : (50 * Z);
  uint32_t Ncb = N; // simplified: no LBRM
  uint32_t ind = (index_k0[BG - 1][rvidx] * Ncb / N) * Z;
  uint32_t k = 0;

  printf("RM_TX: BG=%u Z=%u N=%u Ncb=%u rvidx=%u -> ind=%u (F=%u Foffset=%u) E=%u\n",
         BG, Z, N, Ncb, rvidx, ind, F, Foffset, E);

  // Skip filler if starting point falls in filler region
  if (ind >= Foffset && ind < (F + Foffset)) {
    printf("  ind %u in filler [%u,%u), skip to %u\n", ind, Foffset, F+Foffset, F+Foffset);
    ind = F + Foffset;
  }

  // First segment: copy from ind to end or until E is satisfied
  if (E <= Ncb - ind) {
    printf("  Direct copy: e[0..%u] = d[%u..%u]\n", E-1, ind, ind+E-1);
    memcpy(e, d + ind, E);
    k = E;
  } else {
    printf("  Tail copy: e[0..%u] = d[%u..%u]\n", Ncb-ind-1, ind, Ncb-1);
    memcpy(e, d + ind, Ncb - ind);
    k = Ncb - ind;
  }

  // Repetition if needed
  while (k < E) {
    printf("  Repetition round, k=%u, need %u more\n", k, E-k);
    for (ind = 0; (ind < Ncb) && (k < E); ind++) {
      if (ind == Foffset) {
        printf("    Skip filler [%u,%u)\n", Foffset, F+Foffset);
        ind = F + Foffset;
        if (ind >= Ncb) break;
      }
      e[k++] = d[ind];
    }
  }

  return 0;
}

// Simplified nr_rate_matching_ldpc_rx (decoding)
int nr_rate_matching_ldpc_rx_simple(uint8_t BG, uint16_t Z, int16_t *d, int16_t *soft_input,
                                     uint8_t rvidx, uint8_t clear, uint32_t E,
                                     uint32_t F, uint32_t Foffset) {
  uint32_t N = (BG == 1) ? (66 * Z) : (50 * Z);
  uint32_t Ncb = N; // simplified: no LBRM
  uint32_t ind = (index_k0[BG - 1][rvidx] * Ncb / N) * Z;
  uint32_t k = 0;

  printf("RM_RX: BG=%u Z=%u N=%u Ncb=%u rvidx=%u -> ind=%u clear=%u E=%u\n",
         BG, Z, N, Ncb, rvidx, ind, clear, E);

  if (clear == 1) {
    printf("  HARQ clear: zero d[0..%u]\n", Ncb-1);
    memset(d, 0, Ncb * sizeof(int16_t));
  }

  // Skip filler if starting point falls in filler region
  if (ind >= Foffset && ind < (F + Foffset)) {
    printf("  ind %u in filler [%u,%u), skip to %u\n", ind, Foffset, F+Foffset, F+Foffset);
    ind = F + Foffset;
  }

  // First segment: accumulate from ind to end or until E is consumed
  if (E <= Ncb - ind) {
    printf("  Direct accumulate: d[%u..%u] += soft_input[0..%u]\n", ind, ind+E-1, E-1);
    for (uint32_t i = 0; i < E; i++) d[ind + i] += soft_input[k++];
  } else {
    printf("  Tail accumulate: d[%u..%u] += soft_input[0..%u]\n", ind, Ncb-1, Ncb-ind-1);
    for (uint32_t i = 0; i < Ncb - ind; i++) d[ind + i] += soft_input[k++];
  }

  // Repetition accumulation if needed
  while (k < E) {
    printf("  Repetition accumulate, k=%u, remaining %u\n", k, E-k);
    for (ind = 0; (ind < Ncb) && (k < E); ind++) {
      if (ind == Foffset) {
        printf("    Skip filler [%u,%u)\n", Foffset, F+Foffset);
        ind = F + Foffset;
        if (ind >= Ncb) break;
      }
      d[ind] += soft_input[k++];
    }
  }

  return 0;
}

// Simplified interleaving
void nr_interleaving_ldpc_simple(uint32_t E, uint8_t Qm, uint8_t *e, uint8_t *f) {
  printf("Interleaving: E=%u Qm=%u\n", E, Qm);
  if (E % Qm != 0) {
    printf("ERROR: E=%u not divisible by Qm=%u\n", E, Qm);
    return;
  }

  uint32_t H = E / Qm;
  for (uint32_t i = 0; i < H; i++) {
    for (uint8_t j = 0; j < Qm; j++) {
      f[i * Qm + j] = e[j * H + i];
    }
  }
}

// Simplified deinterleaving
void nr_deinterleaving_ldpc_simple(uint32_t E, uint8_t Qm, int16_t *e, int16_t *f) {
  printf("Deinterleaving: E=%u Qm=%u\n", E, Qm);
  if (E % Qm != 0) {
    printf("ERROR: E=%u not divisible by Qm=%u\n", E, Qm);
    return;
  }

  uint32_t H = E / Qm;
  for (uint32_t i = 0; i < H; i++) {
    for (uint8_t j = 0; j < Qm; j++) {
      e[j * H + i] = f[i * Qm + j];
    }
  }
}

/* A tiny sandbox to exercise NR rate matching / interleaving with fixed parameters
 * and print key intermediate values so you can set breakpoints and follow the flow. */

static void fill_d(uint8_t *d, uint32_t Ncb)
{
  /* Fill coded bits buffer with a recognizable ramp pattern 0,1,2,... for clarity */
  for (uint32_t i = 0; i < Ncb; ++i) d[i] = (uint8_t)(i & 0xFF);
}

static void print_array_u8(const char *name, const uint8_t *a, uint32_t len, uint32_t max_print)
{
  printf("%s[%u]: ", name, len);
  for (uint32_t i = 0; i < len && i < max_print; ++i) printf("%02x ", a[i]);
  if (len > max_print) printf("...");
  printf("\n");
}

static void print_array_i16(const char *name, const int16_t *a, uint32_t len, uint32_t max_print)
{
  printf("%s[%u]: ", name, len);
  for (uint32_t i = 0; i < len && i < max_print; ++i) printf("%d ", a[i]);
  if (len > max_print) printf("...");
  printf("\n");
}

int main(void)
{
  printf("=== NR Rate Matching Sandbox ===\n");

  /* Example parameters — you can tweak and rebuild:
   * BG=1, Z=128, rv=2; choose Foffset/F and E as desired. */
  const uint8_t BG = 1;
  const uint16_t Z = 128;
  const uint8_t rvidx = 2;     /* RV=2 */

  /* Filler region [Foffset, Foffset+F) — try (0,0) for none, or (2048,256) */
  const uint32_t Foffset = 0;  /* try 2048 */
  const uint32_t F = 0;        /* try 256 */

  /* Choose E (bits after rate-matching). Try 3600 and 4998 to see two paths. */
  const uint32_t E = 4998; // divisible by Qm=6

  /* Derived values */
  const uint32_t N = (BG == 1) ? (66u * Z) : (50u * Z);
  const uint32_t Ncb = N; /* simplified: no LBRM */

  printf("Parameters: BG=%u Z=%u N=%u Ncb=%u rvidx=%u Foffset=%u F=%u E=%u\n",
         BG, Z, N, Ncb, rvidx, Foffset, F, E);

  /* Buffers */
  uint8_t *d = (uint8_t *)malloc(Ncb);
  uint8_t *e = (uint8_t *)malloc(E);
  uint8_t *f = (uint8_t *)malloc(E);
  int16_t *d_rx = (int16_t *)calloc(Ncb, sizeof(int16_t));
  int16_t *e_rx = (int16_t *)calloc(E, sizeof(int16_t));

  if (!d || !e || !f || !d_rx || !e_rx) {
    perror("malloc");
    return 1;
  }

  fill_d(d, Ncb);
  printf("Filled d with ramp pattern 0,1,2,...\n");
  print_array_u8("d (first 32)", d, min(32, Ncb), 32);

  /* 1) Rate matching (TX) */
  printf("\n=== 1) Rate Matching (TX) ===\n");
  int rc = nr_rate_matching_ldpc_simple(BG, Z, d, e, F, Foffset, rvidx, E);
  if (rc != 0) { printf("nr_rate_matching_ldpc_simple failed: %d\n", rc); return 2; }
  print_array_u8("e (after RM)", e, min(64, E), 64);

  /* 2) Interleaving with an example Qm (e.g. 64QAM => Qm=6) */
  printf("\n=== 2) Interleaving ===\n");
  const uint8_t Qm = 6;
  nr_interleaving_ldpc_simple(E, Qm, e, f);
  print_array_u8("f (after interleaving)", f, min(64, E), 64);

  /* 3) Deinterleaving back to e_rx (RX side) */
  printf("\n=== 3) Deinterleaving (RX) ===\n");
  // Convert f to int16_t for soft values (simulate received LLRs)
  for (uint32_t i = 0; i < E; i++) {
    e_rx[i] = (int16_t)f[i]; // simple conversion for demo
  }
  nr_deinterleaving_ldpc_simple(E, Qm, e_rx, e_rx); // in-place deinterleaving
  print_array_i16("e_rx (after deinterleaving)", e_rx, min(64, E), 64);

  /* 4) Rate matching RX (HARQ clear=1 for first round) */
  printf("\n=== 4) Rate Matching RX (HARQ) ===\n");
  rc = nr_rate_matching_ldpc_rx_simple(BG, Z, d_rx, e_rx, rvidx, 1 /*clear*/, E, F, Foffset);
  if (rc != 0) { printf("nr_rate_matching_ldpc_rx_simple failed: %d\n", rc); return 3; }
  print_array_i16("d_rx (after HARQ accumulation)", d_rx, min(64, Ncb), 64);

  /* Verification: compare some positions */
  printf("\n=== Verification ===\n");
  printf("Original d[4224..4227]: %02x %02x %02x %02x\n", d[4224], d[4225], d[4226], d[4227]);
  printf("Reconstructed d_rx[4224..4227]: %d %d %d %d\n", d_rx[4224], d_rx[4225], d_rx[4226], d_rx[4227]);

  /* Cleanup */
  free(d); free(e); free(f); free(d_rx); free(e_rx);
  printf("\n=== Done ===\n");
  return 0;
}

