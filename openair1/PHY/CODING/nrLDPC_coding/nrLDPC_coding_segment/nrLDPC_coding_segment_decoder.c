/*
 * Licensed to the OpenAirInterface (OAI) Software Alliance under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The OpenAirInterface Software Alliance licenses this file to You under
 * the OAI Public License, Version 1.0  (the "License"); you may not use this file
 * except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.openairinterface.org/?page_id=698
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *-------------------------------------------------------------------------------
 * For more information about the OpenAirInterface (OAI) Software Alliance:
 *      <EMAIL>
 */

/*! \file PHY/CODING/nrLDPC_coding/nrLDPC_coding_segment/nrLDPC_coding_segment_decoder.c
 * \brief Top-level routines for decoding LDPC transport channels
 */
/* 中文说明：本文件实现“时隙级 LDPC 解码”的软件路径（以代码段为单位组织），
 * 串联 NR 38.212 的关键步骤：反交织 → 反速率匹配（含 HARQ 累加/清零控制）→ 段级 LDPC 迭代解码。
 * 支持时间统计、任务池派发等，并以段为粒度进行并行化处理。
 *
 * 详细功能说明：
 * 1. 时隙级解码管理：协调一个时隙内多个传输块的解码
 * 2. 传输块级解码：管理单个传输块的多个代码段解码
 * 3. 段级解码：执行单个代码段的完整解码流程
 * 4. HARQ 支持：支持软合并和增量冗余
 * 5. 多线程优化：支持并行解码多个代码段
 *
 * 解码流程：
 * 接收软比特 → 反交织 → 反速率匹配(HARQ合并) → LDPC解码 → CRC校验
 *
 * 主要函数：
 * - nrLDPC_coding_decoder(): 时隙级解码入口
 * - nrLDPC_prepare_TB_decoding(): 传输块级解码准备
 * - nr_process_decode_segment(): 段级解码核心函数
 *
 * 调用关系：
 * nr_ulsch_decoding.c → nrLDPC_coding_decoder() → nrLDPC_prepare_TB_decoding() → nr_process_decode_segment()
 *
 * 数据流向：
 * 接收LLR → 反交织 → 反速率匹配 → LDPC解码 → 解码比特 → CRC校验
 *
 * 与编码器的对应关系：
 * - 编码器：c → LDPC编码 → 速率匹配 → 交织 → f
 * - 解码器：LLR → 反交织 → 反速率匹配 → LDPC解码 → c
 */


// [from gNB coding]
#include "nr_rate_matching.h"
#include "PHY/defs_gNB.h"
#include "PHY/CODING/coding_extern.h"
#include "PHY/CODING/coding_defs.h"
#include "PHY/CODING/lte_interleaver_inline.h"
#include "PHY/CODING/nrLDPC_coding/nrLDPC_coding_interface.h"
#include "PHY/CODING/nrLDPC_extern.h"
#include "PHY/NR_TRANSPORT/nr_transport_common_proto.h"
#include "PHY/NR_TRANSPORT/nr_transport_proto.h"
#include "PHY/NR_TRANSPORT/nr_ulsch.h"
#include "PHY/NR_TRANSPORT/nr_dlsch.h"
#include "SCHED_NR/sched_nr.h"
#include "SCHED_NR/fapi_nr_l1.h"
#include "defs.h"
#include "common/utils/LOG/vcd_signal_dumper.h"
#include "common/utils/LOG/log.h"

#include <stdalign.h>
#include <stdint.h>
#include <syscall.h>
#include <time.h>
// #define gNB_DEBUG_TRACE

#define OAI_LDPC_DECODER_MAX_NUM_LLR 27000 // 26112 // NR_LDPC_NCOL_BG1*NR_LDPC_ZMAX = 68*384
// #define DEBUG_CRC
#ifdef DEBUG_CRC
#define PRINT_CRC_CHECK(a) a
#else
#define PRINT_CRC_CHECK(a)
#endif

#include "nfapi/open-nFAPI/nfapi/public_inc/nfapi_interface.h"
#include "nfapi/open-nFAPI/nfapi/public_inc/nfapi_nr_interface.h"

/**
 * \typedef nrLDPC_decoding_parameters_t
 * \struct nrLDPC_decoding_parameters_s
 * \brief decoding parameter of transport blocks
 * \var decoderParms decoder parameters
 * \var Qm modulation order
 * \var Kc ratio between the number of columns in the parity check matrix and the lifting size
 * it is fixed for a given base graph while the lifting size is chosen to have a sufficient number of columns
 * \var rv_index
 * \var max_number_iterations maximum number of LDPC iterations
 * \var abort_decode pointer to decode abort flag
 * \var tbslbrm transport block size LBRM in bytes
 * \var A Transport block size (This is A from 38.212 V15.4.0 section 5.1)
 * \var K Code block size at decoder output
 * \var Z lifting size
 * \var F filler bits size
 * \var r segment index in TB
 * \var E input llr segment size
 * \var C number of segments
 * \var llr input llr segment array
 * \var d Pointers to code blocks before LDPC decoding (38.212 V15.4.0 section 5.3.2)
 * \var d_to_be_cleared
 * pointer to the flag used to clear d properly
 * when true, clear d after rate dematching
 * \var c Pointers to code blocks after LDPC decoding (38.212 V15.4.0 section 5.2.2)
 * \var decodeSuccess pointer to the flag indicating that the decoding of the segment was successful
 * \var ans pointer to task answer used by the thread pool to detect task completion
 * \var p_ts_deinterleave pointer to deinterleaving time stats
 * \var p_ts_rate_unmatch pointer to rate unmatching time stats
 * \var p_ts_ldpc_decode pointer to decoding time stats
/* 结构体：nrLDPC_decoding_parameters_t - 段级解码任务的参数结构
 * 作用：封装传递给 nr_process_decode_segment() 函数的所有参数
 * 使用场景：多线程解码时，每个线程处理一个段的解码任务
 *
 * 成员详细说明：
 * @decoderParms: LDPC 解码器参数
 *                来源：nrLDPC_prepare_TB_decoding() 中设置
 *                包含：BG, Z, numMaxIter, R, outMode等
 * @Qm: 调制阶数（反交织时使用）
 *      来源：从 DCI 中的 MCS 索引通过 MCS 表查找得到
 *      取值：QPSK=2, 16QAM=4, 64QAM=6, 256QAM=8
 * @Kc: 校验矩阵列数与提升因子的比值
 *      来源：由基图类型决定（BG1=68, BG2=52）
 *      作用：确定 LDPC 解码器的矩阵尺寸
 * @rv_index: 冗余版本索引
 *            来源：从 DCI 中解析
 *            作用：反速率匹配时决定比特的抽取/回填位置
 * @abort_decode: 解码中止控制指针
 *                来源：上层传递的中止标志
 *                作用：允许外部中止解码过程
 * @tbslbrm: 传输块大小 LBRM 参数（字节）
 *           来源：RRC 配置参数
 *           作用：限制缓冲区速率匹配
 * @A: 原始传输块大小（比特）
 *     来源：MAC 层传递的 TB 大小
 *     作用：确定 CRC 类型和分段参数
 * @K: 代码块大小（解码器输出）
 *     来源：分段过程计算得出
 *     作用：确定解码输出的比特数
 * @Z: 提升因子
 *     来源：分段过程根据 TB 大小选择
 *     作用：确定 LDPC 矩阵的实际尺寸
 * @F: 填充比特数
 *     来源：分段过程计算得出
 *     作用：在解码时需要跳过这些比特
 * @C: 代码段总数
 *     来源：分段过程计算得出
 *     作用：确定 CRC 类型和解码参数
 * @E: 输入 LLR 段大小
 *     来源：nr_get_E() 计算得出
 *     作用：确定当前段的输入数据长度
 * @llr: 输入 LLR 段数组指针
 *       来源：解调器输出的软比特
 *       格式：int16_t 数组，正值表示比特为0的概率大
 * @d: LDPC 解码前的软比特指针
 *     来源：HARQ 缓冲区
 *     作用：存储反速率匹配后的软比特，支持 HARQ 合并
 * @d_to_be_cleared: HARQ 缓冲区清零标志指针
 *                   来源：HARQ 进程管理
 *                   作用：新传输时清零，重传时保留进行软合并
 * @c: 解码后的代码块指针
 *     来源：HARQ 缓冲区
 *     去向：传递给 CRC 校验和去分段
 * @decodeSuccess: 解码成功标志指针
 *                 来源：段级解码状态
 *                 作用：指示当前段是否解码成功
 * @ans: 任务同步对象指针
 *       来源：线程池管理
 *       作用：标记任务完成，用于线程同步
 * @p_ts_deinterleave: 反交织时间统计指针
 * @p_ts_rate_unmatch: 反速率匹配时间统计指针
 * @p_ts_ldpc_decode: LDPC 解码时间统计指针
 *                    作用：性能分析和优化
 */

 */
typedef struct nrLDPC_decoding_parameters_s {
  t_nrLDPC_dec_params decoderParms;

  uint8_t Qm;

  uint8_t Kc;
  uint8_t rv_index;
  decode_abort_t *abort_decode;

  uint32_t tbslbrm;
  uint32_t A;
  uint32_t K;
  uint32_t Z;
  uint32_t F;

  uint32_t C;

  int E;
  short *llr;
  int16_t *d;
  bool *d_to_be_cleared;
  uint8_t *c;
  bool *decodeSuccess;

  task_ans_t *ans;

  time_stats_t *p_ts_deinterleave;
  time_stats_t *p_ts_rate_unmatch;
  time_stats_t *p_ts_ldpc_decode;
} nrLDPC_decoding_parameters_t;

/* 函数：nr_process_decode_segment - 段级解码的核心函数
 * 作用：执行单个代码段的完整解码流程（反交织→反速率匹配→LDPC解码）
 * 调用位置：由 nrLDPC_prepare_TB_decoding() 通过线程池调用
 *
 * 参数说明：
 * @arg: 指向 nrLDPC_decoding_parameters_t 结构的void指针
 *       来源：nrLDPC_prepare_TB_decoding() 中为每个段准备的参数包
 *       包含：解码参数、输入LLR、输出缓冲区、HARQ状态等
 *
 * 核心流程：
 * 1. 参数解析和初始化
 * 2. 反交织（nr_deinterleaving_ldpc）
 * 3. 反速率匹配（nr_rate_matching_ldpc_rx，含HARQ合并）
 * 4. LDPC 解码（LDPCdecoder）
 * 5. 结果处理和状态更新
 *
 * HARQ 支持：
 * - 新传输：清零 d 缓冲区，重新开始
 * - 重传：保留 d 缓冲区，进行软合并
 * - 增量冗余：根据 rv_index 选择不同的比特
 *
 * 性能优化：
 * - 32字节对齐的处理缓冲区
 * - 饱和运算优化（16bit→8bit）
 * - 早停机制（CRC成功后立即停止）
 */
static void nr_process_decode_segment(void *arg)
{
  /* 参数解析：从void指针恢复具体的参数结构 */
  nrLDPC_decoding_parameters_t *rdata = (nrLDPC_decoding_parameters_t *)arg;
  t_nrLDPC_dec_params *p_decoderParms = &rdata->decoderParms;

  /* 提取关键解码参数 */
  const int K = rdata->K;                                       // 代码块大小
  const int Kprime = K - rdata->F;                              // 去除填充比特后的大小
  const int A = rdata->A;                                       // 原始TB大小
  const int E = rdata->E;                                       // 输入LLR长度
  const int Qm = rdata->Qm;                                     // 调制阶数
  const int rv_index = rdata->rv_index;                         // 冗余版本索引
  const uint8_t Kc = rdata->Kc;                                 // 校验矩阵列数比值
  short *ulsch_llr = rdata->llr;                                // 输入LLR数据

  /* 分配LDPC解码处理缓冲区：32字节对齐以优化SIMD访问 */
  int8_t llrProcBuf[OAI_LDPC_DECODER_MAX_NUM_LLR] __attribute__((aligned(32)));

  /* 初始化解码时间统计 */
  t_nrLDPC_time_stats procTime = {0};
  t_nrLDPC_time_stats *p_procTime = &procTime;

  /* 解码流程概述：
   * 1) 反交织（依据 Qm）：将按调制符号排列的LLR恢复为按子序列排列
   * 2) 反速率匹配（依据 rv_index/F/Foffset 等参数）：将 e 回填至 d，支持HARQ合并
   * 3) 段级 LDPC 迭代解码：可能早停（CRC成功）或触发中止
   * 4) 设置 decodeSuccess，并更新时间统计 */

  /* 第一步：反交织处理
   * 作用：将解调器输出的LLR从调制符号格式转换为代码段格式
   * 输入：ulsch_llr[E] - 按调制符号排列的软比特
   * 输出：harq_e[E] - 按子序列排列的软比特 */

  start_meas(rdata->p_ts_deinterleave);

  /* 分配反交织输出缓冲区：存储反交织后的软比特
   * 对应 38.212 V15.4.0 section 5.4.2.1 中的比特选择后的代码块 */
  int16_t harq_e[E];

  /* 执行反交织：这是编码端交织的逆过程
   * 将连续的LLR按 [f0,f1,f2,...,f(Qm-1)] 循环分配给 e 的 Qm 个子序列
   * 恢复编码端交织前的比特排列顺序 */
  nr_deinterleaving_ldpc(E,                                     // 比特数
                         Qm,                                    // 调制阶数
                         harq_e,                                // 输出：反交织后的软比特
                         ulsch_llr);                            // 输入：解调器输出的LLR

  stop_meas(rdata->p_ts_deinterleave);

  /* 第二步：反速率匹配处理（含HARQ支持）
   * 作用：将反交织后的软比特回填到LDPC解码器输入格式，支持HARQ软合并
   * 输入：harq_e[E] - 反交织后的软比特
   * 输出：rdata->d - HARQ缓冲区中的软比特，支持多次传输的累积 */

  start_meas(rdata->p_ts_rate_unmatch);

  /* 执行反速率匹配：这是编码端速率匹配的逆过程
   * 关键功能：
   * 1. 根据rv_index确定比特的回填位置
   * 2. 支持HARQ软合并：新传输时清零，重传时累加
   * 3. 处理填充比特和系统比特的正确位置 */
  if (nr_rate_matching_ldpc_rx(rdata->tbslbrm,                  // LBRM参数
                               p_decoderParms->BG,              // 基图类型
                               p_decoderParms->Z,               // 提升因子
                               rdata->d,                        // 输出：HARQ缓冲区
                               harq_e,                          // 输入：反交织后的软比特
                               rdata->C,                        // 代码段总数
                               rv_index,                        // 冗余版本索引
                               *rdata->d_to_be_cleared,         // HARQ清零标志
                               E,                               // 输入比特数
                               rdata->F,                        // 填充比特数
                               K - rdata->F - 2 * (p_decoderParms->Z))  // 系统比特数
      == -1) {
    /* 反速率匹配失败处理 */
    stop_meas(rdata->p_ts_rate_unmatch);
    LOG_E(PHY, "nrLDPC_coding_segment_decoder.c: Problem in rate_matching\n");

    /* 标记任务完成并返回 */
    completed_task_ans(rdata->ans);
    return;
  }
  stop_meas(rdata->p_ts_rate_unmatch);

  /* 标记HARQ缓冲区已处理，下次重传时不再清零（进行软合并） */
  *rdata->d_to_be_cleared = false;

  /* 第三步：准备LDPC解码参数 */
  p_decoderParms->crc_type = crcType(rdata->C, A);              // 根据段数和TB大小确定CRC类型
  p_decoderParms->Kprime = lenWithCrc(rdata->C, A);            // 计算包含CRC的信息比特长度

  /* 分配LDPC解码输入缓冲区：16字节对齐以优化SIMD访问 */
  int16_t z[68 * 384 + 16] __attribute__((aligned(16)));

  start_meas(rdata->p_ts_ldpc_decode);

  /* 第四步：重新排列软比特以适应LDPC解码器输入格式 */

  /* 设置前2*Z个比特为0（对应LDPC矩阵的结构化部分） */
  memset(z, 0, 2 * rdata->Z * sizeof(*z));

  /* 设置填充比特为强正值（127），表示这些比特确定为0 */
  memset(z + Kprime, 127, rdata->F * sizeof(*z));

  /* 复制系统比特：从d缓冲区复制到z缓冲区的正确位置 */
  memcpy(z + 2 * rdata->Z, rdata->d, (Kprime - 2 * rdata->Z) * sizeof(*z));

  /* 跳过填充比特，复制校验比特 */
  memcpy(z + K, rdata->d + (K - 2 * rdata->Z), (Kc * rdata->Z - K) * sizeof(*z));

  /* 第五步：数据类型转换（16bit → 8bit）以优化LDPC解码器性能 */
  simde__m128i *pv = (simde__m128i *)&z;                       // 16bit数据的SIMD指针
  int8_t l[68 * 384 + 16] __attribute__((aligned(16)));        // 8bit输出缓冲区
  simde__m128i *pl = (simde__m128i *)&l;                       // 8bit数据的SIMD指针

  /* 使用SIMD指令进行饱和打包：将16bit软比特转换为8bit */
  for (int i = 0, j = 0; j < ((Kc * rdata->Z) >> 4) + 1; i += 2, j++) {
    pl[j] = simde_mm_packs_epi16(pv[i], pv[i + 1]);            // 饱和打包：16bit→8bit
  }
  /* 第六步：执行LDPC迭代解码 */

  /* 调用LDPC解码器：这是解码的核心步骤
   * 输入：l - 8bit软比特数组
   * 输出：llrProcBuf - 解码后的硬比特
   * 返回：实际使用的迭代次数 */
  int decodeIterations =
      LDPCdecoder(p_decoderParms,                               // 解码器参数（BG, Z, 最大迭代数等）
                  0, 0, 0,                                      // 保留参数
                  l,                                            // 输入：8bit软比特
                  llrProcBuf,                                   // 输出：解码后的硬比特
                  p_procTime,                                   // 解码时间统计
                  rdata->abort_decode);                         // 中止解码标志

  /* 第七步：处理解码结果 */
  if (decodeIterations < p_decoderParms->numMaxIter) {
    /* 解码成功：CRC校验通过，提前收敛 */
    memcpy(rdata->c, llrProcBuf, K >> 3);                      // 复制解码结果到输出缓冲区
    *rdata->decodeSuccess = true;                               // 标记解码成功
  } else {
    /* 解码失败：达到最大迭代次数仍未收敛 */
    memset(rdata->c, 0, K >> 3);                               // 清零输出缓冲区
    *rdata->decodeSuccess = false;                              // 标记解码失败
  }
  stop_meas(rdata->p_ts_ldpc_decode);

  /* 标记当前段解码任务完成 */
  completed_task_ans(rdata->ans);
}

/* 函数：nrLDPC_prepare_TB_decoding - 传输块级解码任务准备器
 * 作用：为传输块的所有代码段创建解码任务，设置参数并提交到线程池
 * 调用位置：由 nrLDPC_coding_decoder() 为每个传输块调用
 *
 * 参数详细说明：
 * @nrLDPC_slot_decoding_parameters: 时隙级解码参数
 *                                   来源：nrLDPC_coding_decoder() 的输入参数
 *                                   包含：所有TB的解码参数、线程池、性能统计等
 * @pusch_id: 传输块索引（0到num_pusch_slot-1）
 *            来源：nrLDPC_coding_decoder() 的循环变量
 *            作用：标识当前处理的是哪个传输块
 * @t_info: 线程任务信息结构
 *          来源：nrLDPC_coding_decoder() 中分配
 *          作用：管理任务队列和同步
 *
 * 返回值：创建的任务数量（等于代码段数C）
 *
 * 核心功能：
 * 1. 设置公共解码参数（BG, Z, 最大迭代数等）
 * 2. 为每个代码段创建解码任务参数
 * 3. 提交所有任务到线程池并行执行
 * 4. 初始化解码状态和统计信息
 */
int nrLDPC_prepare_TB_decoding(nrLDPC_slot_decoding_parameters_t *nrLDPC_slot_decoding_parameters,
                               int pusch_id,
                               thread_info_tm_t *t_info)
{
  /* 获取当前传输块的解码参数 */
  nrLDPC_TB_decoding_parameters_t *nrLDPC_TB_decoding_parameters = &nrLDPC_slot_decoding_parameters->TBs[pusch_id];

  /* 初始化已处理段计数器 */
  *nrLDPC_TB_decoding_parameters->processedSegments = 0;

  /* 设置所有段共享的解码参数 */
  t_nrLDPC_dec_params decParams = {.check_crc = check_crc};     // CRC校验函数指针
  decParams.BG = nrLDPC_TB_decoding_parameters->BG;            // 基图类型（来自分段）
  decParams.Z = nrLDPC_TB_decoding_parameters->Z;              // 提升因子（来自分段）
  decParams.numMaxIter = nrLDPC_TB_decoding_parameters->max_ldpc_iterations;  // 最大迭代数
  decParams.outMode = 0;                                       // 输出模式（硬判决）

  /* 为每个代码段创建解码任务 */
  for (int r = 0; r < nrLDPC_TB_decoding_parameters->C; r++) {
    /* 从任务缓冲区分配参数结构 */
    nrLDPC_decoding_parameters_t *rdata = &((nrLDPC_decoding_parameters_t *)t_info->buf)[t_info->len];
    DevAssert(t_info->len < t_info->cap);                       // 确保缓冲区容量足够
    rdata->ans = t_info->ans;                                   // 设置任务同步对象
    t_info->len += 1;                                           // 更新已使用的缓冲区长度

    /* 设置段特定的解码参数 */
    decParams.R = nrLDPC_TB_decoding_parameters->segments[r].R; // 码率（来自分段计算）
    rdata->decoderParms = decParams;                            // 复制解码器参数

    /* 设置输入输出缓冲区指针 */
    rdata->llr = nrLDPC_TB_decoding_parameters->segments[r].llr;  // 输入LLR数据
    rdata->d = nrLDPC_TB_decoding_parameters->segments[r].d;      // HARQ缓冲区
    rdata->c = nrLDPC_TB_decoding_parameters->segments[r].c;      // 解码输出缓冲区

    /* 设置段级参数 */
    rdata->Kc = decParams.BG == 2 ? 52 : 68;                   // 校验矩阵列数（BG1=68, BG2=52）
    rdata->C = nrLDPC_TB_decoding_parameters->C;               // 代码段总数
    rdata->E = nrLDPC_TB_decoding_parameters->segments[r].E;   // 当前段的输入LLR长度
    rdata->A = nrLDPC_TB_decoding_parameters->A;               // 原始TB大小
    rdata->Qm = nrLDPC_TB_decoding_parameters->Qm;             // 调制阶数
    rdata->K = nrLDPC_TB_decoding_parameters->K;               // 代码块大小
    rdata->Z = nrLDPC_TB_decoding_parameters->Z;               // 提升因子
    rdata->F = nrLDPC_TB_decoding_parameters->F;               // 填充比特数
    rdata->rv_index = nrLDPC_TB_decoding_parameters->rv_index; // 冗余版本索引
    rdata->tbslbrm = nrLDPC_TB_decoding_parameters->tbslbrm;   // LBRM参数

    /* 设置控制标志和状态指针 */
    rdata->abort_decode = nrLDPC_TB_decoding_parameters->abort_decode;  // 解码中止标志
    rdata->d_to_be_cleared = nrLDPC_TB_decoding_parameters->segments[r].d_to_be_cleared;  // HARQ清零标志
    rdata->decodeSuccess = &nrLDPC_TB_decoding_parameters->segments[r].decodeSuccess;     // 解码成功标志

    /* 设置性能统计指针 */
    rdata->p_ts_deinterleave = &nrLDPC_TB_decoding_parameters->segments[r].ts_deinterleave;  // 反交织时间
    rdata->p_ts_rate_unmatch = &nrLDPC_TB_decoding_parameters->segments[r].ts_rate_unmatch;  // 反速率匹配时间
    rdata->p_ts_ldpc_decode = &nrLDPC_TB_decoding_parameters->segments[r].ts_ldpc_decode;    // LDPC解码时间

    /* 创建任务并提交到线程池 */
    task_t t = {.func = &nr_process_decode_segment, .args = rdata};  // 任务结构：函数指针+参数
    pushTpool(nrLDPC_slot_decoding_parameters->threadPool, t);       // 提交到线程池执行

    LOG_D(PHY, "Added a block to decode, in pipe: %d\n", r);
  }

  return nrLDPC_TB_decoding_parameters->C;                      // 返回创建的任务数量
}

/* 函数：nrLDPC_coding_init - LDPC编解码库初始化
 * 作用：初始化LDPC编解码相关的全局资源
 * 调用位置：系统启动时调用
 * 返回值：0表示成功 */
int32_t nrLDPC_coding_init(void)
{
  return 0;                                                     // 当前实现为空，预留接口
}

/* 函数：nrLDPC_coding_shutdown - LDPC编解码库清理
 * 作用：清理LDPC编解码相关的全局资源
 * 调用位置：系统关闭时调用
 * 返回值：0表示成功 */
int32_t nrLDPC_coding_shutdown(void)
{
  return 0;                                                     // 当前实现为空，预留接口
}

/* 函数：nrLDPC_coding_decoder - 时隙级LDPC解码器主入口
 * 作用：协调一个时隙内所有传输块的LDPC解码，是整个解码流程的顶层控制函数
 * 调用位置：由 nr_ulsch_decoding.c 中的解码函数调用
 *
 * 参数详细说明：
 * @nrLDPC_slot_decoding_parameters: 时隙级解码参数结构
 *                                   来源：上层解码函数构造的slot_parameters
 *                                   包含：所有TB的解码参数、线程池、性能统计等
 *
 * 返回值：0表示成功，非0表示错误
 *
 * 核心流程：
 * 1. 预扫描：计算所有TB的总段数
 * 2. 内存分配：为所有段分配解码参数结构
 * 3. 任务创建：为每个传输块创建解码任务
 * 4. 并行执行：通过线程池执行所有段的解码
 * 5. 结果统计：统计成功解码的段数
 *
 * 优化特点：
 * - 多TB并行：同时处理多个传输块
 * - 段级并行：每个段独立解码
 * - HARQ支持：支持软合并和增量冗余
 * - 早停机制：CRC成功后立即停止迭代
 */
int32_t nrLDPC_coding_decoder(nrLDPC_slot_decoding_parameters_t *nrLDPC_slot_decoding_parameters)
{
  /* 第一阶段：预扫描所有传输块，计算总段数 */
  int nbSegments = 0;                                           // 总段数计数器
  for (int pusch_id = 0; pusch_id < nrLDPC_slot_decoding_parameters->nb_TBs; pusch_id++) {
    nrLDPC_TB_decoding_parameters_t *nrLDPC_TB_decoding_parameters = &nrLDPC_slot_decoding_parameters->TBs[pusch_id];
    nbSegments += nrLDPC_TB_decoding_parameters->C;             // 累加每个TB的段数
  }

  /* 第二阶段：初始化任务管理结构 */
  nrLDPC_decoding_parameters_t arr[nbSegments];                 // 段解码参数数组
  task_ans_t ans;                                               // 任务同步对象
  init_task_ans(&ans, nbSegments);                              // 初始化同步对象，等待nbSegments个任务完成

  // 线程任务信息结构：管理任务队列和同步
  thread_info_tm_t t_info = {.buf = (uint8_t *)arr, .len = 0, .cap = nbSegments, .ans = &ans};

  /* 第三阶段：创建并提交所有解码任务 */
  for (int pusch_id = 0; pusch_id < nrLDPC_slot_decoding_parameters->nb_TBs; pusch_id++) {
    /* 为当前传输块准备解码任务：创建段级任务并提交到线程池 */
    (void)nrLDPC_prepare_TB_decoding(nrLDPC_slot_decoding_parameters, pusch_id, &t_info);
  }

  /* 第四阶段：等待所有解码任务完成 */
  join_task_ans(t_info.ans);                                    // 阻塞等待所有段解码任务完成

  /* 第五阶段：统计解码结果 */
  for (int pusch_id = 0; pusch_id < nrLDPC_slot_decoding_parameters->nb_TBs; pusch_id++) {
    nrLDPC_TB_decoding_parameters_t *nrLDPC_TB_decoding_parameters = &nrLDPC_slot_decoding_parameters->TBs[pusch_id];

    /* 遍历当前TB的所有段，统计成功解码的段数 */
    for (int r = 0; r < nrLDPC_TB_decoding_parameters->C; r++) {
      if (nrLDPC_TB_decoding_parameters->segments[r].decodeSuccess) {
        /* 段解码成功：增加已处理段计数器 */
        *nrLDPC_TB_decoding_parameters->processedSegments = *nrLDPC_TB_decoding_parameters->processedSegments + 1;
      }
    }
  }

  return 0;                                                     // 成功完成所有解码
}
