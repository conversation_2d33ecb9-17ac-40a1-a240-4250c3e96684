/* SPDX-License-Identifier: BSD-3-Clause
 * Copyright(c) 2017 Intel Corporation
 */

/*! \file PHY/CODING/nrLDPC_coding/nrLDPC_coding_t2/nrLDPC_coding_t2.h
 * \brief NR LDPC 硬件加速编解码头文件（Intel T2/FlexRAN DPDK BBDEV）
 *
 * 中文说明：
 * 本头文件定义了基于Intel T2卡和DPDK BBDEV框架的NR LDPC硬件加速接口。
 *
 * 主要功能：
 * 1. 定义DPDK BBDEV操作的数据类型枚举
 * 2. 设置硬件加速的基本参数和限制
 * 3. 为硬件编解码提供统一的接口定义
 *
 * 硬件加速优势：
 * - 利用专用硬件加速LDPC编解码
 * - 支持批量操作提高吞吐量
 * - 减少CPU负载，提高系统整体性能
 * - 支持HARQ软合并等高级功能
 */

#ifndef _MAIN_H_
#define _MAIN_H_

#include <stddef.h>
#include <sys/queue.h>

#include <rte_common.h>
#include <rte_hexdump.h>
#include <rte_log.h>

/* 最大批处理大小：一次可以提交给硬件的最大操作数 */
#define MAX_BURST 512U

/* BBDEV 操作数据类型枚举
 * 用于区分不同类型的数据缓冲区 */
enum op_data_type {
  DATA_INPUT = 0,        // 输入数据（编码时为信息比特，解码时为LLR）
  DATA_HARD_OUTPUT,      // 硬输出数据（编码时为编码比特，解码时为解码比特）
  DATA_NUM_TYPES,        // 数据类型总数（用于数组大小）
};

#endif
