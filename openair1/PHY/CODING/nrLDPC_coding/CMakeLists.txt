add_subdirectory(nrLDPC_coding_segment)
add_executable(nr_rm_sandbox nrLDPC_coding_segment/nr_rm_sandbox.c)
# include headers from this dir only (no external dependencies)
set_target_properties(nr_rm_sandbox PROPERTIES RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR})
target_include_directories(nr_rm_sandbox PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/nrLDPC_coding_segment)
# link math library only (no log dependency to keep it simple)
target_link_libraries(nr_rm_sandbox PRIVATE m)


add_subdirectory(nrLDPC_coding_xdma)
add_subdirectory(nrLDPC_coding_t2)
