/*
 * Licensed to the OpenAirInterface (OAI) Software Alliance under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The OpenAirInterface Software Alliance licenses this file to You under
 * the OAI Public License, Version 1.1  (the "License"); you may not use this file
 * except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.openairinterface.org/?page_id=698
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *-------------------------------------------------------------------------------
 * For more information about the OpenAirInterface (OAI) Software Alliance:
 *      <EMAIL>
 */

/*! \file openair1/PHY/CODING/nrLDPC_coding/nrLDPC_coding_interface.h
 * \brief interface for libraries implementing coding/decoding algorithms
 */
/* 中文说明：本文件定义“NR LDPC 时隙级编/解码接口”的统一抽象层（slot-level API）。
 * 上层只依赖此接口；底层可由纯软件或各类硬件加速实现（如 T2/DPDK、XDMA/FPGA）按相同签名对接。*/


#include "PHY/defs_gNB.h"

#ifndef __NRLDPC_CODING_INTERFACE__H__
#define __NRLDPC_CODING_INTERFACE__H__

/**
 * \typedef nrLDPC_segment_decoding_parameters_t
 * \struct nrLDPC_segment_decoding_parameters_s
 * \brief decoding parameter of segments
 * \var E input llr segment size
 * \var R code rate indication
 * \var llr segment input llr array
 * \var d Pointers to code blocks before LDPC decoding (38.212 V15.4.0 section 5.3.2)
 * \var d_to_be_cleared
 * pointer to the flag used to clear d properly
 * when true, clear d after rate dematching
 * \var c Pointers to code blocks after LDPC decoding (38.212 V15.4.0 section 5.2.2)
 * \var decodeSuccess
 * flag indicating that the decoding of the segment was successful
 * IT MUST BE FILLED BY THE IMPLEMENTATION
 * \var ts_deinterleave deinterleaving time stats
 * \var ts_rate_unmatch rate unmatching time stats
 * \var ts_ldpc_decode decoding time stats
 */
typedef struct nrLDPC_segment_decoding_parameters_s{
  int E;
  uint8_t R;
  short *llr;
  int16_t *d;
  bool *d_to_be_cleared;
  uint8_t *c;
  bool decodeSuccess;
  time_stats_t ts_deinterleave;
  time_stats_t ts_rate_unmatch;
  time_stats_t ts_ldpc_decode;
} nrLDPC_segment_decoding_parameters_t;
/* 中文翻译：nrLDPC_segment_decoding_parameters_t（单个代码段的解码参数）
 * - E：输入 LLR 段的比特数
 * - R：码率指示
 * - llr：该段的输入 LLR 数组
 * - d：LDPC 解码前的软信息缓冲（38.212 5.3.2）
 * - d_to_be_cleared：指示是否在新 HARQ 轮次首段清零 d（HARQ 累加控制）
 * - c：LDPC 解码后的硬比特（38.212 5.2.2）
 * - decodeSuccess：实现需要填写的“该段是否成功解码”标志
 * - ts_deinterleave/ts_rate_unmatch/ts_ldpc_decode：阶段耗时统计（反交织/反速率匹配/LDPC 解码）*/


/**
 * \typedef nrLDPC_TB_decoding_parameters_t
 * \struct nrLDPC_TB_decoding_parameters_s
 * \brief decoding parameter of transport blocks
 * \var harq_unique_pid unique id of the HARQ process
 * WARNING This id should be unique in the whole instance
 * among the active HARQ processes for the duration of the process
 * \var processedSegments
 * pointer to the number of succesfully decoded segments
 * it initially holds the total number of segments decoded after the previous HARQ round
 * it finally holds the total number of segments decoded after the current HARQ round
 * \var nb_rb number of resource blocks
 * \var Qm modulation order
 * \var mcs MCS
 * \var nb_layers number of layers
 * \var BG LDPC base graph id
 * \var rv_index redundancy version of the current HARQ round
 * \var max_ldpc_iterations maximum number of LDPC iterations
 * \var abort_decode pointer to decode abort flag
 * \var G Available radio resource bits
 * \var tbslbrm Transport block size LBRM
 * \var A Transport block size (This is A from 38.212 V15.4.0 section 5.1)
 * \var K Code block size at decoder output
 * \var Z lifting size
 * \var F filler bits size
 * \var C number of segments
 * \var segments array of segments parameters
 */
typedef struct nrLDPC_TB_decoding_parameters_s{

  uint32_t harq_unique_pid;
  uint32_t *processedSegments;

  uint16_t nb_rb;
  uint8_t Qm;
  uint8_t mcs;
  uint8_t nb_layers;

  uint8_t BG;
  uint8_t rv_index;
  uint8_t max_ldpc_iterations;
  decode_abort_t *abort_decode;

  uint32_t G;
  uint32_t tbslbrm;
  uint32_t A;
  uint32_t K;
  uint32_t Z;
  uint32_t F;

  uint32_t C;
  nrLDPC_segment_decoding_parameters_t *segments;
} nrLDPC_TB_decoding_parameters_t;
/* 中文翻译：nrLDPC_TB_decoding_parameters_t（单个传输块 TB 的解码参数）
 * - harq_unique_pid：HARQ 进程唯一 ID（在存活周期内全局唯一）
 * - processedSegments：当前 TB 在本轮结束后的累计成功段数（输入为上一轮累计值）
 * - nb_rb/Qm/mcs/nb_layers：物理层上下文（RB 数、调制阶数、MCS、层数）
 * - BG：LDPC 基图（1 或 2）
 * - rv_index：当前 HARQ 轮的冗余版本（速率匹配/反速率匹配用）
 * - max_ldpc_iterations/abort_decode：LDPC 最大迭代次数/解码中止标志
 * - G/tbslbrm/A/K/Z/F：NR 38.212 的关键尺寸参数
 * - C：代码段（CB）数量；segments：C 个段的参数数组 */


/**
 * \typedef nrLDPC_slot_decoding_parameters_t
 * \struct nrLDPC_slot_decoding_parameters_s
 * \brief decoding parameter of slot
 * \var frame frame index
 * \var slot slot index
 * \var nb_TBs number of transport blocks
 * \var threadPool pointer to the thread pool
 * The thread pool can be used by the implementation
 * in order to launch jobs internally
 * DEQUEUING THE JOBS IS DONE WITHIN THE IMPLEMENTATION
 * \var TBs array of TBs decoding parameters
 */
typedef struct nrLDPC_slot_decoding_parameters_s{
  int frame;
  int slot;
  int nb_TBs;
  tpool_t *threadPool;
  nrLDPC_TB_decoding_parameters_t *TBs;
} nrLDPC_slot_decoding_parameters_t;
/* 中文翻译：nrLDPC_slot_decoding_parameters_t（一个时隙内的解码参数）
 * - frame/slot：帧号与时隙号
 * - nb_TBs：该时隙内的 TB 数量
 * - threadPool：线程池句柄（实现可用其派发内部任务；任务出队由实现负责）
 * - TBs：TB 级参数数组（逐个 TB 下钻到各段处理）*/


/**
 * \typedef nrLDPC_segment_encoding_parameters_t
 * \struct nrLDPC_segment_encoding_parameters_s
 * \brief encoding parameter of segments
 * \var E input llr segment size
 * \var c Pointers to code blocks before LDPC encoding (38.212 V15.4.0 section 5.2.2)
 * IT MUST BE FILLED BY THE IMPLEMENTATION
 * \var ts_interleave interleaving time stats
 * \var ts_rate_match rate matching time stats
 * \var ts_ldpc_encode encoding time stats
 */
typedef struct nrLDPC_segment_encoding_parameters_s{
  int E;
  uint8_t *c;
  time_stats_t ts_interleave;
  time_stats_t ts_rate_match;
  time_stats_t ts_ldpc_encode;
} nrLDPC_segment_encoding_parameters_t;
/* 中文翻译：nrLDPC_segment_encoding_parameters_t（单个代码段的编码参数）CB
 * - E：该段经速率匹配后的比特数（与调制映射需求相关）
 * - c：实现填充的“编码后码块”输出（38.212 5.2.2），供后续速率匹配/交织使用
 * - ts_interleave/ts_rate_match/ts_ldpc_encode：阶段耗时统计（交织/速率匹配/LDPC 编码）*/


/**
 * \typedef nrLDPC_TB_encoding_parameters_t
 * \struct nrLDPC_TB_encoding_parameters_s
 * \brief encoding parameter of transport blocks
 * \var harq_unique_pid unique id of the HARQ process
 * WARNING This id should be unique in the whole instance
 * among the active HARQ processes for the duration of the process
 * \var nb_rb number of resource blocks
 * \var Qm modulation order
 * \var mcs MCS
 * \var nb_layers number of layers
 * \var BG LDPC base graph id
 * \var rv_index
 * \var G Available radio resource bits
 * \var tbslbrm Transport block size LBRM
 * \var A Transport block size (This is A from 38.212 V15.4.0 section 5.1)
 * \var Kb Number of time the lifting size needed to fit the payload of a code block
 * \var K Code block size at input of encoder
 * \var Z lifting size
 * \var F filler bits size
 * \var C number of segments
 * \var segments array of segments parameters
 * \var output input llr TB array
 */
typedef struct nrLDPC_TB_encoding_parameters_s{

  uint32_t harq_unique_pid;

  uint16_t nb_rb;
  uint8_t Qm;
  uint8_t mcs;
  uint8_t nb_layers;              // MIMO层数：1,2,4 (4×4 MIMO支持)

  uint8_t BG;
  uint8_t rv_index;

  uint32_t G;
  uint32_t tbslbrm;
  uint32_t A;
  uint32_t Kb;
  uint32_t K;
  uint32_t Z;
  uint32_t F;

  uint32_t C;
  nrLDPC_segment_encoding_parameters_t *segments;
  unsigned char *output;
} nrLDPC_TB_encoding_parameters_t;
/* 中文翻译：nrLDPC_TB_encoding_parameters_t（单个传输块 TB 的编码参数）
 * - harq_unique_pid：HARQ 进程唯一 ID
 * - nb_rb/Qm/mcs/nb_layers：物理层上下文
 * - BG/rv_index：基图与冗余版本（速率匹配用）
 * - G/tbslbrm/A/Kb/K/Z/F：NR 38.212 的关键尺寸参数（Kb 与 Z 的关系用于确定块对齐）
 * - C：代码段（CB）数量；segments：C 个段的参数数组
 * - output：该 TB 的最终输出比特流（通常为交织后、打包格式，供调制映射）*/


/**
 * \typedef nrLDPC_slot_encoding_parameters_t
 * \struct nrLDPC_slot_encoding_parameters_s
 * \brief encoding parameter of slot
 * \var frame frame index
 * \var slot slot index
 * \var nb_TBs number of transport blocks
 * \var threadPool pointer to the thread pool
 * The thread pool can be used by the implementation
 * in order to launch jobs internally
 * DEQUEUING THE JOBS IS DONE WITHIN THE IMPLEMENTATION
 * \var tinput pointer to the input timer struct
 * \var tprep pointer to the preparation timer struct
 * \var tparity pointer to the parity timer struct
 * \var toutput pointer to the output timer struct
 * \var TBs array of TBs decoding parameters
 */
typedef struct nrLDPC_slot_encoding_parameters_s{
  int frame;
  int slot;
  int nb_TBs;
  tpool_t *threadPool;
  time_stats_t *tinput;
  time_stats_t *tprep;
  time_stats_t *tparity;
  time_stats_t *toutput;
  nrLDPC_TB_encoding_parameters_t *TBs;
} nrLDPC_slot_encoding_parameters_t;
/* 中文翻译：nrLDPC_slot_encoding_parameters_t（一个时隙内的编码参数）
 * - frame/slot：帧号与时隙号
 * - nb_TBs：该时隙内的 TB 数量
 * - threadPool：线程池句柄（实现可用其派发内部任务）
 * - tinput/tprep/tparity/toutput：阶段计时（输入、准备、奇偶/编码、输出打包）
 * - TBs：TB 级参数数组 */


typedef int32_t(nrLDPC_coding_init_t)(void);
typedef int32_t(nrLDPC_coding_shutdown_t)(void);

/**
 * \brief slot decoding function interface
 * \param nrLDPC_slot_decoding_parameters pointer to the structure holding the parameters necessary for decoding
 */
typedef int32_t(nrLDPC_coding_decoder_t)(nrLDPC_slot_decoding_parameters_t *nrLDPC_slot_decoding_parameters);

/**
 * \brief slot encoding function interface
 * \param nrLDPC_slot_encoding_parameters pointer to the structure holding the parameters necessary for encoding
 */
typedef int32_t(nrLDPC_coding_encoder_t)(nrLDPC_slot_encoding_parameters_t *nrLDPC_slot_encoding_parameters);
/* 中文说明：
 * - nrLDPC_coding_decoder_t：时隙级解码入口（参数为 nrLDPC_slot_decoding_parameters_t*）
 * - nrLDPC_coding_encoder_t：时隙级编码入口（参数为 nrLDPC_slot_encoding_parameters_t*）*/


typedef struct nrLDPC_coding_interface_s {
  nrLDPC_coding_init_t *nrLDPC_coding_init;
  nrLDPC_coding_shutdown_t *nrLDPC_coding_shutdown;
  nrLDPC_coding_decoder_t *nrLDPC_coding_decoder;
  nrLDPC_coding_encoder_t *nrLDPC_coding_encoder;
} nrLDPC_coding_interface_t;
/* 中文说明：nrLDPC_coding_interface_t 聚合了实现库导出的四个入口函数指针，
 * 通过加载器装配为统一接口，便于上层透明调用不同实现（软件/硬件加速）。*/


int load_nrLDPC_coding_interface(char *version, nrLDPC_coding_interface_t *interface);
int free_nrLDPC_coding_interface(nrLDPC_coding_interface_t *interface);
/* 中文说明：load_nrLDPC_coding_interface/free_nrLDPC_coding_interface 由加载器提供，
 * 负责按版本后缀加载 libldpc<version>.so，并绑定/释放上述四个入口函数。*/


#endif
