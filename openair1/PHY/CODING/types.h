/*******************************************************************************
 Copyright (c) 2009-2018, Intel Corporation

 Redistribution and use in source and binary forms, with or without
 modification, are permitted provided that the following conditions are met:

     * Redistributions of source code must retain the above copyright notice,
       this list of conditions and the following disclaimer.
     * Redistributions in binary form must reproduce the above copyright
       notice, this list of conditions and the following disclaimer in the
       documentation and/or other materials provided with the distribution.
     * Neither the name of Intel Corporation nor the names of its contributors
       may be used to endorse or promote products derived from this software
       without specific prior written permission.

 THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE
 FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
 CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*******************************************************************************/

/* 通用数据类型、宏和函数定义
 * 作用：为整个CODING模块提供统一的数据类型和工具函数
 * 使用范围：CRC计算、LDPC编解码、Polar编码等所有编码模块 */

#ifndef CRC_TYPES_H_
#define CRC_TYPES_H_

/* 环境适配：内核态和用户态的头文件包含 */
#ifdef __KERNEL__
#include <asm/i387.h>
#include <linux/types.h>
/* 内核态下定义快速整数类型 */
typedef uint32_t uint_fast32_t;
typedef uint16_t uint_fast16_t;
typedef uint8_t uint_fast8_t;
#else
/* 用户态下使用标准C库 */
#include <stdint.h>
#include <stddef.h>
#endif

/* 内存对齐宏：确保变量按指定边界对齐
 * 用途：SIMD指令优化（AVX512需要64字节对齐，AVX2需要32字节对齐）
 * 示例：DECLARE_ALIGNED(uint8_t buffer[1024], 64); */
#define DECLARE_ALIGNED(_declaration, _boundary)         \
        _declaration __attribute__((aligned(_boundary)))

/* 强制内联宏：确保函数被内联以提高性能
 * 调试模式：不内联，便于调试
 * 发布模式：强制内联，提高性能 */
#ifndef DEBUG
#define __forceinline                                   \
        static inline __attribute__((always_inline))
#else
#define __forceinline                                   \
        static
#endif

/* 分支预测提示：帮助编译器优化分支预测
 * likely(x)：表示条件x很可能为真
 * unlikely(x)：表示条件x很可能为假
 * 用于热点代码路径优化 */
#define likely(x)   __builtin_expect(!!(x), 1)
#define unlikely(x) __builtin_expect(!!(x), 0)

/* 数组维度计算宏：获取静态数组的元素个数
 * 用途：遍历静态数组时避免硬编码长度 */
#ifndef DIM
#define DIM(x) (sizeof(x)/sizeof(x[0]))
#endif

/**
 * 通用工具函数
 * 作用：提供字节序转换等基础功能
 * 使用场景：网络字节序转换、大小端处理等
 */

/**
 * @brief 16位字节序交换
 * 功能：将16位数据的高低字节交换
 * 用途：大小端转换、网络字节序处理
 * 示例：0x1234 -> 0x3412
 *
 * @param val 16位输入数据
 * @return 字节序交换后的16位数据
 */
__forceinline uint16_t bswap2(const uint16_t val)
{
        return (uint16_t) ((val >> 8) | (val << 8));
}

/**
 * @brief 32位字节序交换
 * 功能：将32位数据的字节顺序完全颠倒
 * 用途：大小端转换、网络协议处理
 * 示例：0x12345678 -> 0x78563412 (ABCD -> DCBA)
 *
 * @param val 32位输入数据
 * @return 字节序交换后的32位数据
 */
__forceinline uint32_t bswap4(const uint32_t val)
{
        return ((val >> 24) |             /**< A字节移到最低位*/
                ((val & 0xff0000) >> 8) | /**< B字节右移8位*/
                ((val & 0xff00) << 8) |   /**< C字节左移8位*/
                (val << 24));             /**< D字节移到最高位*/
}

#endif /* CRC_TYPES_H_ */
