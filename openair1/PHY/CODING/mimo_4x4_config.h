/* SPDX-License-Identifier: BSD-3-Clause
 * Copyright(c) 2024 OpenAirInterface
 */

/*! \file PHY/CODING/mimo_4x4_config.h
 * \brief 4×4 MIMO 配置参数和常量定义
 * 
 * 作用：为4×4 MIMO升级提供统一的配置参数和常量定义
 * 使用范围：LDPC编解码、资源分配、缓冲区管理等所有相关模块
 * 
 * 主要功能：
 * 1. 定义4×4 MIMO的关键参数和限制
 * 2. 提供缓冲区大小计算宏
 * 3. 设置性能优化参数
 * 4. 定义调试和监控接口
 */

#ifndef MIMO_4X4_CONFIG_H_
#define MIMO_4X4_CONFIG_H_

#include <stdint.h>
#include <stdbool.h>

/* 4×4 MIMO基本参数 */
#define MAX_MIMO_LAYERS_4X4         4       // 最大MIMO层数
#define MAX_MIMO_LAYERS_2X2         2       // 2×2 MIMO层数（向后兼容）
#define DEFAULT_MIMO_LAYERS         2       // 默认MIMO层数

/* 4×4 MIMO缓冲区缩放因子 */
#define MIMO_4X4_BUFFER_SCALE       2       // 4×4相对于2×2的缓冲区缩放因子
#define MIMO_4X4_THREAD_SCALE       2       // 4×4相对于2×2的线程数缩放因子
#define MIMO_4X4_QUEUE_SCALE        2       // 4×4相对于2×2的队列数缩放因子

/* 4×4 MIMO性能优化参数 */
#define MIMO_4X4_SEGMENTS_PER_TASK  4       // 每个任务处理的段数（原来是8）
#define MIMO_4X4_MAX_TASKS          64      // 最大任务数
#define MIMO_4X4_CACHE_LINE_SIZE    64      // 缓存行大小（字节）
#define MIMO_4X4_SIMD_ALIGNMENT     64      // SIMD对齐要求（字节）

/* 4×4 MIMO缓冲区大小限制 */
#define MIMO_4X4_MAX_BUFFER_SIZE    (16UL << 20)    // 16MB最大缓冲区
#define MIMO_4X4_STACK_THRESHOLD    (256UL << 10)   // 256KB栈分配阈值
#define MIMO_4X4_HEAP_THRESHOLD     (1UL << 20)     // 1MB堆分配阈值

/* 4×4 MIMO硬件加速参数 */
#define MIMO_4X4_T2_QUEUES          4       // T2硬件队列数
#define MIMO_4X4_T2_OPS_SCALE       4       // T2操作数缩放因子
#define MIMO_4X4_T2_POOL_SCALE      2       // T2内存池缩放因子

/* 4×4 MIMO配置结构 */
typedef struct {
    uint8_t max_layers;                     // 最大层数（1,2,4）
    uint8_t default_layers;                 // 默认层数
    bool enable_4x4;                        // 是否启用4×4 MIMO
    bool use_heap_buffers;                  // 是否使用堆分配大缓冲区
    bool enable_simd_optimization;          // 是否启用SIMD优化
    uint32_t buffer_scale_factor;           // 缓冲区缩放因子
    uint32_t thread_scale_factor;           // 线程数缩放因子
    uint32_t max_buffer_size;               // 最大缓冲区大小
    uint32_t stack_threshold;               // 栈分配阈值
} mimo_4x4_config_t;

/* 4×4 MIMO性能统计结构 */
typedef struct {
    uint64_t total_4x4_operations;          // 4×4操作总数
    uint64_t total_4x4_bytes_processed;     // 4×4处理的总字节数
    uint64_t heap_allocations;              // 堆分配次数
    uint64_t stack_allocations;             // 栈分配次数
    double avg_4x4_throughput_mbps;         // 平均4×4吞吐量（Mbps）
    double avg_4x4_latency_us;              // 平均4×4延迟（微秒）
} mimo_4x4_stats_t;

/* 4×4 MIMO缓冲区大小计算宏 */
#define MIMO_4X4_CALC_BUFFER_SIZE(base_size, layers) \
    ((base_size) * (layers) / DEFAULT_MIMO_LAYERS)

#define MIMO_4X4_CALC_G_SIZE(nb_rb, nb_symb, qm, layers) \
    ((nb_rb) * (nb_symb) * 12 * (qm) * (layers))

#define MIMO_4X4_CALC_E_SIZE(G, C, qm, layers) \
    ((G) / ((C) * (qm) * (layers)))

/* 4×4 MIMO对齐宏 */
#define MIMO_4X4_ALIGN_SIZE(size, alignment) \
    (((size) + (alignment) - 1) & ~((alignment) - 1))

#define MIMO_4X4_IS_ALIGNED(ptr, alignment) \
    (((uintptr_t)(ptr) & ((alignment) - 1)) == 0)

/* 4×4 MIMO条件检查宏 */
#define MIMO_4X4_IS_ENABLED(layers) \
    ((layers) == 4)

#define MIMO_4X4_NEEDS_LARGE_BUFFER(size) \
    ((size) > MIMO_4X4_STACK_THRESHOLD)

#define MIMO_4X4_VALIDATE_LAYERS(layers) \
    ((layers) > 0 && (layers) <= MAX_MIMO_LAYERS_4X4 && \
     ((layers) == 1 || (layers) == 2 || (layers) == 4))

/* 4×4 MIMO调试宏 */
#ifdef DEBUG_MIMO_4X4
#define MIMO_4X4_LOG_D(fmt, ...) \
    printf("[MIMO_4X4_DEBUG] " fmt "\n", ##__VA_ARGS__)
#define MIMO_4X4_LOG_I(fmt, ...) \
    printf("[MIMO_4X4_INFO] " fmt "\n", ##__VA_ARGS__)
#define MIMO_4X4_LOG_W(fmt, ...) \
    printf("[MIMO_4X4_WARN] " fmt "\n", ##__VA_ARGS__)
#define MIMO_4X4_LOG_E(fmt, ...) \
    printf("[MIMO_4X4_ERROR] " fmt "\n", ##__VA_ARGS__)
#else
#define MIMO_4X4_LOG_D(fmt, ...)
#define MIMO_4X4_LOG_I(fmt, ...)
#define MIMO_4X4_LOG_W(fmt, ...)
#define MIMO_4X4_LOG_E(fmt, ...)
#endif

/* 全局4×4 MIMO配置实例 */
extern mimo_4x4_config_t g_mimo_4x4_config;
extern mimo_4x4_stats_t g_mimo_4x4_stats;

/* 4×4 MIMO配置函数声明 */
void mimo_4x4_config_init(mimo_4x4_config_t *config);
void mimo_4x4_config_set_defaults(mimo_4x4_config_t *config);
bool mimo_4x4_config_validate(const mimo_4x4_config_t *config);
void mimo_4x4_stats_init(mimo_4x4_stats_t *stats);
void mimo_4x4_stats_update(mimo_4x4_stats_t *stats, uint32_t bytes, double latency_us);
void mimo_4x4_stats_print(const mimo_4x4_stats_t *stats);

/* 4×4 MIMO内存管理函数声明 */
void* mimo_4x4_aligned_alloc(size_t size, size_t alignment);
void mimo_4x4_aligned_free(void *ptr);
bool mimo_4x4_should_use_heap(size_t size);

#endif /* MIMO_4X4_CONFIG_H_ */
