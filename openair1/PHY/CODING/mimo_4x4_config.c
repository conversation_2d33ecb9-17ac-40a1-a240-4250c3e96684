/* SPDX-License-Identifier: BSD-3-Clause
 * Copyright(c) 2024 OpenAirInterface
 */

/*! \file PHY/CODING/mimo_4x4_config.c
 * \brief 4×4 MIMO 配置参数和工具函数实现
 * 
 * 作用：实现4×4 MIMO的配置管理和工具函数
 * 使用范围：整个4×4 MIMO编解码系统
 * 
 * 主要功能：
 * 1. 配置参数的初始化和验证
 * 2. 性能统计的管理和更新
 * 3. 内存管理的优化函数
 * 4. 调试和监控工具
 */

#include "mimo_4x4_config.h"
#include <stdlib.h>
#include <string.h>
#include <stdio.h>
#include <assert.h>

/* 全局4×4 MIMO配置实例 */
mimo_4x4_config_t g_mimo_4x4_config = {0};
mimo_4x4_stats_t g_mimo_4x4_stats = {0};

/* 函数：mimo_4x4_config_init - 初始化4×4 MIMO配置
 * 作用：设置4×4 MIMO的默认配置参数
 * 调用位置：系统初始化时调用
 */
void mimo_4x4_config_init(mimo_4x4_config_t *config)
{
    if (config == NULL) return;
    
    memset(config, 0, sizeof(mimo_4x4_config_t));
    mimo_4x4_config_set_defaults(config);
}

/* 函数：mimo_4x4_config_set_defaults - 设置默认配置
 * 作用：为4×4 MIMO配置设置合理的默认值
 */
void mimo_4x4_config_set_defaults(mimo_4x4_config_t *config)
{
    if (config == NULL) return;
    
    config->max_layers = MAX_MIMO_LAYERS_4X4;
    config->default_layers = DEFAULT_MIMO_LAYERS;
    config->enable_4x4 = true;
    config->use_heap_buffers = true;
    config->enable_simd_optimization = true;
    config->buffer_scale_factor = MIMO_4X4_BUFFER_SCALE;
    config->thread_scale_factor = MIMO_4X4_THREAD_SCALE;
    config->max_buffer_size = MIMO_4X4_MAX_BUFFER_SIZE;
    config->stack_threshold = MIMO_4X4_STACK_THRESHOLD;
}

/* 函数：mimo_4x4_config_validate - 验证配置参数
 * 作用：检查4×4 MIMO配置参数的有效性
 * 返回值：true表示配置有效，false表示配置无效
 */
bool mimo_4x4_config_validate(const mimo_4x4_config_t *config)
{
    if (config == NULL) return false;
    
    /* 检查层数参数 */
    if (!MIMO_4X4_VALIDATE_LAYERS(config->max_layers)) {
        MIMO_4X4_LOG_E("Invalid max_layers: %d", config->max_layers);
        return false;
    }
    
    if (!MIMO_4X4_VALIDATE_LAYERS(config->default_layers)) {
        MIMO_4X4_LOG_E("Invalid default_layers: %d", config->default_layers);
        return false;
    }
    
    /* 检查缩放因子 */
    if (config->buffer_scale_factor == 0 || config->buffer_scale_factor > 8) {
        MIMO_4X4_LOG_E("Invalid buffer_scale_factor: %u", config->buffer_scale_factor);
        return false;
    }
    
    if (config->thread_scale_factor == 0 || config->thread_scale_factor > 8) {
        MIMO_4X4_LOG_E("Invalid thread_scale_factor: %u", config->thread_scale_factor);
        return false;
    }
    
    /* 检查缓冲区大小限制 */
    if (config->max_buffer_size < (1UL << 20)) {  // 至少1MB
        MIMO_4X4_LOG_E("max_buffer_size too small: %u", config->max_buffer_size);
        return false;
    }
    
    if (config->stack_threshold > config->max_buffer_size) {
        MIMO_4X4_LOG_E("stack_threshold exceeds max_buffer_size");
        return false;
    }
    
    return true;
}

/* 函数：mimo_4x4_stats_init - 初始化性能统计
 * 作用：重置4×4 MIMO性能统计计数器
 */
void mimo_4x4_stats_init(mimo_4x4_stats_t *stats)
{
    if (stats == NULL) return;
    
    memset(stats, 0, sizeof(mimo_4x4_stats_t));
}

/* 函数：mimo_4x4_stats_update - 更新性能统计
 * 作用：更新4×4 MIMO的性能统计信息
 * 参数：
 * @stats: 统计结构指针
 * @bytes: 处理的字节数
 * @latency_us: 处理延迟（微秒）
 */
void mimo_4x4_stats_update(mimo_4x4_stats_t *stats, uint32_t bytes, double latency_us)
{
    if (stats == NULL) return;
    
    stats->total_4x4_operations++;
    stats->total_4x4_bytes_processed += bytes;
    
    /* 计算移动平均吞吐量 */
    if (latency_us > 0) {
        double throughput_mbps = (bytes * 8.0) / latency_us;  // Mbps
        if (stats->avg_4x4_throughput_mbps == 0) {
            stats->avg_4x4_throughput_mbps = throughput_mbps;
        } else {
            stats->avg_4x4_throughput_mbps = 
                (stats->avg_4x4_throughput_mbps * 0.9) + (throughput_mbps * 0.1);
        }
    }
    
    /* 计算移动平均延迟 */
    if (stats->avg_4x4_latency_us == 0) {
        stats->avg_4x4_latency_us = latency_us;
    } else {
        stats->avg_4x4_latency_us = 
            (stats->avg_4x4_latency_us * 0.9) + (latency_us * 0.1);
    }
}

/* 函数：mimo_4x4_stats_print - 打印性能统计
 * 作用：输出4×4 MIMO的性能统计信息
 */
void mimo_4x4_stats_print(const mimo_4x4_stats_t *stats)
{
    if (stats == NULL) return;
    
    printf("\n=== 4×4 MIMO Performance Statistics ===\n");
    printf("Total Operations: %lu\n", stats->total_4x4_operations);
    printf("Total Bytes Processed: %lu (%.2f MB)\n", 
           stats->total_4x4_bytes_processed,
           stats->total_4x4_bytes_processed / (1024.0 * 1024.0));
    printf("Heap Allocations: %lu\n", stats->heap_allocations);
    printf("Stack Allocations: %lu\n", stats->stack_allocations);
    printf("Average Throughput: %.2f Mbps\n", stats->avg_4x4_throughput_mbps);
    printf("Average Latency: %.2f us\n", stats->avg_4x4_latency_us);
    printf("=======================================\n\n");
}

/* 函数：mimo_4x4_aligned_alloc - 对齐内存分配
 * 作用：分配指定对齐要求的内存
 * 参数：
 * @size: 分配大小
 * @alignment: 对齐要求
 * 返回值：分配的内存指针，失败返回NULL
 */
void* mimo_4x4_aligned_alloc(size_t size, size_t alignment)
{
    void *ptr = NULL;
    
    /* 参数检查 */
    if (size == 0 || alignment == 0) return NULL;
    if ((alignment & (alignment - 1)) != 0) return NULL;  // 检查是否为2的幂
    
    /* 使用系统的aligned_alloc函数 */
    ptr = aligned_alloc(alignment, MIMO_4X4_ALIGN_SIZE(size, alignment));
    
    if (ptr != NULL) {
        g_mimo_4x4_stats.heap_allocations++;
        MIMO_4X4_LOG_D("Allocated %zu bytes with %zu alignment at %p", 
                       size, alignment, ptr);
    } else {
        MIMO_4X4_LOG_E("Failed to allocate %zu bytes with %zu alignment", 
                       size, alignment);
    }
    
    return ptr;
}

/* 函数：mimo_4x4_aligned_free - 释放对齐内存
 * 作用：释放通过mimo_4x4_aligned_alloc分配的内存
 */
void mimo_4x4_aligned_free(void *ptr)
{
    if (ptr != NULL) {
        free(ptr);
        MIMO_4X4_LOG_D("Freed aligned memory at %p", ptr);
    }
}

/* 函数：mimo_4x4_should_use_heap - 判断是否应该使用堆分配
 * 作用：根据大小判断是否应该使用堆而不是栈分配
 * 参数：
 * @size: 需要分配的大小
 * 返回值：true表示应该使用堆分配，false表示可以使用栈分配
 */
bool mimo_4x4_should_use_heap(size_t size)
{
    return MIMO_4X4_NEEDS_LARGE_BUFFER(size) || 
           !g_mimo_4x4_config.use_heap_buffers;
}
